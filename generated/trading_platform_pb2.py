# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: trading_platform.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'trading_platform.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16trading_platform.proto\x12\x0ftradingplatform\"7\n\x10UserRegistration\x12\x0f\n\x07\x61pi_key\x18\x01 \x01(\t\x12\x12\n\naccount_id\x18\x02 \x01(\t\"\"\n\x0fResponseMessage\x12\x0f\n\x07message\x18\x01 \x01(\t\"$\n\x0e\x41\x63\x63ountRequest\x12\x12\n\naccount_id\x18\x01 \x01(\t\"\x7f\n\x0e\x41\x63\x63ountSummary\x12\x0f\n\x07\x62\x61lance\x18\x01 \x01(\t\x12\x18\n\x10margin_available\x18\x02 \x01(\t\x12\x13\n\x0bmargin_used\x18\x03 \x01(\t\x12\x18\n\x10open_trade_count\x18\x04 \x01(\x05\x12\x13\n\x0bprofit_loss\x18\x05 \x01(\t\"4\n\nOpenTrades\x12&\n\x06trades\x18\x01 \x03(\x0b\x32\x16.tradingplatform.Trade\"\x91\x01\n\x05Trade\x12\x10\n\x08trade_id\x18\x01 \x01(\t\x12\x12\n\ninstrument\x18\x02 \x01(\t\x12\r\n\x05units\x18\x03 \x01(\t\x12\x12\n\nopen_price\x18\x04 \x01(\t\x12\x15\n\rcurrent_price\x18\x05 \x01(\t\x12\x13\n\x0bprofit_loss\x18\x06 \x01(\t\x12\x13\n\x0btime_opened\x18\x07 \x01(\t\"}\n\x0fStrategyRequest\x12\x12\n\ntimeframes\x18\x01 \x03(\t\x12\x17\n\x0fmoving_averages\x18\x02 \x03(\x05\x12\x15\n\ruse_fibonacci\x18\x03 \x01(\x08\x12\x13\n\x0btake_profit\x18\x04 \x01(\x02\x12\x11\n\tstop_loss\x18\x05 \x01(\x02\")\n\x10StrategyResponse\x12\x15\n\rstrategy_json\x18\x01 \x01(\t\"_\n\x15TradeExecutionRequest\x12\x12\n\naccount_id\x18\x01 \x01(\t\x12\x32\n\x08strategy\x18\x02 \x01(\x0b\x32 .tradingplatform.StrategyRequest\"K\n\x16TradeExecutionResponse\x12\x10\n\x08trade_id\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0f\n\x07message\x18\x03 \x01(\t2\xc6\x03\n\x0fTradingPlatform\x12S\n\x0cRegisterUser\x12!.tradingplatform.UserRegistration\x1a .tradingplatform.ResponseMessage\x12U\n\x11GetAccountSummary\x12\x1f.tradingplatform.AccountRequest\x1a\x1f.tradingplatform.AccountSummary\x12M\n\rGetOpenTrades\x12\x1f.tradingplatform.AccountRequest\x1a\x1b.tradingplatform.OpenTrades\x12W\n\x10GenerateStrategy\x12 .tradingplatform.StrategyRequest\x1a!.tradingplatform.StrategyResponse\x12_\n\x0c\x45xecuteTrade\x12&.tradingplatform.TradeExecutionRequest\x1a\'.tradingplatform.TradeExecutionResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'trading_platform_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_USERREGISTRATION']._serialized_start=43
  _globals['_USERREGISTRATION']._serialized_end=98
  _globals['_RESPONSEMESSAGE']._serialized_start=100
  _globals['_RESPONSEMESSAGE']._serialized_end=134
  _globals['_ACCOUNTREQUEST']._serialized_start=136
  _globals['_ACCOUNTREQUEST']._serialized_end=172
  _globals['_ACCOUNTSUMMARY']._serialized_start=174
  _globals['_ACCOUNTSUMMARY']._serialized_end=301
  _globals['_OPENTRADES']._serialized_start=303
  _globals['_OPENTRADES']._serialized_end=355
  _globals['_TRADE']._serialized_start=358
  _globals['_TRADE']._serialized_end=503
  _globals['_STRATEGYREQUEST']._serialized_start=505
  _globals['_STRATEGYREQUEST']._serialized_end=630
  _globals['_STRATEGYRESPONSE']._serialized_start=632
  _globals['_STRATEGYRESPONSE']._serialized_end=673
  _globals['_TRADEEXECUTIONREQUEST']._serialized_start=675
  _globals['_TRADEEXECUTIONREQUEST']._serialized_end=770
  _globals['_TRADEEXECUTIONRESPONSE']._serialized_start=772
  _globals['_TRADEEXECUTIONRESPONSE']._serialized_end=847
  _globals['_TRADINGPLATFORM']._serialized_start=850
  _globals['_TRADINGPLATFORM']._serialized_end=1304
# @@protoc_insertion_point(module_scope)
