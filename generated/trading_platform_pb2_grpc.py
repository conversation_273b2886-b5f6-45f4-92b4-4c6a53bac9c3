# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import trading_platform_pb2 as trading__platform__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in trading_platform_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class TradingPlatformStub(object):
    """Service Definition
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.RegisterUser = channel.unary_unary(
                '/tradingplatform.TradingPlatform/RegisterUser',
                request_serializer=trading__platform__pb2.UserRegistration.SerializeToString,
                response_deserializer=trading__platform__pb2.ResponseMessage.FromString,
                _registered_method=True)
        self.GetAccountSummary = channel.unary_unary(
                '/tradingplatform.TradingPlatform/GetAccountSummary',
                request_serializer=trading__platform__pb2.AccountRequest.SerializeToString,
                response_deserializer=trading__platform__pb2.AccountSummary.FromString,
                _registered_method=True)
        self.GetOpenTrades = channel.unary_unary(
                '/tradingplatform.TradingPlatform/GetOpenTrades',
                request_serializer=trading__platform__pb2.AccountRequest.SerializeToString,
                response_deserializer=trading__platform__pb2.OpenTrades.FromString,
                _registered_method=True)
        self.GenerateStrategy = channel.unary_unary(
                '/tradingplatform.TradingPlatform/GenerateStrategy',
                request_serializer=trading__platform__pb2.StrategyRequest.SerializeToString,
                response_deserializer=trading__platform__pb2.StrategyResponse.FromString,
                _registered_method=True)
        self.ExecuteTrade = channel.unary_unary(
                '/tradingplatform.TradingPlatform/ExecuteTrade',
                request_serializer=trading__platform__pb2.TradeExecutionRequest.SerializeToString,
                response_deserializer=trading__platform__pb2.TradeExecutionResponse.FromString,
                _registered_method=True)


class TradingPlatformServicer(object):
    """Service Definition
    """

    def RegisterUser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAccountSummary(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOpenTrades(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateStrategy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ExecuteTrade(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TradingPlatformServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'RegisterUser': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterUser,
                    request_deserializer=trading__platform__pb2.UserRegistration.FromString,
                    response_serializer=trading__platform__pb2.ResponseMessage.SerializeToString,
            ),
            'GetAccountSummary': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAccountSummary,
                    request_deserializer=trading__platform__pb2.AccountRequest.FromString,
                    response_serializer=trading__platform__pb2.AccountSummary.SerializeToString,
            ),
            'GetOpenTrades': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOpenTrades,
                    request_deserializer=trading__platform__pb2.AccountRequest.FromString,
                    response_serializer=trading__platform__pb2.OpenTrades.SerializeToString,
            ),
            'GenerateStrategy': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateStrategy,
                    request_deserializer=trading__platform__pb2.StrategyRequest.FromString,
                    response_serializer=trading__platform__pb2.StrategyResponse.SerializeToString,
            ),
            'ExecuteTrade': grpc.unary_unary_rpc_method_handler(
                    servicer.ExecuteTrade,
                    request_deserializer=trading__platform__pb2.TradeExecutionRequest.FromString,
                    response_serializer=trading__platform__pb2.TradeExecutionResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'tradingplatform.TradingPlatform', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('tradingplatform.TradingPlatform', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class TradingPlatform(object):
    """Service Definition
    """

    @staticmethod
    def RegisterUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/tradingplatform.TradingPlatform/RegisterUser',
            trading__platform__pb2.UserRegistration.SerializeToString,
            trading__platform__pb2.ResponseMessage.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAccountSummary(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/tradingplatform.TradingPlatform/GetAccountSummary',
            trading__platform__pb2.AccountRequest.SerializeToString,
            trading__platform__pb2.AccountSummary.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetOpenTrades(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/tradingplatform.TradingPlatform/GetOpenTrades',
            trading__platform__pb2.AccountRequest.SerializeToString,
            trading__platform__pb2.OpenTrades.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GenerateStrategy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/tradingplatform.TradingPlatform/GenerateStrategy',
            trading__platform__pb2.StrategyRequest.SerializeToString,
            trading__platform__pb2.StrategyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ExecuteTrade(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/tradingplatform.TradingPlatform/ExecuteTrade',
            trading__platform__pb2.TradeExecutionRequest.SerializeToString,
            trading__platform__pb2.TradeExecutionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
