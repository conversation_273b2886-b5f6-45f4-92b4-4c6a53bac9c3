/**
 * React hook for managing Polygon.io WebSocket connections
 * Provides real-time forex data streaming with automatic reconnection
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { WEBSOCKET_URL } from '../config';

const usePolygonWebSocket = (options = {}) => {
  const {
    url = WEBSOCKET_URL,
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectDelay = 3000,
    onMessage = null,
    onError = null,
    onConnect = null,
    onDisconnect = null
  } = options;

  // State
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [lastMessage, setLastMessage] = useState(null);
  const [subscriptions, setSubscriptions] = useState(new Set());
  const [error, setError] = useState(null);

  // Refs
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectCountRef = useRef(0);
  const sessionIdRef = useRef(null);

  // Clear reconnection timeout
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return;
    }

    console.log(`🔌 Connecting to WebSocket: ${url}`);
    setConnectionStatus('connecting');
    setError(null);

    try {
      const ws = new WebSocket(url);
      wsRef.current = ws;

      ws.onopen = (event) => {
        console.log('✅ WebSocket connected');
        setConnectionStatus('connected');
        reconnectCountRef.current = 0;
        clearReconnectTimeout();
        
        if (onConnect) {
          onConnect(event);
        }
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          setLastMessage(data);

          // Handle different message types
          switch (data.type) {
            case 'connection':
              console.log(`📡 ${data.message}`);
              sessionIdRef.current = data.session_id;
              break;
            
            case 'subscription_confirmed':
              console.log(`✅ Subscribed to ${data.forex_pair} ${data.timeframe}`);
              break;
            
            case 'candle_update':
              console.log(`📊 Candle update: ${data.data.symbol} - ${data.data.close}`);
              break;
            
            case 'error':
              console.error(`❌ WebSocket error: ${data.message}`);
              setError(data.message);
              break;
            
            case 'pong':
              console.log('🏓 Pong received');
              break;
            
            default:
              console.log('📨 Unknown message type:', data.type);
          }

          if (onMessage) {
            onMessage(data);
          }
        } catch (err) {
          console.error('Error parsing WebSocket message:', err);
          setError('Failed to parse message');
        }
      };

      ws.onclose = (event) => {
        console.log(`🔌 WebSocket disconnected: ${event.code} - ${event.reason}`);
        setConnectionStatus('disconnected');
        wsRef.current = null;

        if (onDisconnect) {
          onDisconnect(event);
        }

        // Attempt reconnection if not a clean close
        if (event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
          attemptReconnect();
        }
      };

      ws.onerror = (event) => {
        console.error('❌ WebSocket error:', event);
        setError('WebSocket connection error');
        setConnectionStatus('error');

        if (onError) {
          onError(event);
        }
      };

    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setError('Failed to create connection');
      setConnectionStatus('error');
    }
  }, [url, onConnect, onMessage, onDisconnect, onError, reconnectAttempts, clearReconnectTimeout]);

  // Attempt reconnection
  const attemptReconnect = useCallback(() => {
    if (reconnectCountRef.current >= reconnectAttempts) {
      console.log('❌ Max reconnection attempts reached');
      setConnectionStatus('failed');
      return;
    }

    reconnectCountRef.current += 1;
    const delay = reconnectDelay * Math.pow(2, reconnectCountRef.current - 1); // Exponential backoff
    
    console.log(`🔄 Reconnecting in ${delay}ms (attempt ${reconnectCountRef.current}/${reconnectAttempts})`);
    setConnectionStatus('reconnecting');

    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  }, [connect, reconnectAttempts, reconnectDelay]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting WebSocket');
    clearReconnectTimeout();
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }
    
    setConnectionStatus('disconnected');
    setSubscriptions(new Set());
    sessionIdRef.current = null;
  }, [clearReconnectTimeout]);

  // Send message to WebSocket
  const sendMessage = useCallback((message) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
      wsRef.current.send(messageStr);
      return true;
    } else {
      console.warn('WebSocket not connected, cannot send message');
      return false;
    }
  }, []);

  // Subscribe to forex pair
  const subscribe = useCallback((forexPair, timeframe = '1m') => {
    const subscriptionKey = `${forexPair}_${timeframe}`;
    
    if (subscriptions.has(subscriptionKey)) {
      console.log(`Already subscribed to ${subscriptionKey}`);
      return;
    }

    const message = {
      type: 'subscribe',
      forex_pair: forexPair,
      timeframe: timeframe
    };

    if (sendMessage(message)) {
      setSubscriptions(prev => new Set(prev).add(subscriptionKey));
      console.log(`📡 Subscribing to ${subscriptionKey}`);
    }
  }, [sendMessage, subscriptions]);

  // Unsubscribe from forex pair
  const unsubscribe = useCallback((forexPair, timeframe = '1m') => {
    const subscriptionKey = `${forexPair}_${timeframe}`;
    
    if (!subscriptions.has(subscriptionKey)) {
      console.log(`Not subscribed to ${subscriptionKey}`);
      return;
    }

    const message = {
      type: 'unsubscribe',
      forex_pair: forexPair,
      timeframe: timeframe
    };

    if (sendMessage(message)) {
      setSubscriptions(prev => {
        const newSet = new Set(prev);
        newSet.delete(subscriptionKey);
        return newSet;
      });
      console.log(`📡 Unsubscribing from ${subscriptionKey}`);
    }
  }, [sendMessage, subscriptions]);

  // Send ping
  const ping = useCallback(() => {
    return sendMessage({ type: 'ping' });
  }, [sendMessage]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Cleanup on unmount
    return () => {
      clearReconnectTimeout();
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmount');
      }
    };
  }, [autoConnect, connect, clearReconnectTimeout]);

  // Ping interval to keep connection alive
  useEffect(() => {
    if (connectionStatus === 'connected') {
      const pingInterval = setInterval(() => {
        ping();
      }, 30000); // Ping every 30 seconds

      return () => clearInterval(pingInterval);
    }
  }, [connectionStatus, ping]);

  return {
    // Connection state
    connectionStatus,
    isConnected: connectionStatus === 'connected',
    isConnecting: connectionStatus === 'connecting',
    isReconnecting: connectionStatus === 'reconnecting',
    error,
    
    // Data
    lastMessage,
    subscriptions: Array.from(subscriptions),
    sessionId: sessionIdRef.current,
    
    // Actions
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    sendMessage,
    ping,
    
    // Stats
    reconnectCount: reconnectCountRef.current
  };
};

export default usePolygonWebSocket;
