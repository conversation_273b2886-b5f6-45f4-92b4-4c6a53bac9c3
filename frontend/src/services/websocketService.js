/**
 * WebSocket service for managing real-time forex data
 * Handles data transformation and caching for chart components
 */

class WebSocketService {
  constructor() {
    this.subscribers = new Map(); // Map of forex_pair -> Set of callback functions
    this.candleCache = new Map(); // Map of forex_pair -> latest candle data
    this.isInitialized = false;
  }

  /**
   * Initialize the WebSocket service
   */
  initialize() {
    if (this.isInitialized) {
      console.log('WebSocket service already initialized');
      return;
    }

    console.log('🚀 Initializing WebSocket service');
    this.isInitialized = true;
  }

  /**
   * Subscribe to real-time updates for a forex pair
   * @param {string} forexPair - Forex pair (e.g., 'EURUSD')
   * @param {string} timeframe - Timeframe (e.g., '1m', '5m')
   * @param {function} callback - Callback function to receive updates
   */
  subscribe(forexPair, timeframe, callback) {
    const key = `${forexPair}_${timeframe}`;
    
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    
    this.subscribers.get(key).add(callback);
    console.log(`📡 Subscribed to ${key}, total subscribers: ${this.subscribers.get(key).size}`);
    
    // Return unsubscribe function
    return () => this.unsubscribe(forexPair, timeframe, callback);
  }

  /**
   * Unsubscribe from real-time updates
   * @param {string} forexPair - Forex pair
   * @param {string} timeframe - Timeframe
   * @param {function} callback - Callback function to remove
   */
  unsubscribe(forexPair, timeframe, callback) {
    const key = `${forexPair}_${timeframe}`;
    
    if (this.subscribers.has(key)) {
      this.subscribers.get(key).delete(callback);
      
      if (this.subscribers.get(key).size === 0) {
        this.subscribers.delete(key);
        console.log(`📡 No more subscribers for ${key}, removed from cache`);
      }
    }
  }

  /**
   * Process incoming WebSocket message
   * @param {object} message - WebSocket message
   */
  processMessage(message) {
    switch (message.type) {
      case 'candle_update':
        this.processCandleUpdate(message.data);
        break;
      
      case 'connection':
        console.log(`🔗 WebSocket connection: ${message.message}`);
        break;
      
      case 'subscription_confirmed':
        console.log(`✅ Subscription confirmed: ${message.forex_pair} ${message.timeframe}`);
        break;
      
      case 'error':
        console.error(`❌ WebSocket error: ${message.message}`);
        break;
      
      default:
        console.log('📨 Unhandled message type:', message.type);
    }
  }

  /**
   * Process candle update and notify subscribers
   * @param {object} candleData - Candle data from WebSocket
   */
  processCandleUpdate(candleData) {
    const { symbol, timeframe = '1m' } = candleData;
    const key = `${symbol}_${timeframe}`;
    
    // Transform candle data to chart format
    const transformedCandle = this.transformCandleData(candleData);
    
    // Update cache
    this.candleCache.set(key, transformedCandle);
    
    // Notify subscribers
    if (this.subscribers.has(key)) {
      this.subscribers.get(key).forEach(callback => {
        try {
          callback(transformedCandle);
        } catch (error) {
          console.error('Error in subscriber callback:', error);
        }
      });
    }
    
    console.log(`📊 Processed candle update for ${key}: ${transformedCandle.close}`);
  }

  /**
   * Transform WebSocket candle data to chart format
   * @param {object} candleData - Raw candle data from WebSocket
   * @returns {object} Transformed candle data
   */
  transformCandleData(candleData) {
    return {
      // Chart.js / TradingView format
      time: candleData.timestamp * 1000, // Convert to milliseconds
      open: parseFloat(candleData.open),
      high: parseFloat(candleData.high),
      low: parseFloat(candleData.low),
      close: parseFloat(candleData.close),
      volume: parseFloat(candleData.volume || 0),
      
      // Additional metadata
      symbol: candleData.symbol,
      timeframe: candleData.timeframe,
      vwap: parseFloat(candleData.vwap || 0),
      transactions: parseInt(candleData.transactions || 0),
      isComplete: candleData.is_complete,
      receivedAt: candleData.received_at,
      
      // For compatibility with existing chart code
      timestamp: candleData.timestamp,
      x: candleData.timestamp * 1000,
      y: parseFloat(candleData.close)
    };
  }

  /**
   * Get the latest candle for a forex pair
   * @param {string} forexPair - Forex pair
   * @param {string} timeframe - Timeframe
   * @returns {object|null} Latest candle data or null
   */
  getLatestCandle(forexPair, timeframe = '1m') {
    const key = `${forexPair}_${timeframe}`;
    return this.candleCache.get(key) || null;
  }

  /**
   * Get all cached candles
   * @returns {Map} Map of forex_pair_timeframe -> candle data
   */
  getAllCachedCandles() {
    return new Map(this.candleCache);
  }

  /**
   * Clear cache for a specific forex pair
   * @param {string} forexPair - Forex pair
   * @param {string} timeframe - Timeframe
   */
  clearCache(forexPair, timeframe = '1m') {
    const key = `${forexPair}_${timeframe}`;
    this.candleCache.delete(key);
    console.log(`🗑️ Cleared cache for ${key}`);
  }

  /**
   * Clear all caches
   */
  clearAllCaches() {
    this.candleCache.clear();
    console.log('🗑️ Cleared all candle caches');
  }

  /**
   * Get service statistics
   * @returns {object} Service statistics
   */
  getStats() {
    return {
      subscriberCount: this.subscribers.size,
      cachedCandles: this.candleCache.size,
      subscriptions: Array.from(this.subscribers.keys()),
      isInitialized: this.isInitialized
    };
  }

  /**
   * Format candle data for display
   * @param {object} candle - Candle data
   * @returns {string} Formatted string
   */
  formatCandleForDisplay(candle) {
    if (!candle) return 'No data';
    
    const price = candle.close.toFixed(5);
    const change = candle.close - candle.open;
    const changePercent = ((change / candle.open) * 100).toFixed(2);
    const direction = change >= 0 ? '📈' : '📉';
    
    return `${candle.symbol}: ${price} ${direction} ${changePercent}%`;
  }

  /**
   * Check if a forex pair is being tracked
   * @param {string} forexPair - Forex pair
   * @param {string} timeframe - Timeframe
   * @returns {boolean} True if being tracked
   */
  isTracking(forexPair, timeframe = '1m') {
    const key = `${forexPair}_${timeframe}`;
    return this.subscribers.has(key) && this.subscribers.get(key).size > 0;
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;
