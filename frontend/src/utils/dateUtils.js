// Helper function to calculate date range based on timeframe
export const getDateRange = (timeframe) => {
  const endDate = new Date();
  endDate.setHours(23, 59, 59, 999); // Set to end of day
  const startDate = new Date();
  startDate.setHours(0, 0, 0, 0); // Set to start of day

  // Convert timeframe to hours for comparison
  const timeframeToHours = {
    "1m": 1/60,
    "5m": 5/60,
    "15m": 15/60,
    "30m": 30/60,
    "1h": 1,
    "4h": 4,
    "1d": 24
  };

  const hours = timeframeToHours[timeframe] || 1;

  // Set historical data range based on timeframe
  if (hours <= 1) {
    // For timeframes <= 1 hour, get 1 year of data
    startDate.setFullYear(endDate.getFullYear() - 1);
  } else if (hours === 4) {
    // For 4h timeframe, get 2 years of data
    startDate.setFullYear(endDate.getFullYear() - 2);
  } else if (hours === 24) {
    // For daily timeframe, get 5 years of data
    startDate.setFullYear(endDate.getFullYear() - 5);
  } else {
    // Default to 1 year for any other timeframe
    startDate.setFullYear(endDate.getFullYear() - 1);
  }

  // Format dates for display
  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Calculate chunk size based on timeframe (in days)
  let chunkSize = 30; // Default to 30 days
  if (hours <= 1) {
    chunkSize = 30; // 30 days for hourly or less
  } else if (hours === 4) {
    chunkSize = 60; // 60 days for 4h
  } else if (hours === 24) {
    chunkSize = 90; // 90 days for daily
  }

  return {
    startDate,
    endDate,
    displayRange: `${formatDate(startDate)} to ${formatDate(endDate)}`,
    chunkSize
  };
}; 