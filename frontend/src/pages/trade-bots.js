import { useEffect, useState } from "react";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "../../firebaseConfig";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import {
  collection,
  getDocs,
  updateDoc,
  doc,
  increment,
  setDoc,
} from "firebase/firestore";
import { db } from "../../firebaseConfig";
import { USE_FIREBASE_EMULATOR } from "../config";
import axios from "axios";
import { motion } from "framer-motion";
import Link from "next/link";

// Components
const DashboardLayout = dynamic(() => import("../components/DashboardLayout"), {
  ssr: false,
});

// Firebase Functions URLs
const CONTROL_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://localhost:8080"
  : "https://control-strategy-ihjc6tjxia-uc.a.run.app";

export default function TradeBots() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [firebaseUser, setFirebaseUser] = useState(null);
  const [tradeBots, setTradeBots] = useState([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState(null);

  // Handle authentication
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        router.push("/login");
      } else {
        setIsAuthenticated(true);
        setFirebaseUser(user);
      }
    });
    return () => unsubscribe();
  }, [router]);

  // Fetch trade bots data
  useEffect(() => {
    fetchData();
  }, [firebaseUser?.uid]);

  const fetchData = async () => {
    if (!firebaseUser?.uid) {
      console.log("No user ID found, skipping data fetch");
      return;
    }

    try {
      setLoading(true);
      console.log("Fetching data for user:", firebaseUser.uid);

      // Get an auth token first to ensure auth is properly established
      const idToken = await firebaseUser.getIdToken(true);
      console.log("Successfully refreshed auth token");

      // Check if collections exist and create them if needed
      const userDocRef = doc(db, "users", firebaseUser.uid);
      const submittedStrategiesCollRef = collection(userDocRef, "submittedStrategies");

      // Make sure the user document exists
      await setDoc(userDocRef, { lastAccess: new Date() }, { merge: true });
      console.log("Successfully updated user document");

      // Fetch running bots first to get list of deployed strategy IDs
      const botsSnapshot = await getDocs(submittedStrategiesCollRef);
      console.log("Found submittedStrategies:", botsSnapshot.docs.length);
      botsSnapshot.docs.forEach((doc) => {
        console.log("Strategy data:", doc.id, doc.data());
      });

      const bots = await Promise.all(
        botsSnapshot.docs.map(async (doc) => {
          try {
            const data = doc.data();
            console.log("Processing bot data:", doc.id, data);

            // Fetch trade history
            const tradeHistoryRef = collection(
              db,
              "users",
              firebaseUser.uid,
              "submittedStrategies",
              doc.id,
              "tradeHistory"
            );
            const tradeHistorySnapshot = await getDocs(tradeHistoryRef);
            const trades = tradeHistorySnapshot.docs.map((tradeDoc) =>
              tradeDoc.data()
            );

            // Calculate metrics
            const closedTrades = trades.filter(
              (trade) => trade.status?.toUpperCase() === "CLOSED"
            );
            const winningTrades = closedTrades.filter(
              (trade) => parseFloat(trade.realizedPL) > 0
            );

            // Calculate total P/L: sum of realized P/L from all closed trades + unrealized P/L from open trades
            const totalPnL = trades.reduce((sum, trade) => {
              // For closed trades, add realized P/L
              if (trade.status?.toUpperCase() === "CLOSED") {
                return sum + (parseFloat(trade.realizedPL) || 0);
              } else {
                // For open trades, add unrealized P/L
                return sum + (parseFloat(trade.unrealizedPL) || 0);
              }
            }, 0);

            // Parse strategy JSON if it's a string
            let strategyData = data.strategy_json || data.strategy_data;
            if (typeof strategyData === 'string') {
              try {
                strategyData = JSON.parse(strategyData);
              } catch (e) {
                console.error('Error parsing strategy JSON:', e);
                strategyData = {};
              }
            }

            return {
              id: doc.id,
              strategy_id: data.strategy_id,
              name:
                data.name ||
                data.strategy_data?.name ||
                data.human_readable_rules?.strategy_info?.name ||
                "Unnamed Strategy",
              totalPnL: isNaN(totalPnL) ? 0 : totalPnL,
              winRate:
                closedTrades.length > 0
                  ? (winningTrades.length / closedTrades.length) * 100
                  : 0,
              totalTrades: trades.length || 0,
              status: data.status || "stopped",
              instrument:
                data.instrument ||
                data.strategy_data?.instruments ||
                strategyData?.instruments ||
                data.human_readable_rules?.strategy_info?.instrument ||
                "Unknown",
              timeframe:
                data.timeframe ||
                data.strategy_data?.timeframe ||
                strategyData?.timeframe ||
                data.human_readable_rules?.strategy_info?.timeframe ||
                "Unknown",
              lastHeartbeat: data.last_heartbeat,
              accountBalance: data.accountBalance?.balance || 0,
              deployed: data.deployed || 0,
              strategy_json: strategyData,
              tradingSessions: strategyData?.tradingSession || [],
            };
          } catch (error) {
            console.error(`Error processing bot ${doc.id}:`, error);
            // Return a minimal valid bot object if there's an error
            return {
              id: doc.id,
              strategy_id: doc.data().strategy_id || "unknown",
              name: "Error - " + doc.id.substring(0, 8),
              totalPnL: 0,
              winRate: 0,
              totalTrades: 0,
              status: "unknown",
              instrument: "Unknown",
              timeframe: "Unknown",
              deployed: 0,
              strategy_json: {},
            };
          }
        })
      );

      // Filter out any null values that might have occurred during processing
      const validBots = bots.filter((bot) => bot !== null);
      setTradeBots(validBots);
      console.log("Final tradeBots array:", validBots);
      setError(null);
    } catch (error) {
      console.error("Error fetching data:", error);
      setError("Failed to fetch bots. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const handleControlBot = async (botId, command) => {
    if (!firebaseUser?.uid) return;

    setActionLoading(true);
    try {
      // Get a fresh token each time
      const idToken = await firebaseUser.getIdToken(true);

      const response = await axios.post(
        `${CONTROL_STRATEGY_URL}/control-strategy/${firebaseUser.uid}/${botId}`,
        {
          command: command,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${idToken}`,
          },
        }
      );

      // Update bot status locally
      setTradeBots((prevBots) =>
        prevBots.map((bot) =>
          bot.id === botId ?
          {
            ...bot,
            status: command === "start" ? "running" :
                    command === "pause" ? "paused" :
                    command === "resume" ? "running" :
                    command === "stop" ? "stopped" : bot.status
          } : bot
        )
      );

      // Clear any previous errors
      setError(null);
    } catch (err) {
      console.error(`Error ${command}ing bot:`, err);

      // Set a user-friendly error message
      const actionName = {
        start: "starting",
        stop: "stopping",
        pause: "pausing",
        resume: "resuming"
      }[command] || command + "ing";

      setError(`Error ${actionName} bot. Please try again later.`);
      alert(
        err.response?.data?.detail ||
          err.message ||
          `Failed to ${command} bot. Please try again.`
      );
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "running":
        return "bg-green-500";
      case "paused":
        return "bg-yellow-500";
      case "stopped":
        return "bg-gray-500";
      case "error":
        return "bg-red-500";
      case "market_closed":
        return "bg-purple-500";
      case "not_in_session":
        return "bg-[#EFBD3A]";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case "running":
        return "Running";
      case "paused":
        return "Paused";
      case "stopped":
        return "Stopped";
      case "error":
        return "Error";
      case "market_closed":
        return "Market Closed";
      case "not_in_session":
        return "Outside Trading Session";
      default:
        return status ? status.charAt(0).toUpperCase() + status.slice(1) : "Unknown";
    }
  };

  // Empty state component
  const EmptyState = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center py-16 px-4 text-center"
    >
      <div className="bg-[#141516] p-6 rounded-full mb-6">
        <svg
          className="w-16 h-16 text-[#EFBD3A]"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"
          />
        </svg>
      </div>
      <h3 className="text-2xl font-bold text-white mb-2">No Trading Bots Deployed</h3>
      <p className="text-gray-400 max-w-md mb-8">
        You haven't deployed any trading bots yet. Visit the Strategy Library to deploy a bot from your saved strategies.
      </p>
      <Link
        href="/StrategyLibrary"
        className="px-6 py-3 bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-[#0A0B0B] rounded-lg font-medium transition-colors flex items-center"
      >
        <svg
          className="w-5 h-5 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
        Go to Strategy Library
      </Link>
    </motion.div>
  );

  // Bot card component
  const BotCard = ({ bot }) => {
    const instrument = bot.instrument || 'Unknown';
    const timeframe = bot.timeframe || 'Unknown';
    const tradingSessions = bot.tradingSessions || [];

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-[#0F1011] rounded-lg overflow-hidden shadow-lg border border-[#1a1a1a] hover:border-[#2a2a2a] transition-all"
      >
        <div className="p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-xl font-semibold text-white mb-1 truncate">{bot.name}</h3>
              <div className="flex items-center text-sm text-gray-400">
                <span>{instrument}</span>
                <span className="mx-2">•</span>
                <span>{timeframe}</span>
                {tradingSessions.length > 0 && (
                  <>
                    <span className="mx-2">•</span>
                    <span>{tradingSessions.join(', ')}</span>
                  </>
                )}
              </div>
            </div>
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(bot.status)} mr-2`}></div>
              <span className="text-sm font-medium text-gray-300">{getStatusText(bot.status)}</span>
            </div>
          </div>

          {/* Performance metrics */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-[#141516] rounded-lg p-3">
              <div className="text-sm text-gray-400 mb-1">Total P&L</div>
              <div className={`text-lg font-bold ${parseFloat(bot.totalPnL) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {parseFloat(bot.totalPnL) >= 0 ? '+' : ''}${bot.totalPnL.toFixed(2)}
              </div>
            </div>
            <div className="bg-[#141516] rounded-lg p-3">
              <div className="text-sm text-gray-400 mb-1">Win Rate</div>
              <div className="text-lg font-bold text-white">
                {bot.winRate.toFixed(1)}%
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex space-x-2">
            <button
              onClick={() => router.push(`/trade-bots/${bot.id}`)}
              className="flex-1 px-4 py-2 bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-[#0A0B0B] rounded-lg font-medium transition-colors"
            >
              View Details
            </button>

            {bot.status === "running" && (
              <button
                onClick={() => handleControlBot(bot.id, "pause")}
                disabled={actionLoading}
                className="px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            )}

            {bot.status === "paused" && (
              <button
                onClick={() => handleControlBot(bot.id, "resume")}
                disabled={actionLoading}
                className="px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            )}

            {(bot.status === "running" || bot.status === "paused") && (
              <button
                onClick={() => handleControlBot(bot.id, "stop")}
                disabled={actionLoading}
                className="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                </svg>
              </button>
            )}

            {bot.status === "stopped" && (
              <button
                onClick={() => handleControlBot(bot.id, "start")}
                disabled={actionLoading}
                className="px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Trading Bots</h1>
            <p className="text-gray-400 mt-1">
              Manage and monitor your deployed trading bots
            </p>
          </div>
          <Link
            href="/StrategyLibrary"
            className="px-4 py-2 bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-[#0A0B0B] rounded-lg font-medium transition-colors flex items-center"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Deploy New Bot
          </Link>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="w-12 h-12 border-4 border-t-[#EFBD3A] border-r-transparent border-b-[#EFBD3A] border-l-transparent rounded-full animate-spin"></div>
          </div>
        ) : error ? (
          <div className="bg-red-900/30 border border-red-500/30 text-red-400 p-6 rounded-lg text-center">
            <svg className="w-12 h-12 mx-auto mb-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 className="text-xl font-bold mb-2">Error</h3>
            <p>{error}</p>
            {error.includes("logged in") ? (
              <button
                onClick={() => router.push('/login')}
                className="mt-4 px-4 py-2 bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-[#0A0B0B] rounded-lg transition-colors"
              >
                Log In
              </button>
            ) : firebaseUser ? (
              <button
                onClick={() => fetchData()}
                className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                Try Again
              </button>
            ) : null}
          </div>
        ) : tradeBots.length === 0 ? (
          <EmptyState />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tradeBots.map((bot) => (
              <BotCard key={bot.id} bot={bot} />
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
