import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import axios from "axios";
import { motion } from "framer-motion";

//   Load Dashboard Layout Dynamically
const DashboardLayout = dynamic(() => import("../components/DashboardLayout"), {
  ssr: false,
});

export default function TradeExecution() {
  const router = useRouter();
  const [userId, setUserId] = useState(null);
  const [selectedPair, setSelectedPair] = useState("EUR/USD");
  const [forexPairs, setForexPairs] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredPairs, setFilteredPairs] = useState([]);
  const [scriptKey, setScriptKey] = useState(Date.now());
  const [chatMessage, setChatMessage] = useState("");
  const [chatHistory, setChatHistory] = useState([]);
  const [aiStrategy, setAIStrategy] = useState(null);
  const [loadingStrategy, setLoadingStrategy] = useState(false);
  const [loadingPineScript, setLoadingPineScript] = useState(false);
  const [loadingBacktest, setLoadingBacktest] = useState(false);
  const [backtestResults, setBacktestResults] = useState(null);
  const tradingViewRef = useRef(null);
  const [backtestId, setBacktestId] = useState(null);
  const [backtestStatus, setBacktestStatus] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState("1h");
  const [selectedTimezone, setSelectedTimezone] = useState("America/New_York");
  const [startingBalance, setStartingBalance] = useState(10000);
  const [showBacktestPair, setShowBacktestPair] = useState(false);

  //   Supported Timeframes by QuantConnect
  const timeframes = [
    "1-Minute",
    "3-Minute",
    "5-Minute",
    "15-Minute",
    "Hour",
    "Daily",
    "Weekly",
    "Monthly",
  ];

  const handleOpenModal = () => setIsModalOpen(true);

  const handleConfirmBacktest = (timeframe, timezone) => {
    setSelectedTimeframe(timeframe);
    setSelectedTimezone(timezone);
    // handleBacktestStrategy(3);
    handleBacktraderBacktestStrategy();
    setIsModalOpen(false);
  };

  useEffect(() => {
    const savedPair = localStorage.getItem("selectedPair");
    if (savedPair) {
      setSelectedPair(savedPair);
    }

    const storedUserId = localStorage.getItem("user_id");
    if (storedUserId) {
      setUserId(storedUserId);
      fetchForexPairs(storedUserId);
    }

    setTimeout(() => setScriptKey(Date.now()), 500);
  }, []);

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredPairs([]);
      return;
    }

    const filtered = forexPairs.filter((pair) =>
      pair.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setFilteredPairs(filtered);
  }, [searchQuery, forexPairs]);

  const fetchForexPairs = async (userId) => {
    try {
      const response = await axios.get(
        `http://localhost:8000/forex/pairs/${userId}`
      );

      if (!response.data || !response.data.forex_pairs) {
        throw new Error("Invalid API response format");
      }

      const pairs = response.data.forex_pairs.map((pair) =>
        pair.replace("_", "/")
      );
      setForexPairs(pairs);
    } catch (error) {
      console.error("Error fetching forex pairs:", error);
    }
  };

  const loadTradingViewWidget = () => {
    if (!tradingViewRef.current) return;

    console.log("🔄 Reloading TradingView widget...");

    tradingViewRef.current.innerHTML = "";

    const script = document.createElement("script");
    script.src =
      "https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js";
    script.async = true;

    const tradingPair = selectedPair.replace("/", "");

    script.innerHTML = JSON.stringify({
      width: "100%",
      height: 500,
      symbol: `OANDA:${tradingPair}`,
      interval: "30",
      timezone: "Etc/UTC",
      theme: "dark",
      style: "1",
      locale: "en",
      enable_publishing: false,
      hide_side_toolbar: false,
      allow_symbol_change: true,
      show_popup_button: true,
      popup_width: "1000",
      popup_height: "650",
    });

    tradingViewRef.current.appendChild(script);
  };

  useEffect(() => {
    loadTradingViewWidget();
  }, [scriptKey]);

  useEffect(() => {
    localStorage.setItem("selectedPair", selectedPair);
    setScriptKey(Date.now());
  }, [selectedPair]);

  //   AI Chatbot Logic (with Loading Indicator & Proper JSON Parsing)
  const handleChatSubmit = async () => {
    if (!chatMessage.trim()) return;

    setChatHistory([...chatHistory, { role: "user", text: chatMessage }]);
    setChatMessage("");
    setLoadingStrategy(true);

    try {
      const response = await axios.post("http://localhost:8000/ai/chatbot", {
        message: chatMessage,
      });

      console.log("  Chatbot Response:", response.data);

      if (response.data && response.data.strategy) {
        setChatHistory([
          ...chatHistory,
          { role: "user", text: chatMessage },
          { role: "bot", text: "  AI Strategy Generated!" },
        ]);
        setAIStrategy(response.data.strategy);
      }
    } catch (error) {
      console.error("❌ Error communicating with chatbot:", error);
      setChatHistory([
        ...chatHistory,
        { role: "bot", text: "❌ Error: Failed to generate strategy." },
      ]);
    } finally {
      setLoadingStrategy(false);
    }
  };

  //   Function to Start the Backtest
  const handleBacktestStrategy = async (retries = 3) => {
    if (!aiStrategy) return;

    setLoadingBacktest(true);
    setBacktestStatus("Starting...");
    console.log(" Starting backtest...");

    try {
      //   Step 1: Generate the QuantConnect Strategy Code & Backtest in One Call
      const response = await axios.post(
        "http://localhost:8000/ai/convert_to_quantconnect",
        {
          strategy: aiStrategy,
          timeframe: selectedTimeframe,
          timezone: selectedTimezone,
          forex_pair: selectedPair,
          starting_balance: startingBalance,
        }
      );

      //   Step 2: Validate API Response
      if (!response.data) {
        console.error("❌ API returned no response.");
        throw new Error("No response from AI server.");
      }

      const { quantconnect_code, backtest_results, error } = response.data;

      //   Step 3: Handle API Errors
      if (error) {
        console.error(`❌ AI Error: ${error}`);
        throw new Error(`AI Error: ${error}`);
      }

      if (!quantconnect_code) {
        console.error("❌ AI did not generate valid QuantConnect code.");
        throw new Error("AI failed to generate a valid strategy.");
      }

      console.log("  QuantConnect Code Generated:", quantconnect_code);

      //   Step 4: Check if the Backtest was Run
      if (!backtest_results) {
        console.error("❌ AI did not return backtest results.");
        throw new Error("Backtest results missing.");
      }

      console.log("  Backtest Results Received:", backtest_results);

      //   Step 5: Update UI with Backtest Results
      setBacktestResults(backtest_results.results);
      setBacktestStatus("Completed ");
      setLoadingBacktest(false);
      return;
    } catch (error) {
      console.error(`❌ Error running backtest: ${error.message}`);
    }

    console.error("❌ All retry attempts failed.");
    setBacktestStatus("Error: Unable to generate a working backtest.");
    alert("❌ Failed to backtest strategy.");
    setLoadingBacktest(false);
  };

  const handleBacktraderBacktestStrategy = async () => {
    if (!aiStrategy) {
      alert("❌ No strategy available to backtest. Please generate one first.");
      return;
    }

    setLoadingBacktest(true);
    setBacktestStatus("Starting Backtest...");

    try {
      const response = await axios.post(
        "http://localhost:8000/backtrader/backtest",
        {
          strategy: aiStrategy,
        }
      );

      if (!response.data || !response.data.performance) {
        throw new Error("Invalid response from backtest service.");
      }

      console.log("✅ Backtest Results:", response.data);

      setBacktestResults(response.data);
      setBacktestStatus("Completed");
    } catch (error) {
      console.error("❌ Backtest Error:", error);
      setBacktestStatus("Failed to backtest strategy.");
    } finally {
      setLoadingBacktest(false);
    }
  };

  return (
    <DashboardLayout>
      <motion.div
        className="p-8 min-h-screen bg-gradient-to-br from-gray-900 to-black text-white flex flex-col items-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <h1 className="text-6xl font-extrabold text-cyan-400 mb-10 tracking-wide">
          Trade Execution
        </h1>

        {/*   Forex Pair Search Bar */}
        <div className="relative w-full max-w-lg mb-6">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search for a forex pair..."
            className="w-full p-4 rounded-lg bg-gray-800 text-white text-lg focus:outline-none focus:ring-4 focus:ring-cyan-500"
          />

          {filteredPairs.length > 0 && (
            <ul className="absolute w-full bg-gray-900 text-white mt-2 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
              {filteredPairs.map((pair) => (
                <li
                  key={pair}
                  onClick={() => {
                    setSelectedPair(pair);
                    setSearchQuery(pair);
                    setFilteredPairs([]);
                  }}
                  className="p-3 cursor-pointer hover:bg-gray-700 transition-all"
                >
                  {pair}
                </li>
              ))}
            </ul>
          )}
        </div>

        {/*   TradingView Chart */}
        <div className="w-full max-w-5xl bg-gray-900 p-4 rounded-xl shadow-xl border border-cyan-500">
          <h2 className="text-3xl font-bold mb-4 text-cyan-300">
            Live Chart: {selectedPair}
          </h2>
          <div
            key={scriptKey}
            ref={tradingViewRef}
            className="w-full"
            style={{ height: "500px" }}
          ></div>
        </div>

        {/*   AI Chatbot for Strategy Generation */}
        <div className="bg-gray-900 p-10 rounded-xl shadow-xl border border-purple-500 w-full max-w-4xl mt-10">
          <h2 className="text-4xl font-bold mb-6 text-purple-300">
            Describe Your Strategy
          </h2>
          <textarea
            value={chatMessage}
            onChange={(e) => setChatMessage(e.target.value)}
            placeholder="Describe your trading strategy..."
            className="w-full p-4 text-lg rounded-lg bg-gray-800 text-white focus:outline-none focus:ring-4 focus:ring-purple-500"
          />
          <button
            onClick={handleChatSubmit}
            className="mt-6 px-8 py-4 bg-purple-600 text-white font-semibold rounded-lg shadow-md hover:scale-105 transition transform duration-300"
          >
            Generate Strategy
          </button>

          {loadingStrategy && (
            <p className="mt-4 text-purple-300">⏳ Generating Strategy...</p>
          )}

          {/*   AI Strategy Output */}
          {aiStrategy && (
            <div className="mt-6 p-4 bg-gray-800 rounded-lg text-white text-sm">
              <h3 className="text-xl font-bold mb-2 text-purple-300">
                {aiStrategy.strategyName}
              </h3>
              <p className="mb-4">{aiStrategy.description}</p>

              <p>
                <strong>📌 Instruments:</strong> {aiStrategy.instruments}
              </p>
              <p>
                <strong>⏳ Timeframe:</strong> {aiStrategy.timeframe}
              </p>

              {/* ✅ Indicators Section */}
              <h4 className="mt-4 font-bold text-purple-400">📊 Indicators:</h4>
              <ul>
                {aiStrategy.indicators.map((ind, index) => (
                  <li key={index}>
                    🔹 {ind.indicator_class}(
                    {Object.entries(ind.parameters)
                      .map(([key, value]) => `${key}: ${value}`)
                      .join(", ")}
                    )
                  </li>
                ))}
              </ul>

              {/* ✅ Entry Rules Section */}
              <h4 className="mt-4 font-bold text-green-400">📈 Entry Rules:</h4>
              <ul>
                {aiStrategy.entryRules.map((rule, index) => (
                  <li key={index}>
                    🟢 {rule.name}: {rule.condition}
                  </li>
                ))}
              </ul>

              {/* ✅ Exit Rules Section */}
              <h4 className="mt-4 font-bold text-red-400">📉 Exit Rules:</h4>
              <ul>
                {aiStrategy.exitRules.map((rule, index) => (
                  <li key={index}>
                    🔴 {rule.name}: {rule.condition}
                  </li>
                ))}
              </ul>

              {/* ✅ Risk Management Section */}
              <h4 className="mt-4 font-bold text-yellow-400">
                ⚖️ Risk Management:
              </h4>
              <p>🚨 Stop Loss: {aiStrategy.riskManagement.stopLoss}</p>
              <p>🎯 Take Profit: {aiStrategy.riskManagement.takeProfit}</p>

              <button
                onClick={handleOpenModal}
                className="mt-6 px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:scale-105 transition transform duration-300"
                disabled={loadingBacktest}
              >
                {loadingBacktest ? "⏳ Backtesting..." : "Backtest Strategy"}
              </button>

              {/* ✅ Display Backtest Results */}
              {backtestResults && backtestResults.performance ? (
                <motion.div
                  className="mt-10 w-full max-w-5xl p-8 rounded-xl bg-gray-900 shadow-lg border border-gray-800"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4 }}
                >
                  <h3 className="text-2xl font-semibold text-gray-100 mb-6 tracking-wide">
                    📊 Backtest Results Summary
                  </h3>

                  {/* ✅ Performance Overview */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    {[
                      {
                        label: "Final Portfolio Value",
                        value: `$${
                          backtestResults.performance?.final_portfolio_value ??
                          "N/A"
                        }`,
                      },
                      {
                        label: "Total Trades",
                        value:
                          backtestResults.performance?.total_trades ?? "N/A",
                      },
                      {
                        label: "Max Drawdown",
                        value: `${
                          backtestResults.performance?.max_drawdown ?? "N/A"
                        }%`,
                      },
                    ].map((metric, index) => (
                      <div
                        key={index}
                        className="p-5 rounded-lg bg-gray-800 border border-gray-700 shadow-md text-center"
                      >
                        <h4 className="text-sm font-medium text-gray-400 uppercase">
                          {metric.label}
                        </h4>
                        <p className="text-lg font-semibold text-gray-100">
                          {metric.value}
                        </p>
                      </div>
                    ))}
                  </div>

                  {/* ✅ Advanced Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                    {[
                      {
                        label: "Sharpe Ratio",
                        value:
                          backtestResults.performance?.sharpe_ratio ?? "N/A",
                      },
                      {
                        label: "Profit Factor",
                        value:
                          backtestResults.performance?.profit_factor ?? "N/A",
                      },
                      {
                        label: "Win Rate",
                        value: `${
                          backtestResults.performance?.win_rate ?? "N/A"
                        }%`,
                      },
                    ].map((metric, index) => (
                      <div
                        key={index}
                        className="p-5 rounded-lg bg-gray-800 border border-gray-700 shadow-md text-center"
                      >
                        <h4 className="text-sm font-medium text-gray-400 uppercase">
                          {metric.label}
                        </h4>
                        <p className="text-lg font-semibold text-gray-100">
                          {metric.value}
                        </p>
                      </div>
                    ))}
                  </div>

                  {/* ✅ Best & Worst Trades */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {[
                      {
                        label: "Best Trade",
                        value: `$${
                          backtestResults.performance?.best_trade ?? "N/A"
                        }`,
                      },
                      {
                        label: "Worst Trade",
                        value: `$${
                          backtestResults.performance?.worst_trade ?? "N/A"
                        }`,
                      },
                    ].map((trade, index) => (
                      <div
                        key={index}
                        className="p-5 rounded-lg bg-gray-800 border border-gray-700 shadow-md text-center"
                      >
                        <h4 className="text-sm font-medium text-gray-400 uppercase">
                          {trade.label}
                        </h4>
                        <p className="text-lg font-semibold text-gray-100">
                          {trade.value}
                        </p>
                      </div>
                    ))}
                  </div>

                  {/* ✅ Consecutive Wins & Losses */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {[
                      {
                        label: "Win Rate",
                        value: `${
                          backtestResults.performance?.win_rate ?? "N/A"
                        }%`,
                      },
                      {
                        label: "Lose Rate",
                        value: `${
                          backtestResults.performance?.lose_rate ?? "N/A"
                        }%`,
                      },
                    ].map((stat, index) => (
                      <div
                        key={index}
                        className="p-5 rounded-lg bg-gray-800 border border-gray-700 shadow-md text-center"
                      >
                        <h4 className="text-sm font-medium text-gray-400 uppercase">
                          {stat.label}
                        </h4>
                        <p className="text-lg font-semibold text-gray-100">
                          {stat.value}
                        </p>
                      </div>
                    ))}
                  </div>
                </motion.div>
              ) : backtestStatus ? (
                <motion.div
                  className="mt-6 w-full max-w-4xl bg-gray-900 p-6 rounded-lg shadow-lg border border-red-500 text-center"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4 }}
                >
                  <h3 className="text-2xl font-semibold text-red-500">
                    ❌ No Backtest Results Found
                  </h3>
                  <p className="text-md text-gray-400">
                    The backtest results are missing or incomplete.
                  </p>
                </motion.div>
              ) : null}
            </div>
          )}
          {/*   Timeframe and TimeZone Selection Modal */}
          {isModalOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-md flex justify-center items-center z-50">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="bg-gray-900 text-white p-8 rounded-2xl shadow-2xl max-w-md w-full text-center"
              >
                <h2 className="text-2xl font-bold mb-4 text-cyan-300">
                  📊 Configure Backtest
                </h2>

                {/*   Timeframe Selection */}
                <label className="block text-lg font-semibold text-gray-300 mb-2">
                  ⏳ Select Timeframe:
                </label>
                <select
                  value={selectedTimeframe}
                  onChange={(e) => setSelectedTimeframe(e.target.value)}
                  className="w-full p-3 rounded-lg bg-gray-800 text-white border border-gray-600 focus:ring-4 focus:ring-cyan-500"
                >
                  {["Minute", "Hour", "Daily", "Weekly", "Monthly"].map(
                    (timeframe) => (
                      <option key={timeframe} value={timeframe}>
                        {timeframe}
                      </option>
                    )
                  )}
                </select>

                {/*   Timezone Selection */}
                <label className="block text-lg font-semibold text-gray-300 mt-4 mb-2">
                  🌍 Choose Timezone:
                </label>
                <select
                  value={selectedTimezone}
                  onChange={(e) => setSelectedTimezone(e.target.value)}
                  className="w-full p-3 rounded-lg bg-gray-800 text-white border border-gray-600 focus:ring-4 focus:ring-cyan-500"
                >
                  {[
                    "UTC",
                    "America/New_York",
                    "America/Chicago",
                    "Europe/London",
                    "Asia/Tokyo",
                    "Australia/Sydney",
                  ].map((timezone) => (
                    <option key={timezone} value={timezone}>
                      {timezone}
                    </option>
                  ))}
                </select>

                {/* ✅ Starting Balance Selection */}
                <label className="block text-lg font-semibold text-gray-300 mt-4 mb-2">
                  💰 Starting Balance ($):
                </label>
                <input
                  type="number"
                  value={startingBalance}
                  onChange={(e) => setStartingBalance(Number(e.target.value))}
                  className="w-full p-3 rounded-lg bg-gray-800 text-white"
                />

                {/*   Action Buttons */}
                <div className="flex justify-between mt-6">
                  <button
                    onClick={() => setIsModalOpen(false)}
                    className="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() =>
                      handleConfirmBacktest(selectedTimeframe, selectedTimezone)
                    }
                    className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-all"
                  >
                    Start Backtest 🚀
                  </button>
                </div>
              </motion.div>
            </div>
          )}
        </div>
      </motion.div>
    </DashboardLayout>
  );
}
