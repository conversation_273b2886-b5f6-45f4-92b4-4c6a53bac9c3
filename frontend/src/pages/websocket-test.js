/**
 * Simple WebSocket test page
 * Tests basic WebSocket connection to our production server
 */

import React, { useState, useEffect, useRef } from 'react';
import { WEBSOCKET_URL } from '../config';

const WebSocketTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [subscriptions, setSubscriptions] = useState([]);
  const wsRef = useRef(null);

  // Connect to WebSocket
  const connect = () => {
    // Prevent multiple connections
    if (wsRef.current?.readyState === WebSocket.OPEN ||
        wsRef.current?.readyState === WebSocket.CONNECTING) {
      console.log('Already connected or connecting');
      return;
    }

    console.log('Connecting to WebSocket...');
    setConnectionStatus('connecting');

    const ws = new WebSocket(WEBSOCKET_URL);
    wsRef.current = ws;

    ws.onopen = () => {
      console.log('Connected to WebSocket');
      setConnectionStatus('connected');
      addMessage('system', 'Connected to WebSocket server');
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        addMessage('received', JSON.stringify(data, null, 2));

        // Handle specific message types
        if (data.type === 'subscription_confirmed') {
          setSubscriptions(prev => [...prev, `${data.forex_pair}_${data.timeframe}`]);
        }
      } catch (error) {
        addMessage('error', `Failed to parse message: ${event.data}`);
      }
    };

    ws.onclose = (event) => {
      console.log('WebSocket closed:', event.code, event.reason);
      setConnectionStatus('disconnected');
      addMessage('system', `Disconnected: ${event.code} - ${event.reason}`);
      wsRef.current = null;
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setConnectionStatus('error');
      addMessage('error', 'WebSocket connection error');
    };
  };

  // Disconnect from WebSocket
  const disconnect = () => {
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
    }
  };

  // Send message
  const sendMessage = (message) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(typeof message === 'string' ? message : JSON.stringify(message));
      addMessage('sent', typeof message === 'string' ? message : JSON.stringify(message, null, 2));
      return true;
    } else {
      addMessage('error', 'WebSocket not connected');
      return false;
    }
  };

  // Add message to log
  const addMessage = (type, content) => {
    const timestamp = new Date().toLocaleTimeString();
    setMessages(prev => [...prev, { type, content, timestamp }].slice(-50)); // Keep last 50 messages
  };

  // Predefined actions
  const subscribeToEURUSD = () => {
    sendMessage({
      type: 'subscribe',
      forex_pair: 'EURUSD',
      timeframe: '1m'
    });
  };

  const subscribeToGBPUSD = () => {
    sendMessage({
      type: 'subscribe',
      forex_pair: 'GBPUSD',
      timeframe: '1m'
    });
  };

  const unsubscribeFromEURUSD = () => {
    sendMessage({
      type: 'unsubscribe',
      forex_pair: 'EURUSD',
      timeframe: '1m'
    });
    setSubscriptions(prev => prev.filter(sub => sub !== 'EURUSD_1m'));
  };

  const sendPing = () => {
    sendMessage({ type: 'ping' });
  };

  const sendCustomMessage = () => {
    if (inputMessage.trim()) {
      try {
        const parsed = JSON.parse(inputMessage);
        sendMessage(parsed);
      } catch (error) {
        sendMessage(inputMessage);
      }
      setInputMessage('');
    }
  };

  // Auto-connect on mount (only once)
  useEffect(() => {
    let mounted = true;

    const connectOnce = () => {
      if (mounted && !wsRef.current) {
        connect();
      }
    };

    // Delay initial connection to avoid rapid reconnections
    const timer = setTimeout(connectOnce, 1000);

    return () => {
      mounted = false;
      clearTimeout(timer);
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    };
  }, []); // Empty dependency array - only run once

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-400';
      case 'connecting': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getMessageColor = (type) => {
    switch (type) {
      case 'sent': return 'text-blue-400';
      case 'received': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'system': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">WebSocket Test</h1>

        {/* Status and Controls */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <span className="text-lg">Status:</span>
              <span className={`text-lg font-medium ${getStatusColor()}`}>
                {connectionStatus}
              </span>
              <span className="text-sm text-gray-400">
                {WEBSOCKET_URL}
              </span>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={connect}
                disabled={connectionStatus === 'connected'}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 px-4 py-2 rounded"
              >
                Connect
              </button>
              <button
                onClick={disconnect}
                disabled={connectionStatus !== 'connected'}
                className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 px-4 py-2 rounded"
              >
                Disconnect
              </button>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <button
              onClick={subscribeToEURUSD}
              disabled={connectionStatus !== 'connected'}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-3 py-2 rounded text-sm"
            >
              Subscribe EUR/USD
            </button>
            <button
              onClick={subscribeToGBPUSD}
              disabled={connectionStatus !== 'connected'}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-3 py-2 rounded text-sm"
            >
              Subscribe GBP/USD
            </button>
            <button
              onClick={unsubscribeFromEURUSD}
              disabled={connectionStatus !== 'connected'}
              className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 px-3 py-2 rounded text-sm"
            >
              Unsubscribe EUR/USD
            </button>
            <button
              onClick={sendPing}
              disabled={connectionStatus !== 'connected'}
              className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 px-3 py-2 rounded text-sm"
            >
              Send Ping
            </button>
          </div>
        </div>

        {/* Custom Message */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-medium mb-3">Send Custom Message</h3>
          <div className="flex space-x-2">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder='{"type": "subscribe", "forex_pair": "EURUSD", "timeframe": "1m"}'
              className="flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm"
              onKeyPress={(e) => e.key === 'Enter' && sendCustomMessage()}
            />
            <button
              onClick={sendCustomMessage}
              disabled={connectionStatus !== 'connected' || !inputMessage.trim()}
              className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-600 px-4 py-2 rounded"
            >
              Send
            </button>
          </div>
        </div>

        {/* Subscriptions */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-medium mb-3">Active Subscriptions</h3>
          {subscriptions.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {subscriptions.map((sub, index) => (
                <span key={index} className="bg-green-600 px-3 py-1 rounded text-sm">
                  {sub}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-gray-400">No active subscriptions</p>
          )}
        </div>

        {/* Message Log */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-3">Message Log</h3>
          <div className="bg-gray-900 rounded p-4 h-96 overflow-y-auto font-mono text-sm">
            {messages.length === 0 ? (
              <p className="text-gray-400">No messages yet...</p>
            ) : (
              messages.map((msg, index) => (
                <div key={index} className="mb-2">
                  <span className="text-gray-500">[{msg.timestamp}]</span>
                  <span className={`ml-2 ${getMessageColor(msg.type)}`}>
                    {msg.type.toUpperCase()}:
                  </span>
                  <pre className="mt-1 whitespace-pre-wrap text-gray-300">
                    {msg.content}
                  </pre>
                </div>
              ))
            )}
          </div>
          <button
            onClick={() => setMessages([])}
            className="mt-3 bg-gray-600 hover:bg-gray-700 px-3 py-1 rounded text-sm"
          >
            Clear Log
          </button>
        </div>
      </div>
    </div>
  );
};

export default WebSocketTest;
