import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import axios from "axios";
import { motion } from "framer-motion";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "../../firebaseConfig";
import { doc, updateDoc, getFirestore } from "firebase/firestore";
import { app } from "../../firebaseConfig";
import { USE_FIREBASE_EMULATOR } from "../config";
import LoadingSpinner from "../components/LoadingSpinner";

const DashboardLayout = dynamic(() => import("../components/DashboardLayout"), { ssr: false });
const RegisterForm = dynamic(() => import("../components/RegisterForm"), { ssr: false });

export default function Dashboard({ isSidebarOpen }) {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [firebaseUser, setFirebaseUser] = useState(null);
  const [hasApiKey, setHasApiKey] = useState(null);
  const [accountData, setAccountData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [transitionComplete, setTransitionComplete] = useState(false);
  const [activeStrategies, setActiveStrategies] = useState([]);
  const [recentTrades, setRecentTrades] = useState([]);

  // Temporary state for historical data testing
  const [forexPair, setForexPair] = useState('EURUSD');
  const [timeframe, setTimeframe] = useState('15m');
  const [daysBack, setDaysBack] = useState(30);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processResult, setProcessResult] = useState(null);

  const GET_USER_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/get_user"
    : "https://get-user-ihjc6tjxia-uc.a.run.app";
  const GET_BROKER_ACCOUNT_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/get_oanda_account"
    : "https://get-oanda-account-ihjc6tjxia-uc.a.run.app";

  // const FETCH_HISTORICAL_DATA_URL = USE_FIREBASE_EMULATOR
  //   ? "http://127.0.0.1:5001/oryntrade/us-central1/fetch_and_store_historical_data"
  //   : "https://fetch-and-store-historical-data-ihjc6tjxia-uc.a.run.app";

  const FETCH_HISTORICAL_DATA_URL = "https://fetch-and-store-historical-data-ihjc6tjxia-uc.a.run.app";

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log("Auth state changed:", user);
      if (!user) {
        console.log("User not authenticated, redirecting to login");
        router.push("/login");
      } else {
        setIsAuthenticated(true);
        setFirebaseUser(user);
        console.log("User authenticated:", user);
      }
    });
    return () => unsubscribe();
  }, [router]);

  useEffect(() => {
    if (firebaseUser?.uid) {
      console.log("Fetching user data for UID:", firebaseUser.uid);
      axios
        .get(`${GET_USER_URL}?firebase_uid=${firebaseUser.uid}`)
        .then((response) => {
          console.log("User data fetched:", response.data);
          setHasApiKey(response.data.has_api_key);
          if (response.data.has_api_key) {
            fetchBrokerAccountData(firebaseUser.uid);
          } else {
            setLoading(false);
          }
        })
        .catch((error) => {
          console.error("Error fetching user data:", error);
          setHasApiKey(false);
          setLoading(false);
        });
    }
  }, [firebaseUser?.uid]);

  const fetchBrokerAccountData = (uid) => {
    console.log("Fetching account data for UID:", uid);
    axios
      .get(`${GET_BROKER_ACCOUNT_URL}?firebase_uid=${uid}`)
      .then((res) => {
        console.log("Account data fetched:", res.data);
        setAccountData(res.data);
        setTimeout(() => setTransitionComplete(true), 1000);
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error fetching account data:", err);
        setLoading(false);
      });
  };

  const handleApiKeyRegistered = (apiKey) => {
    console.log("API key registered:", apiKey);
    setHasApiKey(true);
    if (firebaseUser) {
      fetchBrokerAccountData(firebaseUser.uid);

      const db = getFirestore(app);
      const userDocRef = doc(db, "users", firebaseUser.uid);

      updateDoc(userDocRef, {
        api_key: apiKey,
        has_api_key: true,
      })
        .then(() => {
          console.log("API key and has_api_key updated in Firestore.");
        })
        .catch((error) => {
          console.error("Error updating Firestore document:", error);
        });
    }
  };

  // Temporary function to test historical data processing
  const handleProcessHistoricalData = async () => {
    setIsProcessing(true);
    setProcessResult(null);

    try {
      console.log(`Processing historical data for ${forexPair} ${timeframe}`);

      const response = await axios.post(FETCH_HISTORICAL_DATA_URL, {
        forex_pair: forexPair,
        timeframe: timeframe,
        days_back: daysBack
      });

      console.log('Historical data processing result:', response.data);
      setProcessResult(response.data);
    } catch (error) {
      console.error('Error processing historical data:', error);
      setProcessResult({
        success: false,
        error: error.response?.data?.error || error.message
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isAuthenticated) {
    return <LoadingSpinner fullScreen text="Loading dashboard..." />;
  }

  if (loading) {
    return <LoadingSpinner fullScreen text="Loading account data..." />;
  }

  return (
    <DashboardLayout>
      <div className={`w-full transition-all duration-200 ${isSidebarOpen ? 'lg:pl-0' : 'lg:pl-0'}`}>
        <div className="w-full space-y-6">
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-[#0A0B0B] rounded-xl p-6 text-[#FEFEFF] shadow-lg border border-[#1a1a1a] relative overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-[#EFBD3A]/10 via-[#EFBD3A]/5 to-[#EFBD3A]/10 pointer-events-none"></div>
            <h1 className="text-3xl font-bold mb-2">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">
                Welcome back,
              </span>
              <span className="text-[#FEFEFF] ml-2">{firebaseUser?.email?.split('@')[0]}!</span>
            </h1>
            <p className="text-gray-300">Here's what's happening with your trading account today.</p>
          </motion.div>

          {/* Show RegisterForm if API key is not registered */}
          {!hasApiKey && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="w-full"
            >
              <RegisterForm firebaseUser={firebaseUser} onApiKeyRegistered={handleApiKeyRegistered} />
            </motion.div>
          )}

          {hasApiKey === true && (
            <>
              {/* Temporary Historical Data Testing Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-[#0A0B0B] rounded-xl p-6 text-[#FEFEFF] shadow-lg border border-[#1a1a1a] relative overflow-hidden mb-6"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 via-red-500/5 to-red-500/10 pointer-events-none"></div>
                <h2 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-red-400 to-red-400">
                  🧪 TEMPORARY: Historical Data Testing
                </h2>
                <p className="text-gray-300 mb-4 text-sm">
                  This is a temporary testing interface for the new historical data function. It will be removed after testing.
                  <br />
                  <strong>Note:</strong> Using {daysBack} days of data instead of 1 year to avoid timeout issues during testing.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Forex Pair</label>
                    <select
                      value={forexPair}
                      onChange={(e) => setForexPair(e.target.value)}
                      className="w-full bg-[#1a1a1a] border border-[#333] rounded-lg px-3 py-2 text-[#FEFEFF] focus:outline-none focus:ring-2 focus:ring-red-400"
                      disabled={isProcessing}
                    >
                      <option value="EURUSD">EUR/USD</option>
                      <option value="GBPUSD">GBP/USD</option>
                      <option value="USDJPY">USD/JPY</option>
                      <option value="AUDUSD">AUD/USD</option>
                      <option value="USDCAD">USD/CAD</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Timeframe</label>
                    <select
                      value={timeframe}
                      onChange={(e) => setTimeframe(e.target.value)}
                      className="w-full bg-[#1a1a1a] border border-[#333] rounded-lg px-3 py-2 text-[#FEFEFF] focus:outline-none focus:ring-2 focus:ring-red-400"
                      disabled={isProcessing}
                    >
                      <option value="1m">1 Minute</option>
                      <option value="5m">5 Minutes</option>
                      <option value="15m">15 Minutes</option>
                      <option value="30m">30 Minutes</option>
                      <option value="1h">1 Hour</option>
                      <option value="4h">4 Hours</option>
                      <option value="1d">1 Day</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Days Back</label>
                    <select
                      value={daysBack}
                      onChange={(e) => setDaysBack(parseInt(e.target.value))}
                      className="w-full bg-[#1a1a1a] border border-[#333] rounded-lg px-3 py-2 text-[#FEFEFF] focus:outline-none focus:ring-2 focus:ring-red-400"
                      disabled={isProcessing}
                    >
                      <option value={7}>7 Days</option>
                      <option value={14}>14 Days</option>
                      <option value={30}>30 Days</option>
                      <option value={60}>60 Days</option>
                      <option value={90}>90 Days</option>
                      <option value={180}>180 Days</option>
                      <option value={365}>1 Year (365 Days)</option>
                    </select>
                  </div>
                </div>

                <button
                  onClick={handleProcessHistoricalData}
                  disabled={isProcessing}
                  className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white font-medium py-3 rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 focus:ring-offset-[#0A0B0B] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isProcessing ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Processing... (This may take several minutes)
                    </div>
                  ) : (
                    `Fetch & Store ${daysBack} Days of ${forexPair} ${timeframe} Data`
                  )}
                </button>

                {processResult && (
                  <div className="mt-4 p-4 bg-[#1a1a1a] rounded-lg border border-[#333]">
                    <h3 className="text-sm font-medium text-gray-300 mb-2">Result:</h3>
                    {processResult.success ? (
                      <div className="text-green-400 text-sm">
                        <p>✅ Success! Processed {processResult.total_candles} candles</p>
                        <p>📁 Created {processResult.total_files} daily files</p>
                        <p>📅 Date range: {processResult.date_range?.start} to {processResult.date_range?.end}</p>
                        <p>☁️ Stored in bucket: {processResult.bucket}</p>
                      </div>
                    ) : (
                      <div className="text-red-400 text-sm">
                        <p>❌ Error: {processResult.error}</p>
                      </div>
                    )}
                  </div>
                )}
              </motion.div>

              <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Account Overview Card */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] relative group hover:border-[#EFBD3A]/40 transition-all duration-300"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-[#EFBD3A]/5 via-transparent to-[#EFBD3A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                <h2 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">Account Overview</h2>
                <div className="space-y-4 relative">
                  <div>
                    <p className="text-gray-400 text-sm">Balance</p>
                    <p className="text-3xl font-bold text-[#FEFEFF]">
                      ${accountData ? parseFloat(accountData.balance).toFixed(2) : "0.00"}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">Account ID</p>
                    <p className="text-lg text-[#FEFEFF]">{accountData?.account_id || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">Last Updated</p>
                    <p className="text-sm text-gray-300">
                      {new Date().toLocaleString()}
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Active Strategies Card */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] relative group hover:border-[#EFBD3A]/40 transition-all duration-300"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-[#EFBD3A]/5 via-transparent to-[#EFBD3A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                <h2 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">Active Strategies</h2>
                {activeStrategies.length > 0 ? (
                  <div className="space-y-3">
                    {activeStrategies.map((strategy, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-[#1a1a1a] rounded-lg border border-[#1a1a1a]/50">
                        <div>
                          <p className="text-[#FEFEFF] font-medium">{strategy.name}</p>
                          <p className="text-sm text-gray-400">{strategy.pair}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          strategy.status === 'active' ? 'bg-[#EFBD3A]/20 text-[#EFBD3A]' : 'bg-yellow-500/20 text-yellow-400'
                        }`}>
                          {strategy.status}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-400">No active strategies</p>
                )}
                <button
                  onClick={() => router.push('/strategy-generation')}
                  className="mt-4 w-full bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A] text-[#0A0B0B] font-medium py-2 rounded-lg hover:from-[#EFBD3A] hover:to-[#EFBD3A] transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:ring-offset-2 focus:ring-offset-[#0A0B0B]"
                >
                  Create New Strategy
                </button>
              </motion.div>

              {/* Recent Trades Card */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] relative group hover:border-[#EFBD3A]/40 transition-all duration-300"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-[#EFBD3A]/5 via-transparent to-[#EFBD3A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                <h2 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">Recent Trades</h2>
                {recentTrades.length > 0 ? (
                  <div className="space-y-3">
                    {recentTrades.map((trade, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-[#1a1a1a] rounded-lg border border-[#1a1a1a]/50">
                        <div>
                          <p className="text-[#FEFEFF] font-medium">{trade.pair}</p>
                          <p className="text-sm text-gray-400">{trade.type}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          trade.profit > 0 ? 'bg-[#EFBD3A]/20 text-[#EFBD3A]' : 'bg-red-500/20 text-red-400'
                        }`}>
                          {trade.profit > 0 ? '+' : ''}{trade.profit}%
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-400">No recent trades</p>
                )}
                <button
                  onClick={() => router.push('/trades')}
                  className="mt-4 w-full bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A] text-[#0A0B0B] font-medium py-2 rounded-lg hover:from-[#EFBD3A] hover:to-[#EFBD3A] transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:ring-offset-2 focus:ring-offset-[#0A0B0B]"
                >
                  View All Trades
                </button>
              </motion.div>
            </div>
            </>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}