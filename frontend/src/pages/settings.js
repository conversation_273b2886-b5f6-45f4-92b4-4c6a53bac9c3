import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { motion, AnimatePresence } from "framer-motion";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "../../firebaseConfig";
import { doc, getDoc, updateDoc, deleteDoc, getFirestore } from "firebase/firestore";
import { app } from "../../firebaseConfig";
import { updatePassword, deleteUser } from "firebase/auth";
import LoadingSpinner from "../components/LoadingSpinner";
import dynamic from "next/dynamic";
import axios from "axios";
import { USE_FIREBASE_EMULATOR } from "../config";
import { getFunctions, httpsCallable } from "firebase/functions";

const DashboardLayout = dynamic(() => import("../components/DashboardLayout"), { ssr: false });

const UPDATE_API_KEY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/update_api_key"
  : "https://update-api-key-ihjc6tjxia-uc.a.run.app";

const CHANGE_PASSWORD_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/change_password"
  : "https://change-password-ihjc6tjxia-uc.a.run.app";

const DELETE_ACCOUNT_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/delete_account"
  : "https://delete-account-ihjc6tjxia-uc.a.run.app";

const UPDATE_TIMEZONE_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/update_timezone"
  : "https://update-timezone-ihjc6tjxia-uc.a.run.app";

export default function Settings() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [firebaseUser, setFirebaseUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [showApiKey, setShowApiKey] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [newApiKey, setNewApiKey] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [activeTab, setActiveTab] = useState('account');
  const [timezone, setTimezone] = useState('');
  const [isUpdatingTimezone, setIsUpdatingTimezone] = useState(false);
  const [timezoneOptions, setTimezoneOptions] = useState([]);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        router.push("/login");
      } else {
        setIsAuthenticated(true);
        setFirebaseUser(user);
        fetchUserData(user.uid);
      }
    });
    return () => unsubscribe();
  }, [router]);

  useEffect(() => {
    // Generate timezone options with both full name and abbreviation
    const options = Intl.supportedValuesOf('timeZone').map((tz) => {
      const now = new Date();
      const formatter = new Intl.DateTimeFormat('en-US', {
        timeZone: tz,
        timeZoneName: 'long',
        hour: 'numeric',
        minute: 'numeric'
      });
      const timeString = formatter.format(now);
      const abbrFormatter = new Intl.DateTimeFormat('en-US', {
        timeZone: tz,
        timeZoneName: 'short'
      });
      const timezoneAbbr = abbrFormatter.formatToParts(now)
        .find(part => part.type === 'timeZoneName')
        .value;
      return {
        fullName: tz,
        display: `${timeString} (${timezoneAbbr})`,
        abbreviation: timezoneAbbr
      };
    });
    setTimezoneOptions(options);
  }, []);

  // Find the current timezone option based on the stored abbreviation
  const currentTimezoneOption = timezoneOptions.find(
    option => option.abbreviation === userData?.timezone
  ) || timezoneOptions[0];

  const fetchUserData = async (uid) => {
    try {
      const db = getFirestore(app);
      const userDoc = await getDoc(doc(db, "users", uid));
      if (userDoc.exists()) {
        setUserData(userDoc.data());
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching user data:", error);
      setLoading(false);
    }
  };

  const handlePasswordChange = async (e) => {
    e.preventDefault();
    setIsUpdating(true);
    setMessage({ type: '', text: '' });

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setMessage({ type: 'error', text: 'New passwords do not match' });
      setIsUpdating(false);
      return;
    }

    try {
      const response = await axios.post(CHANGE_PASSWORD_URL, {
        firebase_uid: firebaseUser.uid,
        new_password: passwordData.newPassword
      });

      if (response.status === 200) {
        setMessage({ type: 'success', text: 'Password updated successfully' });
        setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: error.response?.data?.error || 'Failed to update password. Please try again.' 
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleApiKeyUpdate = async (e) => {
    e.preventDefault();
    setIsUpdating(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await axios.post(UPDATE_API_KEY_URL, {
        firebase_uid: firebaseUser.uid,
        api_key: newApiKey
      });

      if (response.status === 200) {
        setUserData(prev => ({ ...prev, api_key: newApiKey }));
        setMessage({ type: 'success', text: 'API key updated successfully' });
        setNewApiKey('');
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: error.response?.data?.error || 'Failed to update API key. Please try again.' 
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleAccountDelete = async () => {
    setIsUpdating(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await axios.post(DELETE_ACCOUNT_URL, {
        firebase_uid: firebaseUser.uid
      });

      if (response.status === 200) {
        await auth.signOut();
        router.push("/login");
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: error.response?.data?.error || 'Failed to delete account. Please try again.' 
      });
      setIsUpdating(false);
    }
  };

  const handleTimezoneUpdate = async (newTimezone) => {
    setIsUpdatingTimezone(true);
    setMessage({ type: '', text: '' });

    try {
      // Convert the timezone to its abbreviation
      const now = new Date();
      const formatter = new Intl.DateTimeFormat('en-US', {
        timeZone: newTimezone,
        timeZoneName: 'short'
      });
      const timezoneAbbr = formatter.formatToParts(now)
        .find(part => part.type === 'timeZoneName')
        .value;

      const functions = getFunctions(app);
      const updateTimezone = httpsCallable(functions, 'update_timezone');
      
      await updateTimezone({ timezone: timezoneAbbr });
      
      setUserData(prev => ({ ...prev, timezone: timezoneAbbr }));
      setMessage({ type: 'success', text: 'Timezone updated successfully' });
    } catch (error) {
      console.error("Error updating timezone:", error);
      setMessage({ 
        type: 'error', 
        text: error.message || 'Failed to update timezone. Please try again.' 
      });
    } finally {
      setIsUpdatingTimezone(false);
    }
  };

  if (!isAuthenticated || loading) {
    return <LoadingSpinner fullScreen text="Loading settings..." />;
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] relative group hover:border-[#EFBD3A]/40 transition-all duration-300"
        >
          <div className="absolute inset-0 bg-gradient-to-br from-[#EFBD3A]/5 via-transparent to-[#EFBD3A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
          <h1 className="text-3xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">Settings</h1>
          <p className="text-gray-400">Manage your account settings and preferences</p>
        </motion.div>

        {/* Message Display */}
        <AnimatePresence>
          {message.text && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className={`p-4 rounded-lg ${
                message.type === 'success' ? 'bg-[#EFBD3A]/20 text-[#EFBD3A]' : 'bg-red-500/20 text-red-400'
              }`}
            >
              {message.text}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Tabs */}
        <div className="flex space-x-4 bg-[#1a1a1a] p-1 rounded-lg">
          {['account', 'security', 'api', 'danger'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab
                  ? 'bg-[#EFBD3A] text-[#0A0B0B]'
                  : 'text-gray-400 hover:text-[#FEFEFF] hover:bg-[#0A0B0B]'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'account' && (
            <motion.div
              key="account"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] space-y-6"
            >
              <div>
                <h2 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">Account Information</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Email</label>
                    <p className="text-[#FEFEFF]">{firebaseUser?.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Account Created</label>
                    <p className="text-[#FEFEFF]">{userData?.created_at ? new Date(userData.created_at).toLocaleDateString() : 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Timezone</label>
                    <div className="relative">
                      <select
                        value={currentTimezoneOption?.fullName}
                        onChange={(e) => handleTimezoneUpdate(e.target.value)}
                        disabled={isUpdatingTimezone}
                        className="w-full bg-[#1a1a1a] border border-[#1a1a1a] rounded-lg px-4 py-2.5 text-[#FEFEFF] focus:outline-none focus:border-[#EFBD3A] transition-colors appearance-none cursor-pointer"
                      >
                        {timezoneOptions.map((option) => (
                          <option key={option.fullName} value={option.fullName} className="bg-[#1a1a1a] text-[#FEFEFF]">
                            {option.display}
                          </option>
                        ))}
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg className="w-5 h-5 text-[#FEFEFF]/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                    {isUpdatingTimezone && (
                      <div className="mt-2 text-sm text-[#EFBD3A] flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-[#EFBD3A]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Updating timezone...
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'security' && (
            <motion.div
              key="security"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] space-y-6"
            >
              <div>
                <h2 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">Change Password</h2>
                <form onSubmit={handlePasswordChange} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">New Password</label>
                    <input
                      type="password"
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                      className="w-full bg-[#1a1a1a] border border-[#1a1a1a] rounded-lg px-4 py-2 text-[#FEFEFF] focus:outline-none focus:border-[#EFBD3A] transition-colors"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Confirm New Password</label>
                    <input
                      type="password"
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                      className="w-full bg-[#1a1a1a] border border-[#1a1a1a] rounded-lg px-4 py-2 text-[#FEFEFF] focus:outline-none focus:border-[#EFBD3A] transition-colors"
                      required
                    />
                  </div>
                  <button
                    type="submit"
                    disabled={isUpdating}
                    className="w-full bg-[#EFBD3A] text-[#0A0B0B] font-medium py-2 rounded-lg hover:bg-[#EFBD3A]/90 transition-colors focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:ring-offset-2 focus:ring-offset-[#0A0B0B] disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUpdating ? 'Updating...' : 'Update Password'}
                  </button>
                </form>
              </div>
            </motion.div>
          )}

          {activeTab === 'api' && (
            <motion.div
              key="api"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] space-y-6"
            >
              <div>
                <h2 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">API Key Management</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Current API Key</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type={showApiKey ? "text" : "password"}
                        value={userData?.api_key || ''}
                        readOnly
                        className="w-full bg-[#1a1a1a] border border-[#1a1a1a] rounded-lg px-4 py-2 text-[#FEFEFF]"
                      />
                      <button
                        onClick={() => setShowApiKey(!showApiKey)}
                        className="px-3 py-2 bg-[#1a1a1a] text-[#FEFEFF] rounded-lg hover:bg-[#EFBD3A] hover:text-[#0A0B0B] transition-colors"
                      >
                        {showApiKey ? 'Hide' : 'Show'}
                      </button>
                    </div>
                  </div>
                  <form onSubmit={handleApiKeyUpdate} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">New API Key</label>
                      <input
                        type="text"
                        value={newApiKey}
                        onChange={(e) => setNewApiKey(e.target.value)}
                        className="w-full bg-[#1a1a1a] border border-[#1a1a1a] rounded-lg px-4 py-2 text-[#FEFEFF] focus:outline-none focus:border-[#EFBD3A] transition-colors"
                        required
                      />
                    </div>
                    <button
                      type="submit"
                      disabled={isUpdating}
                      className="w-full bg-[#EFBD3A] text-[#0A0B0B] font-medium py-2 rounded-lg hover:bg-[#EFBD3A]/90 transition-colors focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:ring-offset-2 focus:ring-offset-[#0A0B0B] disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isUpdating ? 'Updating...' : 'Update API Key'}
                    </button>
                  </form>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'danger' && (
            <motion.div
              key="danger"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-red-500/20 space-y-6"
            >
              <div>
                <h2 className="text-xl font-semibold mb-4 text-red-500">Danger Zone</h2>
                <p className="text-gray-400 mb-4">Once you delete your account, there is no going back. Please be certain.</p>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="w-full bg-red-500/10 text-red-500 border border-red-500/20 font-medium py-2 rounded-lg hover:bg-red-500/20 transition-colors"
                >
                  Delete Account
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Delete Account Confirmation Modal */}
        <AnimatePresence>
          {showDeleteConfirm && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
            >
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                className="bg-[#0A0B0B] rounded-xl p-6 max-w-md w-full border border-red-500/20"
              >
                <h3 className="text-xl font-semibold mb-4 text-red-500">Delete Account</h3>
                <p className="text-gray-400 mb-6">Are you sure you want to delete your account? This action cannot be undone.</p>
                <div className="flex space-x-4">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="flex-1 bg-[#1a1a1a] text-[#FEFEFF] font-medium py-2 rounded-lg hover:bg-[#1a1a1a]/80 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAccountDelete}
                    disabled={isUpdating}
                    className="flex-1 bg-red-500/10 text-red-500 border border-red-500/20 font-medium py-2 rounded-lg hover:bg-red-500/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUpdating ? 'Deleting...' : 'Yes, Delete'}
                  </button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </DashboardLayout>
  );
} 