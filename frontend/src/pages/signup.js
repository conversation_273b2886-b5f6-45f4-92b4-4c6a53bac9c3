import { useState } from 'react';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { auth } from '../../firebaseConfig';
import { useRouter } from 'next/router';
import Link from 'next/link';
import LoadingSpinner from '../components/LoadingSpinner';
import axios from 'axios';
import { USE_FIREBASE_EMULATOR } from '../config';

export default function Signup() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const SIGNUP_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/signup"
    : "https://signup-ihjc6tjxia-uc.a.run.app";

  const handleSignup = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      const response = await axios.post(
        SIGNUP_URL,
        JSON.stringify({
          email: user.email,
          firebase_uid: user.uid,
        }),
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 201 || response.status === 200) {
        router.push('/dashboard');
      } else {
        setError('Signup failed. Please try again.');
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-black py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 mb-4">
            <svg 
              viewBox="-2 -2 28 28" 
              className="w-12 h-12"
              style={{ overflow: 'visible' }}
            >
              <g fill="#FFB800">
                <rect x="2" y="14" width="4" height="10" />
                <rect x="8" y="9" width="4" height="15" />
                <rect x="14" y="4" width="4" height="20" />
                <rect x="20" y="0" width="4" height="24" />
                <path d="M20,0 L24,0 L20,4 Z" />
              </g>
            </svg>
          </div>
          <h2 className="text-center text-3xl font-bold">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#FFB800] to-[#FFA000]">
              Oryn
            </span>
            <span className="text-white ml-1">Trade</span>
          </h2>
          <p className="mt-2 text-center text-sm text-gray-400">
            Create your account
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSignup}>
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-md p-3">
              <p className="text-sm text-red-400 text-center">{error}</p>
            </div>
          )}
          
          <div className="rounded-md shadow-sm -space-y-px bg-gray-900">
            <div>
              <label htmlFor="email-address" className="sr-only">
                Email address
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-t-md relative block w-full px-3 py-2 border border-gray-700 placeholder-gray-400 text-white bg-gray-900 focus:outline-none focus:ring-primary-gold focus:border-primary-gold focus:z-10 sm:text-sm"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-700 placeholder-gray-400 text-white bg-gray-900 focus:outline-none focus:ring-primary-gold focus:border-primary-gold focus:z-10 sm:text-sm"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="confirm-password" className="sr-only">
                Confirm Password
              </label>
              <input
                id="confirm-password"
                name="confirm-password"
                type="password"
                autoComplete="new-password"
                required
                className="appearance-none rounded-b-md relative block w-full px-3 py-2 border border-gray-700 placeholder-gray-400 text-white bg-gray-900 focus:outline-none focus:ring-primary-gold focus:border-primary-gold focus:z-10 sm:text-sm"
                placeholder="Confirm Password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-black bg-[#FFB800] hover:bg-[#FFA000] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FFB800] focus:ring-offset-black disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <LoadingSpinner size="small" />
              ) : (
                'Sign up'
              )}
            </button>
          </div>

          <div className="text-center">
            <Link
              href="/login"
              className="text-sm text-primary-gold hover:text-primary-gold/80"
            >
              Already have an account? Sign in
            </Link>
          </div>

          <div className="text-center space-y-2">
            <Link
              href="/login"
              className="text-sm text-gray-400 hover:text-gray-300"
            >
              ← Back to Login
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}