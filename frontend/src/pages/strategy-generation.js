import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import dynamic from "next/dynamic";
import axios from "axios";
import { useLocalStorage } from "../hooks/useLocalStorage";
import { USE_FIREBASE_EMULATOR } from "../config";
import { onAuthStateChanged } from "firebase/auth";
import { getFirestore, doc, getDoc } from 'firebase/firestore';
import { auth } from "../../firebaseConfig";
import { useRouter } from "next/router";
import StrategyDescriptionDialog from "../components/StrategyDescriptionDialog";
import BacktestResults from '../components/BacktestResults';
import SaveStrategyNotification from '../components/SaveStrategyNotification';
import { useInView } from "react-intersection-observer";
import { toast } from 'react-toastify';
import StrategyChart from '../components/StrategyChart';
import { getDateRange } from '../utils/dateUtils';

// API URLs
const GET_HISTORICAL_DATA_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/fetch_historical_data_from_polygon"
  : "https://fetch-historical-data-from-polygon-ihjc6tjxia-uc.a.run.app";

const SAVE_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/save_strategy"
  : "https://save-strategy-ihjc6tjxia-uc.a.run.app";

const GET_STRATEGIES_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/get_strategies"
  : "https://get-strategies-ihjc6tjxia-uc.a.run.app";

const UPDATE_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/update_strategy"
  : "https://update-strategy-ihjc6tjxia-uc.a.run.app";

const GET_FOREX_PAIRS_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/get_forex_pairs_oanda"
  : "https://get-forex-pairs-oanda-ihjc6tjxia-uc.a.run.app";

const RUN_BACKTEST_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/run_backtest"
  : "https://run-backtest-ihjc6tjxia-uc.a.run.app";

// Constants
const timeframeOptions = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"];

const tradingSessions = [
  {
    name: "All",
    timezone: "All",
    description: "All Trading Sessions"
  },
  {
    name: "London",
    timezone: "Europe/London",
    description: "London Session"
  },
  {
    name: "New York",
    timezone: "America/New_York",
    description: "New York Session"
  },
  {
    name: "Tokyo",
    timezone: "Asia/Tokyo",
    description: "Tokyo Session"
  },
  {
    name: "Sydney",
    timezone: "Australia/Sydney",
    description: "Sydney Session"
  }
];

// Button styling constants
const buttonStyles = {
  primary: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-blue-500/30 border border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
  success: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-green-500/30 border border-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50",
  danger: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white font-medium rounded-lg shadow-lg hover:shadow-red-500/30 border border-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50",
  purple: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-purple-500/30 border border-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50",
  secondary: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white font-medium rounded-lg shadow-lg hover:shadow-gray-500/30 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50",
  add: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-lg shadow-md hover:shadow-blue-400/30 border border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50",
  remove: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-1 p-1 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-md shadow-md hover:shadow-red-400/30 border border-red-500 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50",
};

// Dynamic imports
const DashboardLayout = dynamic(() => import("../components/DashboardLayout"), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen bg-[#0A0B0B] flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin"></div>
    </div>
  ),
});

const BacktestDialogComponent = dynamic(
  () => import("../components/BacktestDialog").then(mod => {
    const { default: Component } = mod;
    return props => <Component {...props} />;
  }),
  {
    ssr: false,
    loading: () => (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-[#0c0f1c] p-6 rounded-xl border border-white/10">
          <div className="w-16 h-16 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    )
  }
);

const ClientOnlyDropdown = dynamic(() => import("../components/ClientOnlyDropdown"), {
  ssr: false,
  loading: () => (
    <div className="w-full h-12 bg-gray-800 rounded-lg animate-pulse"></div>
  ),
});

// Helper functions
const generateId = () =>
  Date.now().toString() + Math.random().toString(36).substring(2);

const getSessionNameFromTimezone = (timezone) => {
  switch(timezone) {
    case "Europe/London":
      return "London";
    case "America/New_York":
      return "New York";
    case "Asia/Tokyo":
      return "Tokyo";
    case "Australia/Sydney":
      return "Sydney";
    case "All":
      return "All";
    default:
      return null;
  }
};

// Components
const StepNavigation = ({ steps, currentStep, onStepClick }) => {
  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={`flex flex-col items-center ${
              step.id === currentStep ? 'text-[#EFBD3A]' : 'text-[#FEFEFF]/60'
            }`}
          >
            <motion.button
              onClick={() => onStepClick(step.id)}
              whileHover={step.id < currentStep ? { scale: 1.1 } : {}}
              whileTap={step.id < currentStep ? { scale: 0.9 } : {}}
              className={`w-16 h-16 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-300 ${
                currentStep > step.id
                  ? 'bg-gradient-to-b from-white/20 to-white/5 text-[#FEFEFF] shadow-lg cursor-pointer hover:from-white/30'
                  : currentStep === step.id
                  ? 'bg-gradient-to-b from-[#EFBD3A]/20 to-[#EFBD3A]/5 text-[#EFBD3A] ring-2 ring-[#EFBD3A]/20 shadow-lg'
                  : 'bg-gradient-to-b from-white/[0.08] to-transparent text-[#FEFEFF]/40 cursor-not-allowed opacity-50'
              }`}
              disabled={step.id >= currentStep}
            >
              {currentStep > step.id ? (
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                step.id
              )}
            </motion.button>
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className={`mt-2 text-sm font-medium ${
                currentStep >= step.id ? 'text-[#FEFEFF]' : 'text-[#FEFEFF]/40'
              }`}
            >
              {step.title}
            </motion.span>
          </div>
        ))}
      </div>
      <div className="h-1 bg-white/10 rounded-full mt-4">
        <motion.div
          className="h-full bg-[#EFBD3A] rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </div>
  );
};

const BacktestDialogWrapper = ({ isOpen, onClose, strategy, onBacktestComplete }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <BacktestDialogComponent
      isOpen={isOpen}
      onClose={onClose}
      strategy={strategy}
      onBacktestComplete={onBacktestComplete}
    />
  );
};

// Constants for form validation
const timezoneOptions = ["UTC", "EST", "PST", "CET", "IST", "GMT"];
const conditionOperators = ["Crossing above", "Crossing below", ">", "<", ">=", "<=", "=="];

// Supported indicators
const supportedIndicators = [
  { name: "SMA", defaultParameters: { period: 50 } },
  { name: "EMA", defaultParameters: { period: 20 } },
  { name: "RSI", defaultParameters: { period: 14 } },
  { name: "MACD", defaultParameters: { fast: 12, slow: 26, signal: 9 } },
  { name: "BollingerBands", defaultParameters: { period: 20, devfactor: 2, offset: 0 } }
];

// Main component
export default function StrategyGeneration() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [strategyName, setStrategyName] = useState('');
  const [forexPair, setForexPair] = useState('');
  const [isForexPairValid, setIsForexPairValid] = useState(true);
  const [isForexPairExists, setIsForexPairExists] = useState(true);
  const [availableForexPairs, setAvailableForexPairs] = useState([]);
  const [isLoadingForexPairs, setIsLoadingForexPairs] = useState(true);
  const [forexPairError, setForexPairError] = useState('');
  const [strategyDescription, setStrategyDescription] = useState('');
  const [tradingSession, setTradingSession] = useState('London');
  const [timeframe, setTimeframe] = useLocalStorage("timeframe", "1h");
  const [timezone, setTimezone] = useLocalStorage("timezone", "America/New_York");
  const [entryLongGroupOperator, setEntryLongGroupOperator] = useState('AND');
  const [exitLongGroupOperator, setExitLongGroupOperator] = useState('OR');
  const [hasBeenModified, setHasBeenModified] = useLocalStorage("hasBeenModified", false);
  const [isStrategySaved, setIsStrategySaved] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isStrategyDescriptionOpen, setIsStrategyDescriptionOpen] = useState(false);
  const [saveSuccessMessage, setSaveSuccessMessage] = useState("");
  const [userTimezone, setUserTimezone] = useState('UTC');
  const [firebaseUser, setFirebaseUser] = useState(null);
  const [riskPercentage, setRiskPercentage] = useState('');
  const [riskRewardRatio, setRiskRewardRatio] = useState('');
  const [stopLossMethod, setStopLossMethod] = useState('fixed');
  const [fixedPips, setFixedPips] = useState('');
  const [indicatorBasedSL, setIndicatorBasedSL] = useState('');
  const [indicatorParams, setIndicatorParams] = useState({});
  const [lotSize, setLotSize] = useState('');
  const [riskWarning, setRiskWarning] = useState('');
  const [isAtrAddedToChart, setIsAtrAddedToChart] = useState(false);
  const [isAddingAtrToChart, setIsAddingAtrToChart] = useState(false);
  const [pendingAtrIndicator, setPendingAtrIndicator] = useState(null);

  // State for Bollinger Bands indicator
  const [isBollingerAddedToChart, setIsBollingerAddedToChart] = useState(false);
  const [isAddingBollingerToChart, setIsAddingBollingerToChart] = useState(false);
  const [pendingBollingerIndicator, setPendingBollingerIndicator] = useState(null);

  // State for Support & Resistance indicator
  const [isSRAddedToChart, setIsSRAddedToChart] = useState(false);
  const [isAddingSRToChart, setIsAddingSRToChart] = useState(false);
  const [pendingSRIndicator, setPendingSRIndicator] = useState(null);

  // Add new state for forex data
  const [forexData, setForexData] = useState(null);
  const [isLoadingForexData, setIsLoadingForexData] = useState(false);
  const [forexDataError, setForexDataError] = useState(null);

  // Function to fetch forex data
  const fetchForexData = async (pair, tf) => {
    if (!pair || !tf) return;

    setIsLoadingForexData(true);
    setForexDataError(null);

    try {
      const { startDate, endDate, displayRange, chunkSize } = getDateRange(tf);
      let allData = [];
      let currentStart = new Date(startDate);

      while (currentStart < endDate) {
        let chunkEnd = new Date(currentStart);
        chunkEnd.setDate(chunkEnd.getDate() + chunkSize);

        if (chunkEnd > endDate) {
          chunkEnd = endDate;
        }

        const response = await axios.get(GET_HISTORICAL_DATA_URL, {
          params: {
            symbol: pair,
            timeframe: tf,
            start_date: currentStart.toISOString().split('T')[0],
            end_date: chunkEnd.toISOString().split('T')[0]
          }
        });

        if (response.data && response.data.data) {
          allData = [...allData, ...response.data.data];
        }

        currentStart = chunkEnd;
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Format and process the data
      const processedData = allData
        .filter(item => item && typeof item === 'object')
        .map(item => {
          // Log the raw datetime and its parsed UTC value
          console.log('Raw item.datetime:', item.datetime);

          // Parse the ISO datetime string and ensure it's treated as UTC
          // If the string doesn't end with 'Z', add it to ensure UTC interpretation
          const utcDateString = item.datetime.endsWith('Z') ?
            item.datetime :
            item.datetime + 'Z';

          // Create date object from UTC string
          const dateObj = new Date(utcDateString);

          // Get timestamp in seconds
          const timestamp = dateObj.getTime() / 1000;

          console.log('Parsed UTC:', new Date(timestamp * 1000).toUTCString());

          return {
            time: Math.floor(timestamp),
            open: Number(item.open) || 0,
            high: Number(item.high) || 0,
            low: Number(item.low) || 0,
            close: Number(item.close) || 0,
            volume: Number(item.volume) || 0
          };
        })
        .filter(item =>
          !isNaN(item.time) &&
          !isNaN(item.open) &&
          !isNaN(item.high) &&
          !isNaN(item.low) &&
          !isNaN(item.close) &&
          item.high >= item.low
        );

      // Remove duplicates and sort
      const uniqueData = Array.from(new Map(
        processedData.map(item => [item.time, item])
      ).values()).sort((a, b) => a.time - b.time);

      setForexData(uniqueData);
      // Log the last candle and current UTC time for debugging
      if (uniqueData && uniqueData.length > 0) {
        const lastCandle = uniqueData[uniqueData.length - 1];
        console.log('Last candle:', lastCandle);

        // Create a date object from the timestamp and explicitly show UTC time
        const lastCandleDate = new Date(lastCandle.time * 1000);
        console.log('Last candle UTC time:', lastCandleDate.toUTCString());
        console.log('Last candle UTC hours:', lastCandleDate.getUTCHours());
        console.log('Last candle UTC minutes:', lastCandleDate.getUTCMinutes());

        // Show current time for comparison
        const now = new Date();
        console.log('Current UTC time:', now.toUTCString());
        console.log('Current UTC hours:', now.getUTCHours());
        console.log('Current Local time:', now.toLocaleString());
      }
    } catch (error) {
      console.error('Error fetching forex data:', error);
      setForexDataError(error.message);
    } finally {
      setIsLoadingForexData(false);
    }
  };

  // Fetch data when forex pair is selected or timeframe changes
  useEffect(() => {
    if (forexPair && isForexPairValid && isForexPairExists) {
      fetchForexData(forexPair, timeframe);
    }
  }, [forexPair, timeframe]);

  // Fetch user timezone from Firestore
  useEffect(() => {
    const fetchUserTimezone = async () => {
      if (!firebaseUser?.uid) return;

      try {
        const db = getFirestore();
        const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));

        if (userDoc.exists()) {
          const userData = userDoc.data();
          setUserTimezone(userData.timezone || 'UTC');
        }
      } catch (error) {
        console.error('Error fetching user timezone:', error);
      }
    };

    fetchUserTimezone();
  }, [firebaseUser]);

  // Update the Firebase auth effect
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setFirebaseUser(user);
        setIsMounted(true);
        try {
          const db = getFirestore();
          const userDoc = await getDoc(doc(db, 'users', user.uid));

          if (userDoc.exists()) {
            const userData = userDoc.data();
            setUserTimezone(userData.timezone || 'UTC');
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          // Don't redirect on data fetch error, just set default timezone
          setUserTimezone('UTC');
        }
      } else {
        // Redirect to login if user not found
        router.push("/login");
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [router]);

  // Add navigation warning effects here
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasBeenModified && !isStrategySaved) {
        const message = "You have unsaved changes. Are you sure you want to leave?";
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasBeenModified, isStrategySaved]);

  useEffect(() => {
    const handleRouteChange = (url) => {
      if (hasBeenModified && !isStrategySaved) {
        const confirm = window.confirm("You have unsaved changes. Are you sure you want to leave?");
        if (!confirm) {
          router.events.emit('routeChangeError');
          throw 'Navigation cancelled by user';
        }
      }
    };

    router.events.on('routeChangeStart', handleRouteChange);
    return () => router.events.off('routeChangeStart', handleRouteChange);
  }, [hasBeenModified, isStrategySaved, router]);

  // Update the fetch saved strategies effect
  useEffect(() => {
    const fetchSavedStrategies = async () => {
      if (!firebaseUser?.uid) return;

      try {
        const response = await axios.get(`${GET_STRATEGIES_URL}?firebase_uid=${firebaseUser.uid}`);
        setSavedStrategies(response.data);
      } catch (error) {
        console.error("Error loading saved strategies:", error);
        // Set empty array on error to prevent undefined errors
        setSavedStrategies([]);
      }
    };

    if (firebaseUser?.uid) {
      fetchSavedStrategies();
    }
  }, [firebaseUser]);

  // Update the fetch forex pairs effect
  useEffect(() => {
    const fetchForexPairs = async () => {
      if (!firebaseUser?.uid) {
        setIsLoadingForexPairs(false);
        return;
      }

      try {
        const response = await axios.get(
          `${GET_FOREX_PAIRS_URL}?firebase_uid=${firebaseUser.uid}`
        );
        setAvailableForexPairs(response.data.forex_pairs);
      } catch (error) {
        console.error("Error fetching forex pairs:", error);
        setForexPairError("Failed to load available forex pairs");
        setAvailableForexPairs([]);
      } finally {
        setIsLoadingForexPairs(false);
      }
    };

    if (firebaseUser?.uid) {
      fetchForexPairs();
    }
  }, [firebaseUser]);

  // Validate forex pair format and existence
  const validateForexPair = (pair) => {
    if (isLoadingForexPairs || !availableForexPairs.length) {
      setIsForexPairValid(true); // Don't show error while loading
      setIsForexPairExists(true);
      setForexPairError("");
      return;
    }
    // Format validation: Should be in the format "XXX/XXX" or "XXX_XXX"
    const formatRegex = /^[A-Z]{3}[\/_][A-Z]{3}$/;
    const isValidFormat = formatRegex.test(pair);
    setIsForexPairValid(isValidFormat);

    // Normalize the pair format for comparison (convert / to _)
    const normalizedPair = pair.replace('/', '_');

    // Existence validation: Check if the pair exists in available pairs
    const exists = availableForexPairs.includes(normalizedPair);
    setIsForexPairExists(exists);

    // Set error message
    if (!isValidFormat) {
      setForexPairError("Please enter a valid forex pair format (e.g., EUR/USD)");
    } else if (!exists) {
      setForexPairError("This forex pair is not available for trading");
    } else {
      setForexPairError("");
    }
  };

  // Re-validate forex pair when pairs are loaded or changed
  useEffect(() => {
    if (!isLoadingForexPairs && forexPair) {
      validateForexPair(forexPair);
    }
  }, [isLoadingForexPairs, availableForexPairs]);

  const handleForexPairChange = (e) => {
    const pair = e.target.value.toUpperCase();
    setForexPair(pair);
    validateForexPair(pair);
  };

  // Define the steps
  const steps = [
    { id: 1, title: 'Basic Information', description: 'Set your strategy name and trading pair' },
    { id: 2, title: 'Time Settings', description: 'Configure timeframe and timezone' },
    { id: 3, title: 'Indicators', description: 'Add technical indicators to your strategy' },
    { id: 4, title: 'Entry Rules', description: 'Define when to enter trades' },
    { id: 5, title: 'Exit Rules', description: 'Define when to exit trades' },
    { id: 6, title: "Risk Management", description: 'Set stop loss and take profit levels' },
    { id: 7, title: 'Review', description: 'Review and finalize your strategy' }
  ];

  // Continue with the rest of your state declarations and effects
  const [stopLoss, setStopLoss] = useState("");
  const [takeProfit, setTakeProfit] = useState("");
  const [stopLossUnit, setStopLossUnit] = useLocalStorage("stopLossUnit", "pips");
  const [takeProfitUnit, setTakeProfitUnit] = useLocalStorage("takeProfitUnit", "pips");
  const [selectedIndicators, setSelectedIndicators] = useLocalStorage("selectedIndicators", []);

  // Function to check if a specific indicator type is already in the selectedIndicators array
  const isIndicatorTypeAlreadyAdded = useCallback((indicatorType) => {
    return selectedIndicators.some(ind => ind.type === indicatorType);
  }, [selectedIndicators]);

  // Effect to check if there are any indicators with displayPreference set to 'new'
  // This is a backup mechanism in case the immediate state updates in the callback don't work
  useEffect(() => {
    // Check for ATR indicators
    if (!isAtrAddedToChart) {
      const hasAddedAtrIndicator = selectedIndicators.some(
        ind => ind.type === 'ATR' && ind.displayPreference === 'new'
      );

      if (hasAddedAtrIndicator) {
        console.log('Found ATR indicator with displayPreference=new in useEffect');
        setIsAtrAddedToChart(true);
        setIsAddingAtrToChart(false);
      }
    }

    // Check for Bollinger Bands indicators
    if (!isBollingerAddedToChart) {
      // Check for Bollinger Bands indicators with displayPreference='new'
      const hasAddedBollingerIndicator = selectedIndicators.some(
        ind => ind.type === 'BollingerBands' && ind.displayPreference === 'new'
      );

      // Also check if there are any Bollinger Bands indicators at all
      const hasBollingerIndicator = selectedIndicators.some(
        ind => ind.type === 'BollingerBands'
      );

      if (hasAddedBollingerIndicator) {
        console.log('Found BollingerBands indicator with displayPreference=new in useEffect');
        setIsBollingerAddedToChart(true);
        setIsAddingBollingerToChart(false);
      } else if (hasBollingerIndicator) {
        // If there's any Bollinger Bands indicator, mark it as added to chart
        console.log('Found BollingerBands indicator in useEffect');
        setIsBollingerAddedToChart(true);
        setIsAddingBollingerToChart(false);
      }
    }

    // Check for Support & Resistance indicators
    if (!isSRAddedToChart) {
      // Check for Support & Resistance indicators with displayPreference='new'
      const hasAddedSRIndicator = selectedIndicators.some(
        ind => ind.type === 'SupportResistance' && ind.displayPreference === 'new'
      );

      // Also check if there are any Support & Resistance indicators at all
      const hasSRIndicator = selectedIndicators.some(
        ind => ind.type === 'SupportResistance'
      );

      if (hasAddedSRIndicator) {
        console.log('Found Support & Resistance indicator with displayPreference=new in useEffect');
        setIsSRAddedToChart(true);
        setIsAddingSRToChart(false);
      } else if (hasSRIndicator) {
        // If there's any Support & Resistance indicator, mark it as added to chart
        console.log('Found Support & Resistance indicator in useEffect');
        setIsSRAddedToChart(true);
        setIsAddingSRToChart(false);
      }
    }
  }, [selectedIndicators, isAtrAddedToChart, isBollingerAddedToChart, isSRAddedToChart]);

  const [entryRules, setEntryRules] = useLocalStorage("entryRules", []);
  const [exitRules, setExitRules] = useLocalStorage("exitRules", []);
  const [entryBuyGroupOperator, setEntryBuyGroupOperator] = useLocalStorage("entryBuyGroupOperator", "AND");
  const [entrySellGroupOperator, setEntrySellGroupOperator] = useLocalStorage("entrySellGroupOperator", "AND");
  const [exitBuyGroupOperator, setExitBuyGroupOperator] = useLocalStorage("exitBuyGroupOperator", "AND");
  const [exitSellGroupOperator, setExitSellGroupOperator] = useLocalStorage("exitSellGroupOperator", "AND");
  const [isStrategyFinalized, setIsStrategyFinalized] = useLocalStorage("isStrategyFinalized", false);
  const [savedStrategies, setSavedStrategies] = useLocalStorage("savedStrategies", []);
  const [selectedStoredStrategyId, setSelectedStoredStrategyId] = useLocalStorage("selectedStoredStrategyId", null);
  const [backtestResults, setBacktestResults] = useLocalStorage("backtestResults", null);
  const [backtestStatus, setBacktestStatus] = useState(null);
  const [backtestError, setBacktestError] = useState(null);
  const [loadingBacktest, setLoadingBacktest] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTimeframeBT, setSelectedTimeframeBT] = useState("1h");
  const [selectedTimezoneBT, setSelectedTimezoneBT] = useState("America/New_York");
  const [startingBalance, setStartingBalance] = useState(10000);
  const [showDuplicatePopup, setShowDuplicatePopup] = useState(false);
  const [isDescriptionDialogOpen, setIsDescriptionDialogOpen] = useState(false);
  const [generatedStrategy, setGeneratedStrategy] = useState(null);
  const [activeTab, setActiveTab] = useState('strategy');
  // Initialize editingStrategyId from localStorage if available
  const [editingStrategyId, setEditingStrategyId] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem("editingStrategyId") || null;
    }
    return null;
  });
  const [newIndicator, setNewIndicator] = useState({
    type: "",
    parameters: {},
    source: "price" // default to price
  });

  // Add this with other useState declarations at the top
  const [resultsRef, setResultsRef] = useState(null);

  // Add this state variable
  const [isStrategyValid, setIsStrategyValid] = useState(false);

  // Add new state for animations
  const [isAnimating, setIsAnimating] = useState(false);
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  // Add new state for entry rule modal
  const [isEntryRuleModalOpen, setIsEntryRuleModalOpen] = useState(false);
  const [newEntryRule, setNewEntryRule] = useState({
    id: '',
    tradeType: 'long',
    indicator1: '',
    operator: 'Crossing above',
    compareType: 'value',
    indicator2: '',
    value: '',
    barRef: 'close', // Add bar reference field with default value
    band: undefined, // Add band property for Bollinger Bands (first indicator)
    band2: undefined, // Add band2 property for Bollinger Bands (second indicator)
    macdComponent: undefined, // Add macdComponent property for MACD (first indicator)
    macdComponent2: undefined // Add macdComponent2 property for MACD (second indicator)
  });

  // Add new state for exit rule modal
  const [isExitRuleModalOpen, setIsExitRuleModalOpen] = useState(false);
  const [newExitRule, setNewExitRule] = useState({
    id: '',
    tradeType: 'long',
    indicator1: '',
    operator: 'Crossing above',
    compareType: 'value',
    indicator2: '',
    value: '',
    barRef: 'close', // Add bar reference field with default value
    band: undefined, // Add band property for Bollinger Bands (first indicator)
    band2: undefined, // Add band2 property for Bollinger Bands (second indicator)
    macdComponent: undefined, // Add macdComponent property for MACD (first indicator)
    macdComponent2: undefined // Add macdComponent2 property for MACD (second indicator)
  });

  const [showSaveNotification, setShowSaveNotification] = useState(false);
  const [isUpdateNotification, setIsUpdateNotification] = useState(false);
  const [isSavingStrategy, setIsSavingStrategy] = useState(false);

  // Add this with other state declarations
  const [isBacktestDialogOpen, setIsBacktestDialogOpen] = useState(false);

  // Rename the state variable to avoid conflict with the array
  const [selectedTradingSessions, setSelectedTradingSessions] = useState([]);

  // Add handler for trading session changes
  const handleTradingSessionChange = (e) => {
    handleStrategyModification();
    const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);

    // If "All" is selected, clear other selections
    if (selectedOptions.includes("All")) {
      setSelectedTradingSessions(["All"]);
    } else {
      // Remove "All" if it was previously selected
      setSelectedTradingSessions(selectedOptions.filter(option => option !== "All"));
    }
  };

  // Update the effect that handles strategyId from query params
  useEffect(() => {
    const { strategyId } = router.query;
    if (strategyId && firebaseUser?.uid && savedStrategies.length > 0) {
      const strat = savedStrategies.find(s => s.id.toString() === strategyId);
      if (strat) {
        const s = JSON.parse(strat.strategy_json);
        // Set basic information
        setStrategyName(s.name);
        setStrategyDescription(s.description || "");
        setForexPair(s.instruments);
        setTimeframe(s.timeframe);
        setTimezone(s.TimeZone);

        // Set risk management
        setRiskPercentage(s.riskManagement.riskPercentage || "");
        setRiskRewardRatio(s.riskManagement.riskRewardRatio || "");
        setStopLossMethod(s.riskManagement.stopLossMethod || "fixed");
        setFixedPips(s.riskManagement.fixedPips || "");

        // Set indicator-based stop loss settings if present
        if (s.riskManagement.stopLossMethod === 'indicator' && s.riskManagement.indicatorBasedSL) {
          setIndicatorBasedSL(s.riskManagement.indicatorBasedSL.indicator || "");
          if (s.riskManagement.indicatorBasedSL.parameters && Object.keys(s.riskManagement.indicatorBasedSL.parameters).length > 0) {
            setIndicatorParams(s.riskManagement.indicatorBasedSL.parameters);
          } else {
            setIndicatorParams(getDefaultIndicatorParams(s.riskManagement.indicatorBasedSL.indicator || ""));
          }
        } else {
          setIndicatorBasedSL("");
          setIndicatorParams({});
        }

        // Set lot size if present
        setLotSize(s.riskManagement.lotSize || "");

        // Set indicators and rules
        setSelectedIndicators(s.indicators || []);
        setEntryRules(s.entryRules || []);
        setExitRules(s.exitRules || []);

        // Convert trading sessions to timezone values for the select input
        const sessions = Array.isArray(s.tradingSession) ? s.tradingSession : [s.tradingSession];
        const sessionTimezones = sessions.map(sessionName => {
          if (sessionName === "All") return "All";
          const session = tradingSessions.find(ts => ts.name === sessionName);
          return session ? session.timezone : sessionName;
        });
        setSelectedTradingSessions(sessionTimezones);

        setGeneratedStrategy(s);
        setEditingStrategyId(strat.id);
        setIsStrategyFinalized(true);
        setHasBeenModified(false);
      }
    }
  }, [router.query, firebaseUser, savedStrategies]);

  // When a stored strategy is selected, update the form state.
  useEffect(() => {
    if (selectedStoredStrategyId && savedStrategies.length > 0) {
      const strat = savedStrategies.find(
        (s) => s.id.toString() === selectedStoredStrategyId
      );
      if (strat) {
        const s = JSON.parse(strat.strategy_json);
        setStrategyName(s.name);
        setForexPair(s.instruments);
        setTimeframe(s.timeframe);
        setTimezone(s.TimeZone);
        setStopLoss(s.riskManagement.stopLoss);
        setRiskPercentage(s.riskManagement.riskPercentage);
        setRiskRewardRatio(s.riskManagement.riskRewardRatio);
        setSelectedIndicators(s.indicators || []);
        setEntryRules(s.entryRules || []);
        setExitRules(s.exitRules || []);
        setGeneratedStrategy(s);
        setEditingStrategyId(strat.id);
      }
    }
  }, [selectedStoredStrategyId, savedStrategies]);

  // Add a function to check if strategy is saved
  const checkIfStrategyIsSaved = useCallback(() => {
    if (!strategyName || !savedStrategies) return false;
    return savedStrategies.some(
      (s) => s.name.toLowerCase() === strategyName.trim().toLowerCase()
    );
  }, [strategyName, savedStrategies]);

  // Update the effect to check if strategy is saved when name or savedStrategies changes
  useEffect(() => {
    setIsStrategySaved(checkIfStrategyIsSaved());
  }, [strategyName, savedStrategies, checkIfStrategyIsSaved]);

  // Add a debug effect to monitor key state changes
  useEffect(() => {
    console.log("State change detected:");
    console.log(`- editingStrategyId: ${editingStrategyId}`);
    console.log(`- hasBeenModified: ${hasBeenModified}`);
    console.log(`- isStrategySaved: ${isStrategySaved}`);

    // This is where we determine if we should show "Update Strategy" or "Save Strategy"
    const shouldShowUpdateButton = editingStrategyId && (hasBeenModified || !isStrategySaved);
    console.log(`- Should show "Update Strategy" button: ${shouldShowUpdateButton}`);

    // Store editingStrategyId in localStorage to ensure it persists
    if (editingStrategyId) {
      localStorage.setItem("editingStrategyId", editingStrategyId);
    }
  }, [editingStrategyId, hasBeenModified, isStrategySaved]);

  // Add getSourceOptions helper function after the existing helper functions
  const getSourceOptions = () => {
    const baseOptions = [
      { value: "close", label: "Close" },
      { value: "open", label: "Open" },
      { value: "high", label: "High" },
      { value: "low", label: "Low" },
      { value: "volume", label: "Volume" }
    ];

    // Add existing indicators as source options
    const indicatorOptions = selectedIndicators.map(ind => ({
      value: ind.id,
      label: `${ind.type} (${Object.entries(ind.parameters)
        .map(([k, v]) => `${k}: ${v}`)
        .join(", ")})`
    }));

    return [...baseOptions, ...indicatorOptions];
  };

  // Update the handleNewIndicatorTypeChange function
  const handleNewIndicatorTypeChange = (e) => {
    handleStrategyModification();
    const type = e.target.value;
    if (!type) {
      setNewIndicator({ type: "", parameters: {}, source: "price" });
      return;
    }
    const found = supportedIndicators.find((ind) => ind.name === type);
    setNewIndicator({
      type,
      parameters: { ...found.defaultParameters },
      source: "price" // Reset source to price when changing indicator type
    });
  };

  const handleNewIndicatorParamChange = (paramKey, value) => {
    handleStrategyModification();
    setNewIndicator((prev) => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [paramKey]: Number(value)
      },
    }));
  };

  // Update the addNewIndicator function
  const addNewIndicator = () => {
    handleStrategyModification();
    if (!newIndicator.type) return;
    const instance = {
      id: generateId(),
      type: newIndicator.type,
      parameters: { ...newIndicator.parameters },
      source: newIndicator.source
    };
    setSelectedIndicators((prev) => [...prev, instance]);
    setNewIndicator({ type: "", parameters: {}, source: "price" });
  };

  // --- Selected Indicators Handlers ---
  const removeSelectedIndicator = (id) => {
    handleStrategyModification();
    setSelectedIndicators((prev) => prev.filter((ind) => ind.id !== id));
  };

  // --- Conditions Handlers ---
  const handleAddEntryRule = () => {
    handleStrategyModification();
    if (!newEntryRule.indicator1) return;

    // Check if this is a Bollinger Bands indicator and set a default band if not specified
    const isBollingerBands1 = selectedIndicators.find(
      ind => ind.id === newEntryRule.indicator1 && ind.type === 'BollingerBands'
    );

    // Check if second indicator is Bollinger Bands
    const isBollingerBands2 = newEntryRule.compareType === 'indicator' && selectedIndicators.find(
      ind => ind.id === newEntryRule.indicator2 && ind.type === 'BollingerBands'
    );

    // Check if this is a MACD indicator and set a default component if not specified
    const isMacd1 = selectedIndicators.find(
      ind => ind.id === newEntryRule.indicator1 && ind.type === 'MACD'
    );

    // Check if second indicator is MACD
    const isMacd2 = newEntryRule.compareType === 'indicator' && selectedIndicators.find(
      ind => ind.id === newEntryRule.indicator2 && ind.type === 'MACD'
    );

    const ruleToAdd = {
      ...newEntryRule,
      id: generateId(),
      // Set default band to 'middle' for Bollinger Bands if not specified
      band: isBollingerBands1 && !newEntryRule.band ? 'middle' : newEntryRule.band,
      // Set default band2 to 'middle' for second Bollinger Bands indicator if not specified
      band2: isBollingerBands2 && !newEntryRule.band2 ? 'middle' : newEntryRule.band2,
      // Set default macdComponent to 'macd' for MACD if not specified
      macdComponent: isMacd1 && !newEntryRule.macdComponent ? 'macd' : newEntryRule.macdComponent,
      // Set default macdComponent2 to 'macd' for second MACD indicator if not specified
      macdComponent2: isMacd2 && !newEntryRule.macdComponent2 ? 'macd' : newEntryRule.macdComponent2
    };

    console.log("Adding entry rule:", ruleToAdd);
    setEntryRules(prev => [...prev, ruleToAdd]);

    setNewEntryRule({
      id: '',
      tradeType: 'long',
      indicator1: '',
      operator: 'Crossing above',
      compareType: 'value',
      indicator2: '',
      value: '',
      barRef: 'close', // Add bar reference field with default value
      band: undefined, // Reset band
      band2: undefined, // Reset band2
      macdComponent: undefined, // Reset macdComponent
      macdComponent2: undefined // Reset macdComponent2
    });
    setIsEntryRuleModalOpen(false);
  };

  const addEntryRule = () => {
    setIsEntryRuleModalOpen(true);
  };

  const removeEntryRule = (index) => {
    handleStrategyModification();
    setEntryRules((prev) => prev.filter((_, i) => i !== index));
  };

  // Compute grouping counts.
  const entryLongCount = entryRules.filter((r) => r.tradeType === "long").length;
  const entryShortCount = entryRules.filter(
    (r) => r.tradeType === "short"
  ).length;
  const exitLongCount = exitRules.filter((r) => r.tradeType === "long").length;
  const exitShortCount = exitRules.filter((r) => r.tradeType === "short").length;

  // Helper: Given a value ("price" or an indicator id), return a label.
  const getIndicatorLabel = (val, rule, isSecondIndicator = false) => {
    console.log(`getIndicatorLabel called with val=${val}, isSecondIndicator=${isSecondIndicator}, rule=`, rule);

    if (val === "price") return "Price";

    // First try to find in selectedIndicators (for new/edited strategies)
    const foundInSelected = selectedIndicators.find((ind) => ind.id === val);
    if (foundInSelected) {
      // Check if this is a Bollinger Bands indicator and we need to specify which band
      if (foundInSelected.type === 'BollingerBands') {
        // Use band2 for second indicator, band for first indicator
        const bandProperty = isSecondIndicator ? rule?.band2 : rule?.band;

        if (bandProperty) {
          const bandLabel = bandProperty === 'upper' ? 'Upper Band' :
                           bandProperty === 'lower' ? 'Lower Band' :
                           'Middle Band';
          return `${foundInSelected.type} ${bandLabel} (${Object.entries(foundInSelected.parameters)
            .map(([k, v]) => `${k}: ${v}`)
            .join(", ")})`;
        }
      }

      // Check if this is a MACD indicator and we need to specify which component
      if (foundInSelected.type === 'MACD') {
        // Special case for MACD crossover rules (same indicator for both indicator1 and indicator2)
        if (rule && rule.compareType === 'indicator' && rule.indicator1 === rule.indicator2) {
          // This is a MACD crossover rule
          // For MACD crossovers, always show the component name regardless of whether we're looking at indicator1 or indicator2
          const macdComponentProperty = isSecondIndicator ? rule.macdComponent2 : rule.macdComponent;
          if (macdComponentProperty) {
            const componentLabel = macdComponentProperty === 'signal' ? 'Signal Line' : 'MACD Line';
            return `${foundInSelected.type} ${componentLabel} (${Object.entries(foundInSelected.parameters)
              .map(([k, v]) => `${k}: ${v}`)
              .join(", ")})`;
          }
        }

        // Use macdComponent2 for second indicator, macdComponent for first indicator
        const macdComponentProperty = isSecondIndicator ? rule?.macdComponent2 : rule?.macdComponent;
        if (macdComponentProperty) {
          const componentLabel = macdComponentProperty === 'signal' ? 'Signal Line' : 'MACD Line';
          return `${foundInSelected.type} ${componentLabel} (${Object.entries(foundInSelected.parameters)
            .map(([k, v]) => `${k}: ${v}`)
            .join(", ")})`;
        }

        // If we're here, we have a MACD indicator but no component specified
        // Try to find the rule in entryRules or exitRules that uses this indicator
        if (!rule) {
          // Look for a rule that uses this indicator
          const entryRule = entryRules.find(r =>
            (r.indicator1 === val && r.compareType === 'indicator' && r.indicator2 === val) ||
            (r.indicator2 === val && r.compareType === 'indicator' && r.indicator1 === val)
          );

          const exitRule = exitRules.find(r =>
            (r.indicator1 === val && r.compareType === 'indicator' && r.indicator2 === val) ||
            (r.indicator2 === val && r.compareType === 'indicator' && r.indicator1 === val)
          );

          const matchingRule = entryRule || exitRule;

          if (matchingRule) {
            // This is a MACD crossover rule
            // Determine if we're showing the first or second indicator based on the context
            const isSecond = isSecondIndicator || false;
            const componentProperty = isSecond ? matchingRule.macdComponent2 : matchingRule.macdComponent;
            if (componentProperty) {
              const componentLabel = componentProperty === 'signal' ? 'Signal Line' : 'MACD Line';
              return `${foundInSelected.type} ${componentLabel} (${Object.entries(foundInSelected.parameters)
                .map(([k, v]) => `${k}: ${v}`)
                .join(", ")})`;
            }
          }
        }

        // If we still don't have a component, default to showing both components
        // This is a fallback for when we can't determine which component to show
        return `${foundInSelected.type} (${Object.entries(foundInSelected.parameters)
          .map(([k, v]) => `${k}: ${v}`)
          .join(", ")})`;
      }

      return `${foundInSelected.type} (${Object.entries(foundInSelected.parameters)
        .map(([k, v]) => `${k}: ${v}`)
        .join(", ")})`;
    }

    // If not found in selectedIndicators, try to find in generatedStrategy (for loaded strategies)
    if (generatedStrategy && generatedStrategy.indicators) {
      // First try exact match
      const foundInGenerated = generatedStrategy.indicators.find((ind) => ind.id === val);
      if (foundInGenerated) {
        // Check if this is a Bollinger Bands indicator and we need to specify which band
        const indType = foundInGenerated.type || foundInGenerated.indicator_class;
        if (indType === 'BollingerBands') {
          // Use band2 for second indicator, band for first indicator
          const bandProperty = isSecondIndicator ? rule?.band2 : rule?.band;

          if (bandProperty) {
            const bandLabel = bandProperty === 'upper' ? 'Upper Band' :
                             bandProperty === 'lower' ? 'Lower Band' :
                             'Middle Band';
            return `${indType} ${bandLabel} (${Object.entries(foundInGenerated.parameters)
              .map(([k, v]) => `${k}: ${v}`)
              .join(", ")})`;
          }
        }

        // Check if this is a MACD indicator and we need to specify which component
        if (indType === 'MACD') {
          // Use macdComponent2 for second indicator, macdComponent for first indicator
          const macdComponentProperty = isSecondIndicator ? rule?.macdComponent2 : rule?.macdComponent;

          if (macdComponentProperty) {
            const componentLabel = macdComponentProperty === 'signal' ? 'Signal Line' : 'MACD Line';
            return `${indType} ${componentLabel} (${Object.entries(foundInGenerated.parameters)
              .map(([k, v]) => `${k}: ${v}`)
              .join(", ")})`;
          }
        }

        return `${indType} (${Object.entries(foundInGenerated.parameters)
          .map(([k, v]) => `${k}: ${v}`)
          .join(", ")})`;
      }

      // If no exact match, try to match by type and period
      const [indicatorType, period] = val.split('_');
      const foundByType = generatedStrategy.indicators.find(ind => {
        const indType = ind.type || ind.indicator_class;
        const indPeriod = ind.parameters?.period;
        return indType === indicatorType && indPeriod?.toString() === period;
      });
      if (foundByType) {
        // Check if this is a Bollinger Bands indicator and we need to specify which band
        const indType = foundByType.type || foundByType.indicator_class;
        if (indType === 'BollingerBands') {
          // Use band2 for second indicator, band for first indicator
          const bandProperty = isSecondIndicator ? rule?.band2 : rule?.band;

          if (bandProperty) {
            const bandLabel = bandProperty === 'upper' ? 'Upper Band' :
                             bandProperty === 'lower' ? 'Lower Band' :
                             'Middle Band';
            return `${indType} ${bandLabel} (${Object.entries(foundByType.parameters)
              .map(([k, v]) => `${k}: ${v}`)
              .join(", ")})`;
          }
        }

        // Check if this is a MACD indicator and we need to specify which component
        if (indType === 'MACD') {
          // Use macdComponent2 for second indicator, macdComponent for first indicator
          const macdComponentProperty = isSecondIndicator ? rule?.macdComponent2 : rule?.macdComponent;

          if (macdComponentProperty) {
            const componentLabel = macdComponentProperty === 'signal' ? 'Signal Line' : 'MACD Line';
            return `${indType} ${componentLabel} (${Object.entries(foundByType.parameters)
              .map(([k, v]) => `${k}: ${v}`)
              .join(", ")})`;
          }
        }

        return `${indType} (${Object.entries(foundByType.parameters)
          .map(([k, v]) => `${k}: ${v}`)
          .join(", ")})`;
      }
    }

    return "N/A";
  };

  // Add these helper functions after the existing helper functions
  const getEntryLongCount = (rules) => rules.filter(rule => rule.tradeType === "long").length;
  const getEntryShortCount = (rules) => rules.filter(rule => rule.tradeType === "short").length;
  const getExitLongCount = (rules) => rules.filter(rule => rule.tradeType === "long").length;
  const getExitShortCount = (rules) => rules.filter(rule => rule.tradeType === "short").length;

  // Helper function to display rule in Trading Rules section
  const displayRule = (rule) => {
    return (
      <>
        {getIndicatorLabel(rule.indicator1, rule)}
        {' '}
        <span className="text-[#EFBD3A]">{rule.operator}</span>
        {' '}
        {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
        {rule.indicator1 === 'price' && (
          <>
            {' '}
            <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
          </>
        )}
      </>
    );
  };

  // Function to render a rule in the Trading Rules section
  const renderRule = (rule) => (
    <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#1a1a1a] p-2 rounded">
      {getIndicatorLabel(rule.indicator1, rule)}
      {' '}
      <span className="text-[#EFBD3A]">{rule.operator}</span>
      {' '}
      {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
      {rule.indicator1 === 'price' && (
        <>
          {' '}
          <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
        </>
      )}
    </div>
  );

  // --- Form Submission ---
  const handleSubmit = async (e) => {
    if (e && e.preventDefault) e.preventDefault();

    // Validate required fields
    const isValid = !!(
      strategyName.trim() &&
      forexPair.trim() &&
      selectedIndicators.length > 0 &&
      entryRules.length > 0 &&
      exitRules.length > 0
    );

    if (!isValid) {
      alert(
        "Please complete all required fields: Strategy Name, Forex Pair, at least one indicator, one entry rule, and one exit rule."
      );
      setIsStrategyValid(false);
      setIsStrategyFinalized(false);
      return;
    }

    const strategyPayload = {
      name: strategyName,
      instruments: forexPair,
      timeframe: timeframe,
      indicators: selectedIndicators.map((ind) => ({
        id: ind.id,
        indicator_class: ind.type,
        type: ind.type,
        parameters: { ...ind.parameters },
        source: ind.source
      })),
      entryRules: entryRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close' // Ensure barRef is included
      })),
      exitRules: exitRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close' // Ensure barRef is included
      })),
      riskManagement: {
        stopLoss,
        riskPercentage,
        riskRewardRatio,
        stopLossUnit,
        takeProfitUnit: "percentage",
        stopLossMethod,
        fixedPips: stopLossMethod === 'fixed' ? String(fixedPips || "50") : undefined,
        indicatorBasedSL: stopLossMethod === 'indicator' ? {
          indicator: indicatorBasedSL,
          parameters: indicatorParams
        } : undefined,
        lotSize: stopLossMethod === 'risk' ? lotSize : undefined
      },
      tradingSession: selectedTradingSessions.map(session =>
        tradingSessions.find(s => s.timezone === session)?.name || session
      ),
      ...(entryLongCount > 1 && { entryLongGroupOperator: entryBuyGroupOperator }),
      ...(entryShortCount > 1 && { entryShortGroupOperator: entrySellGroupOperator }),
      ...(exitLongCount > 1 && { exitLongGroupOperator: exitBuyGroupOperator }),
      ...(exitShortCount > 1 && { exitShortGroupOperator: exitSellGroupOperator })
    };

    try {
      console.log("Strategy generated successfully:", strategyPayload);
      setMessage("Strategy generated successfully!");
      setGeneratedStrategy(strategyPayload);
      setIsStrategyValid(true);
      setIsStrategyFinalized(true);
      setHasBeenModified(false); // Reset modification flag when strategy is finalized
    } catch (error) {
      console.error("Error submitting strategy:", error);
      setMessage("Error submitting strategy");
      setIsStrategyValid(false);
      setIsStrategyFinalized(false);
    }
  };

  // --- Save Strategy Handler using Firebase Functions & Firestore ---
  const handleSaveStrategy = async () => {
    console.log("%c handleSaveStrategy called", "background: #333; color: #ff0; font-weight: bold; padding: 2px 5px;");

    // Get the effective editing ID from state or localStorage
    const effectiveEditingId = editingStrategyId || localStorage.getItem("editingStrategyId");
    console.log(`Current editingStrategyId: ${editingStrategyId}`);
    console.log(`localStorage editingStrategyId: ${localStorage.getItem("editingStrategyId")}`);
    console.log(`Effective editing ID: ${effectiveEditingId}`);

    // Validate required fields
    const requiredFields = {
      'Strategy Name': strategyName,
      'Forex Pair': forexPair,
      'Timeframe': timeframe,
      'Trading Sessions': selectedTradingSessions.length > 0,
      'Indicators': selectedIndicators.length > 0,
      'Entry Rules': entryRules.length > 0,
      'Exit Rules': exitRules.length > 0
    };

    const missingFields = Object.entries(requiredFields)
      .filter(([_, value]) => !value)
      .map(([field]) => field);

    if (missingFields.length > 0) {
      alert(`Please complete the following required fields: ${missingFields.join(', ')}`);
      return;
    }

    setIsSavingStrategy(true);
    setSaveSuccessMessage("");

    try {
      const strategyData = {
        name: strategyName,
        description: strategyDescription,
        forexPair,
        timeframe,
        timezone,
        tradingSession: selectedTradingSessions,
        indicators: selectedIndicators,
        entryRules,
        exitRules,
        riskManagement: {
          stopLoss,
          stopLossUnit,
          riskPercentage,
          riskRewardRatio,
          takeProfitUnit: "percentage",
          stopLossMethod,
          fixedPips: stopLossMethod === 'fixed' ? String(fixedPips || "50") : undefined,
          indicatorBasedSL: stopLossMethod === 'indicator' ? {
            indicator: indicatorBasedSL,
            parameters: indicatorParams
          } : undefined,
          lotSize: stopLossMethod === 'risk' ? lotSize : undefined
        }
      };

      // Determine if we're updating or saving a new strategy
      if (effectiveEditingId) {
        console.log(`Updating existing strategy with ID: ${effectiveEditingId}`);
        await updateStrategy(effectiveEditingId, strategyData);
      } else {
        console.log("Saving new strategy");
        const result = await saveNewStrategy(strategyData);

        // If a new strategy was saved, set the editingStrategyId
        if (result && result.strategyId) {
          console.log(`Setting editingStrategyId to: ${result.strategyId}`);
          setEditingStrategyId(result.strategyId);
          localStorage.setItem("editingStrategyId", result.strategyId);
        }
      }

      // Set success state
      setIsStrategySaved(true);
      setHasBeenModified(false);

      // Update localStorage
      localStorage.setItem("hasBeenModified", JSON.stringify(false));
      localStorage.setItem("isStrategySaved", JSON.stringify(true));

      // Set success message
      const actionText = effectiveEditingId ? "updated" : "saved";
      setSaveSuccessMessage(`Strategy successfully ${actionText}! You can find it in the Strategy Library.`);

      // Clear success message after 5 seconds
      setTimeout(() => {
        setSaveSuccessMessage("");
      }, 5000);

    } catch (error) {
      console.error("Error saving/updating strategy:", error);
      const actionText = effectiveEditingId ? "updating" : "saving";
      alert(`Error ${actionText} strategy: ${error.message}`);
    } finally {
      setIsSavingStrategy(false);
    }
  };

  // --- Backtest Handlers ---
  const handleOpenModal = () => setIsModalOpen(true);
  const handleConfirmBacktest = () => {
    handleBacktest();
    setIsModalOpen(false);
  };
  const handleBacktest = async () => {
    try {
      setIsLoading(true);
      setBacktestResults(null);
      setError(null);

      // Validate strategy before backtesting
      if (!strategy.name || !strategy.instruments || !strategy.timeframe) {
        setError("Please complete the strategy configuration before backtesting.");
        setIsLoading(false);
        return;
      }

      if (!strategy.indicators || strategy.indicators.length === 0) {
        setError("Please add at least one indicator to your strategy.");
        setIsLoading(false);
        return;
      }

      if (!strategy.entryRules || strategy.entryRules.length === 0) {
        setError("Please add at least one entry rule to your strategy.");
        setIsLoading(false);
        return;
      }

      if (!strategy.exitRules || strategy.exitRules.length === 0) {
        setError("Please add at least one exit rule to your strategy.");
        setIsLoading(false);
        return;
      }

      // Transform entry and exit rules to ensure band and band2 properties are preserved
      const transformedEntryRules = strategy.entryRules.map(rule => ({
        ...rule,
        trade_type: rule.tradeType.toLowerCase(),
        indicator1: rule.indicator1,
        operator: rule.operator,
        compare_type: rule.compareType,
        indicator2: rule.indicator2 || null,
        value: rule.value || null,
        logical_operator: rule.logicalOperator || "AND",
        bar_ref: rule.barRef || "close",
        band: rule.band || null,  // Preserve band property for Bollinger Bands (first indicator)
        band2: rule.band2 || null  // Preserve band2 property for Bollinger Bands (second indicator)
      }));

      const transformedExitRules = strategy.exitRules.map(rule => ({
        ...rule,
        trade_type: rule.tradeType.toLowerCase(),
        indicator1: rule.indicator1,
        operator: rule.operator,
        compare_type: rule.compareType,
        indicator2: rule.indicator2 || null,
        value: rule.value || null,
        logical_operator: rule.logicalOperator || "AND",
        bar_ref: rule.barRef || "close",
        band: rule.band || null,  // Preserve band property for Bollinger Bands (first indicator)
        band2: rule.band2 || null  // Preserve band2 property for Bollinger Bands (second indicator)
      }));

      // Add trading costs to strategy (default values if not set)
      const strategyWithCosts = {
        ...strategy,
        entry_rules: transformedEntryRules,
        exit_rules: transformedExitRules,
        tradingCosts: {
          spreadPips: 1.0,  // Default 1 pip spread
          commissionPercentage: 0.0,  // Default no percentage commission
          commissionFixed: 0.0  // Default no fixed commission
        }
      };

      // Call the new backtest endpoint
      const response = await fetch(`${process.env.REACT_APP_API_URL}/run_oryn_backtest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          strategy: strategyWithCosts
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to run backtest');
      }

      const results = await response.json();

      // Format dates in trades
      const formattedResults = {
        ...results,
        trades: results.trades.map(trade => ({
          ...trade,
          entry_time: new Date(trade.entry_time),
          exit_time: new Date(trade.exit_time)
        }))
      };

      setBacktestResults(formattedResults);
      setActiveTab('results');  // Switch to results tab

      // Show success message
      toast.success('Backtest completed successfully!', {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

    } catch (error) {
      console.error('Backtest error:', error);
      setError(error.message || 'Failed to run backtest');

      // Show error message
      toast.error(error.message || 'Failed to run backtest', {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStrategyGenerated = (response) => {
    console.log("Received strategy from AI:", response);

    // Handle both nested and flat response formats
    const strategy = response.strategy || response;

    // Store the description
    setStrategyDescription(response.description || "");

    // Basic Information
    setStrategyName(strategy.name || strategy.strategyName || "");
    setForexPair(strategy.instruments || "");

    // Time Settings
    setTimeframe(strategy.timeframe || "");
    setTimezone(strategy.TimeZone || "UTC");

    // Handle trading sessions
    const tradingSessions = strategy.tradingSession || ["All"];
    const selectedTimezones = tradingSessions.map(sessionName => {
      // If "All" is selected, keep it as is
      if (sessionName === "All") {
        return "All";
      }

      // Convert session names to corresponding timezone values
      switch(sessionName.toLowerCase()) {
        case "london":
          return "Europe/London";
        case "new york":
        case "new_york":
          return "America/New_York";
        case "tokyo":
          return "Asia/Tokyo";
        case "sydney":
          return "Australia/Sydney";
        default:
          return sessionName; // Keep original value if unknown
      }
    });
    console.log("Setting trading sessions:", tradingSessions, "->", selectedTimezones);
    setSelectedTradingSessions(selectedTimezones);

    // Indicators
    setSelectedIndicators((strategy.indicators || []).map(indicator => ({
      id: indicator.id || generateId(),
      type: indicator.indicator_class || indicator.type,
      parameters: indicator.parameters || {},
      source: indicator.source || "price"
    })));

    // Entry Rules
    const entryRulesArray = strategy.entry_rules || strategy.entryRules || [];
    const processedEntryRules = entryRulesArray.map(rule => {
      const newRule = {
        id: rule.id || generateId(),
        tradeType: rule.tradeType.toLowerCase(),
        indicator1: rule.indicator1,
        operator: rule.operator,
        compareType: rule.compareType,
        indicator2: rule.indicator2 || "",
        value: rule.value || "",
        logicalOperator: rule.logicalOperator || "AND",
        barRef: rule.barRef || "close",
        band: rule.band, // Preserve band property for Bollinger Bands (first indicator)
        band2: rule.band2, // Preserve band2 property for Bollinger Bands (second indicator)
        macdComponent: rule.macdComponent, // Preserve macdComponent property for MACD (first indicator)
        macdComponent2: rule.macdComponent2 // Preserve macdComponent2 property for MACD (second indicator)
      };

      // Check if this is a MACD crossover rule (same indicator for both indicator1 and indicator2)
      if (rule.compareType === 'indicator' && rule.indicator1 === rule.indicator2) {
        // Find the indicator
        const indicator = (strategy.indicators || []).find(ind => ind.id === rule.indicator1);
        if (indicator && (indicator.indicator_class === 'MACD' || indicator.type === 'MACD')) {
          // This is a MACD crossover rule
          // Set default components if not specified
          if (!newRule.macdComponent) newRule.macdComponent = 'macd';
          if (!newRule.macdComponent2) newRule.macdComponent2 = 'signal';
        }
      }

      return newRule;
    });
    setEntryRules(processedEntryRules);

    // Exit Rules
    const exitRulesArray = strategy.exit_rules || strategy.exitRules || [];
    const processedExitRules = exitRulesArray.map(rule => {
      const newRule = {
        id: rule.id || generateId(),
        tradeType: rule.tradeType.toLowerCase(),
        indicator1: rule.indicator1,
        operator: rule.operator,
        compareType: rule.compareType,
        indicator2: rule.indicator2 || "",
        value: rule.value || "",
        logicalOperator: rule.logicalOperator || "AND",
        barRef: rule.barRef || "close",
        band: rule.band, // Preserve band property for Bollinger Bands (first indicator)
        band2: rule.band2, // Preserve band2 property for Bollinger Bands (second indicator)
        macdComponent: rule.macdComponent, // Preserve macdComponent property for MACD (first indicator)
        macdComponent2: rule.macdComponent2 // Preserve macdComponent2 property for MACD (second indicator)
      };

      // Check if this is a MACD crossover rule (same indicator for both indicator1 and indicator2)
      if (rule.compareType === 'indicator' && rule.indicator1 === rule.indicator2) {
        // Find the indicator
        const indicator = (strategy.indicators || []).find(ind => ind.id === rule.indicator1);
        if (indicator && (indicator.indicator_class === 'MACD' || indicator.type === 'MACD')) {
          // This is a MACD crossover rule
          // Set default components if not specified
          if (!newRule.macdComponent) newRule.macdComponent = 'macd';
          if (!newRule.macdComponent2) newRule.macdComponent2 = 'signal';
        }
      }

      return newRule;
    });
    setExitRules(processedExitRules);

    // Risk Management
    if (strategy.riskManagement) {
      const { stopLoss, riskPercentage, riskRewardRatio, stopLossUnit, takeProfitUnit } = strategy.riskManagement;
      setStopLoss(stopLoss);
      setRiskPercentage(riskPercentage);
      setRiskRewardRatio(riskRewardRatio);
      setStopLossUnit(stopLossUnit || "percentage");
      setTakeProfitUnit(takeProfitUnit || "percentage");
    }

    // Group Operators
    setEntryLongGroupOperator(strategy.entryLongGroupOperator || "AND");
    setExitLongGroupOperator(strategy.exitLongGroupOperator || "OR");

    // Set current step to 1 for review
    setCurrentStep(1);

    // Mark strategy as modified
    setHasBeenModified(true);
  };

  // Function to clear the strategy form
  const clearStrategyForm = () => {
    // Clear editing state
    setEditingStrategyId(null);
    localStorage.removeItem("editingStrategyId"); // Also remove from localStorage
    setSelectedStoredStrategyId(null);

    // Basic Information
    setStrategyName("");
    setStrategyDescription("");
    setForexPair("");
    setIsForexPairValid(true);
    setIsForexPairExists(true);
    setForexPairError("");

    // Time Settings
    setTimeframe("1h");
    setTimezone("UTC");
    setSelectedTradingSessions(["All"]);

    // Indicators
    setSelectedIndicators([]);
    setNewIndicator({
      type: "",
      parameters: {},
      source: "price"
    });

    // Entry Rules
    setEntryRules([]);
    setNewEntryRule({
      tradeType: "long",
      indicator1: "",
      operator: "Crossing above",
      compareType: "value",
      indicator2: "",
      value: "",
      logicalOperator: "AND",
      barRef: "close",
      band: undefined, // Reset band property
      band2: undefined // Reset band2 property
    });
    setIsEntryRuleModalOpen(false);

    // Exit Rules
    setExitRules([]);
    setNewExitRule({
      tradeType: "long",
      indicator1: "",
      operator: "Crossing above",
      compareType: "value",
      indicator2: "",
      value: "",
      logicalOperator: "AND",
      barRef: "close",
      band: undefined, // Reset band property
      band2: undefined // Reset band2 property
    });
    setIsExitRuleModalOpen(false);

    // Risk Management
    setRiskPercentage("");
    setRiskRewardRatio("");
    setStopLossMethod("fixed");
    setFixedPips("");
    setIndicatorBasedSL("");
    setLotSize("");
    setStopLoss("");
    setStopLossUnit("pips");

    // Reset all indicator states
    // ATR
    setIsAtrAddedToChart(false);
    setIsAddingAtrToChart(false);
    setPendingAtrIndicator(null);

    // Bollinger Bands
    setIsBollingerAddedToChart(false);
    setIsAddingBollingerToChart(false);
    setPendingBollingerIndicator(null);

    // Support & Resistance
    setIsSRAddedToChart(false);
    setIsAddingSRToChart(false);
    setPendingSRIndicator(null);

    // Remove all indicator types
    setSelectedIndicators(prev =>
      prev.filter(ind => !(
        ind.type === 'ATR' ||
        ind.type === 'BollingerBands' ||
        ind.type === 'SupportResistance'
      ))
    );

    // Group Operators
    setEntryBuyGroupOperator("AND");
    setEntrySellGroupOperator("AND");
    setExitBuyGroupOperator("OR");
    setExitSellGroupOperator("OR");

    // Strategy State
    setGeneratedStrategy(null);
    setIsStrategyFinalized(false);
    setIsStrategyValid(false);
    setHasBeenModified(false);
    setIsStrategySaved(false);

    // Clear any backtest related states
    setBacktestResults(null);
    setBacktestStatus(null);
    setBacktestError(null);
    setLoadingBacktest(false);
    setIsBacktestDialogOpen(false);

    // Clear any modals/dialogs
    setIsModalOpen(false);
    setIsStrategyDescriptionOpen(false);
    setShowDuplicatePopup(false);
    setIsDescriptionDialogOpen(false);

    // Reset to first step
    setCurrentStep(1);

    // Clear URL parameter if it exists
    if (router.query.strategyId) {
      router.replace('/strategy-generation', undefined, { shallow: true });
    }

    // Scroll to top of the page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Add a function to handle any strategy modifications
  const handleStrategyModification = () => {
    console.log("%c handleStrategyModification called", "background: #333; color: #0f0; font-weight: bold; padding: 2px 5px;");

    // Check if we're editing an existing strategy
    const effectiveEditingId = editingStrategyId || localStorage.getItem("editingStrategyId");
    console.log(`Current editingStrategyId: ${editingStrategyId}`);
    console.log(`localStorage editingStrategyId: ${localStorage.getItem("editingStrategyId")}`);
    console.log(`Effective editing ID: ${effectiveEditingId}`);

    // Force update both states to ensure they're properly reflected in the UI
    // Use a callback form to ensure we're working with the latest state
    setHasBeenModified(() => true);
    setIsStrategySaved(() => false);

    // If we're editing a strategy, make sure editingStrategyId is set in both state and localStorage
    if (effectiveEditingId) {
      console.log(`Strategy being edited: ${effectiveEditingId}`);
      // Ensure editingStrategyId is set in state
      setEditingStrategyId(effectiveEditingId);
      // Ensure editingStrategyId is set in localStorage
      localStorage.setItem("editingStrategyId", effectiveEditingId);
    } else {
      console.log("No editingStrategyId set - this is a new strategy");
    }

    // Force localStorage update for hasBeenModified
    localStorage.setItem("hasBeenModified", JSON.stringify(true));
    localStorage.setItem("isStrategySaved", JSON.stringify(false));

    // Add a small delay and then log the state to see if it was updated
    setTimeout(() => {
      console.log(`After modification - hasBeenModified: ${hasBeenModified}, isStrategySaved: ${isStrategySaved}, editingStrategyId: ${editingStrategyId}`);
      console.log(`localStorage hasBeenModified: ${localStorage.getItem("hasBeenModified")}`);
      console.log(`localStorage isStrategySaved: ${localStorage.getItem("isStrategySaved")}`);
      console.log(`localStorage editingStrategyId: ${localStorage.getItem("editingStrategyId")}`);
    }, 100);
  };

  // Update basic info handlers
  const handleStrategyNameChange = (e) => {
    handleStrategyModification();
    setStrategyName(e.target.value);
  };

  const handleTimeframeChange = (e) => {
    handleStrategyModification();
    const newTimeframe = e.target.value;
    setTimeframe(newTimeframe);

    // Log the change for debugging
    console.log('Timeframe changed to:', newTimeframe);
  };

  const handleTimezoneChange = (e) => {
    handleStrategyModification();
    setTimezone(e.target.value);
  };

  const handleStopLossChange = (e) => {
    handleStrategyModification();
    setStopLoss(e.target.value);
  };

  const handleRiskPercentageChange = (e) => {
    console.log("Risk percentage changed, calling handleStrategyModification");
    console.log(`Current editingStrategyId: ${editingStrategyId}`);

    // Call handleStrategyModification to update state
    handleStrategyModification();

    const value = e.target.value;
    // Only allow numbers between 0 and 100
    if (value === '' || (value >= 0 && value <= 100)) {
      setRiskPercentage(value);

      // Check for potentially risky combinations
      if (stopLossMethod === 'fixed') {
        checkRiskyParameterCombinations(fixedPips, value);
      }
      console.log(`Risk percentage set to: ${value}, hasBeenModified: ${hasBeenModified}, isStrategySaved: ${isStrategySaved}, editingStrategyId: ${editingStrategyId}`);
    }
  };

  const handleRiskRewardRatioChange = (e) => {
    handleStrategyModification();
    const value = e.target.value;
    // Only allow positive numbers
    if (value === '' || value >= 0) {
      setRiskRewardRatio(value);
    }
  };

  const handleStopLossMethodChange = (e) => {
    handleStrategyModification();
    const value = e.target.value;
    setStopLossMethod(value);

    // Clear risk warning if not using fixed pips
    if (value !== 'fixed') {
      setRiskWarning('');
    } else {
      // Check for potentially risky combinations if switching to fixed pips
      checkRiskyParameterCombinations(fixedPips, riskPercentage);
    }

    // Reset all indicator states when changing stop loss method
    // ATR
    setIsAtrAddedToChart(false);
    setIsAddingAtrToChart(false);
    setPendingAtrIndicator(null);

    // Check if Bollinger Bands is already added from step 3
    const existingBollingerBands = selectedIndicators.some(
      ind => ind.type === 'BollingerBands' && !ind.fromRiskManagement
    );

    if (existingBollingerBands) {
      // If Bollinger Bands is already on the chart from step 3, keep it marked as added
      console.log('Bollinger Bands already exists on chart from step 3 (stop loss method change)');
      setIsBollingerAddedToChart(true);
    } else {
      // Otherwise reset the Bollinger Bands state
      setIsBollingerAddedToChart(false);
      setIsAddingBollingerToChart(false);
      setPendingBollingerIndicator(null);
    }

    // Support & Resistance
    setIsSRAddedToChart(false);
    setIsAddingSRToChart(false);
    setPendingSRIndicator(null);

    // Remove risk management indicators when changing stop loss method
    // But keep indicators added from step 3
    setSelectedIndicators(prev =>
      prev.filter(ind => !(
        (ind.type === 'ATR') ||
        (ind.type === 'BollingerBands' && ind.fromRiskManagement) ||
        (ind.type === 'SupportResistance')
      ))
    );
  };

  const handleFixedPipsChange = (e) => {
    handleStrategyModification();
    const value = e.target.value;
    setFixedPips(value);

    // Check for potentially risky combinations
    checkRiskyParameterCombinations(value, riskPercentage);
  };

  // Check for risky parameter combinations that might cause insufficient margin
  const checkRiskyParameterCombinations = (fixedPipsValue, riskPercentageValue) => {
    const fixedPips = parseFloat(fixedPipsValue);
    const riskPct = parseFloat(riskPercentageValue);

    if (!isNaN(riskPct) && !isNaN(fixedPips) && riskPct >= 10 && fixedPips >= 50) {
      setRiskWarning('Warning: High risk percentage combined with large fixed pips may cause insufficient margin errors when trading. Consider reducing risk percentage or fixed pips value.');
    } else {
      setRiskWarning('');
    }
    // Remove the incorrect line that was causing the error
  };

  const handleIndicatorParamChange = (param, value) => {
    handleStrategyModification();
    setIndicatorParams(prev => ({
      ...prev,
      [param]: value
    }));

    // Reset indicator states when changing parameters
    if (indicatorBasedSL === 'atr') {
      setIsAtrAddedToChart(false);
      setIsAddingAtrToChart(false);
      setPendingAtrIndicator(null);

      // Remove all ATR indicators
      setSelectedIndicators(prev =>
        prev.filter(ind => ind.type !== 'ATR')
      );
    } else if (indicatorBasedSL === 'bollinger') {
      // Check if Bollinger Bands is already added from step 3
      const existingBollingerBands = selectedIndicators.some(
        ind => ind.type === 'BollingerBands' && !ind.fromRiskManagement
      );

      if (existingBollingerBands) {
        // If Bollinger Bands is already on the chart from step 3, just mark it as added
        console.log('Bollinger Bands already exists on chart from step 3 (param change)');
        setIsBollingerAddedToChart(true);

        // Only remove Bollinger Bands indicators from risk management
        setSelectedIndicators(prev =>
          prev.filter(ind => !(ind.type === 'BollingerBands' && ind.fromRiskManagement))
        );
      } else {
        // Otherwise reset the state and remove all Bollinger Bands indicators
        setIsBollingerAddedToChart(false);
        setIsAddingBollingerToChart(false);
        setPendingBollingerIndicator(null);

        // Remove all Bollinger Bands indicators from risk management
        setSelectedIndicators(prev =>
          prev.filter(ind => !(ind.type === 'BollingerBands' && ind.fromRiskManagement))
        );
      }
    } else if (indicatorBasedSL === 'support_resistance') {
      setIsSRAddedToChart(false);
      setIsAddingSRToChart(false);
      setPendingSRIndicator(null);

      // Remove all Support & Resistance indicators
      setSelectedIndicators(prev =>
        prev.filter(ind => ind.type !== 'SupportResistance')
      );
    }
  };

  const getDefaultIndicatorParams = (indicator) => {
    if (indicator === 'support_resistance') {
      // Default values based on timeframe
      const currentTimeframe = timeframe.toLowerCase();
      if (currentTimeframe.includes('m')) {
        // For minute timeframes, use fewer periods since each candle represents less time
        const minutes = parseInt(currentTimeframe);
        if (minutes <= 5) {
          return { left: 5, right: 5 }; // 1m, 5m
        } else if (minutes <= 15) {
          return { left: 4, right: 4 }; // 15m
        } else {
          return { left: 3, right: 3 }; // 30m
        }
      } else if (currentTimeframe.includes('h')) {
        // For hour timeframes, use more periods
        const hours = parseInt(currentTimeframe);
        if (hours <= 1) {
          return { left: 6, right: 6 }; // 1h
        } else if (hours <= 4) {
          return { left: 8, right: 8 }; // 4h
        } else {
          return { left: 10, right: 10 }; // 12h
        }
      } else if (currentTimeframe.includes('d')) {
        // For daily timeframes, use even more periods
        return { left: 15, right: 15 }; // 1d
      } else if (currentTimeframe.includes('w')) {
        // For weekly timeframes, use the most periods
        return { left: 20, right: 20 }; // 1w
      }
      // Default fallback
      return { left: 10, right: 10 };
    }
    return {};
  };

  const handleIndicatorBasedSLChange = (e) => {
    handleStrategyModification();
    const value = e.target.value;
    setIndicatorBasedSL(value);

    // Reset all indicator states when changing indicator type
    // ATR
    setIsAtrAddedToChart(false);
    setIsAddingAtrToChart(false);
    setPendingAtrIndicator(null);

    // Bollinger Bands - special handling
    if (value === 'bollinger') {
      // Check if Bollinger Bands is already added from step 3
      const existingBollingerBands = selectedIndicators.some(
        ind => ind.type === 'BollingerBands' && !ind.fromRiskManagement
      );

      if (existingBollingerBands) {
        // If Bollinger Bands is already on the chart from step 3, just mark it as added
        console.log('Bollinger Bands already exists on chart from step 3');
        setIsBollingerAddedToChart(true);
      } else {
        // Otherwise reset the state
        setIsBollingerAddedToChart(false);
        setIsAddingBollingerToChart(false);
        setPendingBollingerIndicator(null);
      }
    } else {
      // For other indicators, reset Bollinger Bands state
      setIsBollingerAddedToChart(false);
      setIsAddingBollingerToChart(false);
      setPendingBollingerIndicator(null);
    }

    // Support & Resistance
    setIsSRAddedToChart(false);
    setIsAddingSRToChart(false);
    setPendingSRIndicator(null);

    // Remove risk management indicators when changing indicator type
    // But keep indicators added from step 3
    setSelectedIndicators(prev =>
      prev.filter(ind => !(
        (ind.type === 'ATR') ||
        (ind.type === 'BollingerBands' && ind.fromRiskManagement) ||
        (ind.type === 'SupportResistance')
      ))
    );

    if (value) {
      // Only set default params if indicatorParams is empty or not for this indicator
      setIndicatorParams(prev => {
        if (!prev || Object.keys(prev).length === 0 || indicatorBasedSL !== value) {
          return getDefaultIndicatorParams(value);
        }
        return prev;
      });
    } else {
      setIndicatorParams({});
    }
  };

  const handleLotSizeChange = (e) => {
    handleStrategyModification();
    setLotSize(e.target.value);
  };

  // Update stored strategy selection handler
  const handleStoredStrategySelect = (e) => {
    if (hasBeenModified && !isStrategySaved) {
      const confirm = window.confirm("You have unsaved changes. Loading another strategy will discard these changes. Do you want to continue?");
      if (!confirm) {
        e.preventDefault();
        return;
      }
    }

    const selectedId = e.target.value;
    setSelectedStoredStrategyId(selectedId);

    // Clear backtest related states
    setBacktestResults(null);
    setBacktestStatus(null);
    setLoadingBacktest(false);

    if (selectedId && savedStrategies.length > 0) {
      const strat = savedStrategies.find(s => s.id.toString() === selectedId);
      if (strat) {
        const s = JSON.parse(strat.strategy_json);
        // Set basic information
        setStrategyName(s.name);
        setStrategyDescription(s.description || "");
        setForexPair(s.instruments);
        setTimeframe(s.timeframe);
        setTimezone(s.TimeZone);

        // Set risk management
        setRiskPercentage(s.riskManagement.riskPercentage || "");
        setRiskRewardRatio(s.riskManagement.riskRewardRatio || "");
        setStopLossMethod(s.riskManagement.stopLossMethod || "fixed");
        setFixedPips(s.riskManagement.fixedPips || "");

        // Set indicator-based stop loss settings if present
        if (s.riskManagement.stopLossMethod === 'indicator' && s.riskManagement.indicatorBasedSL) {
          setIndicatorBasedSL(s.riskManagement.indicatorBasedSL.indicator || "");
          if (s.riskManagement.indicatorBasedSL.parameters && Object.keys(s.riskManagement.indicatorBasedSL.parameters).length > 0) {
            setIndicatorParams(s.riskManagement.indicatorBasedSL.parameters);
          } else {
            setIndicatorParams(getDefaultIndicatorParams(s.riskManagement.indicatorBasedSL.indicator || ""));
          }
        } else {
          setIndicatorBasedSL("");
          setIndicatorParams({});
        }

        // Set lot size if present
        setLotSize(s.riskManagement.lotSize || "");

        // Set indicators and rules
        setSelectedIndicators(s.indicators || []);
        setEntryRules(s.entryRules || []);
        setExitRules(s.exitRules || []);

        // Convert trading sessions to timezone values for the select input
        const sessions = Array.isArray(s.tradingSession) ? s.tradingSession : [s.tradingSession];
        const sessionTimezones = sessions.map(sessionName => {
          if (sessionName === "All") return "All";
          const session = tradingSessions.find(ts => ts.name === sessionName);
          return session ? session.timezone : sessionName;
        });
        setSelectedTradingSessions(sessionTimezones);

        setGeneratedStrategy(s);
        setEditingStrategyId(strat.id);
        setIsStrategyFinalized(true);
        setHasBeenModified(false);
      }
    } else {
      clearStrategyForm();
    }
  };

  const handleAddExitRule = () => {
    handleStrategyModification();
    if (!newExitRule.indicator1) return;

    // Check if this is a Bollinger Bands indicator and set a default band if not specified
    const isBollingerBands1 = selectedIndicators.find(
      ind => ind.id === newExitRule.indicator1 && ind.type === 'BollingerBands'
    );

    // Check if second indicator is Bollinger Bands
    const isBollingerBands2 = newExitRule.compareType === 'indicator' && selectedIndicators.find(
      ind => ind.id === newExitRule.indicator2 && ind.type === 'BollingerBands'
    );

    // Check if this is a MACD indicator and set a default component if not specified
    const isMacd1 = selectedIndicators.find(
      ind => ind.id === newExitRule.indicator1 && ind.type === 'MACD'
    );

    // Check if second indicator is MACD
    const isMacd2 = newExitRule.compareType === 'indicator' && selectedIndicators.find(
      ind => ind.id === newExitRule.indicator2 && ind.type === 'MACD'
    );

    const ruleToAdd = {
      ...newExitRule,
      id: generateId(),
      // Set default band to 'middle' for Bollinger Bands if not specified
      band: isBollingerBands1 && !newExitRule.band ? 'middle' : newExitRule.band,
      // Set default band2 to 'middle' for second Bollinger Bands indicator if not specified
      band2: isBollingerBands2 && !newExitRule.band2 ? 'middle' : newExitRule.band2,
      // Set default macdComponent to 'macd' for MACD if not specified
      macdComponent: isMacd1 && !newExitRule.macdComponent ? 'macd' : newExitRule.macdComponent,
      // Set default macdComponent2 to 'macd' for second MACD indicator if not specified
      macdComponent2: isMacd2 && !newExitRule.macdComponent2 ? 'macd' : newExitRule.macdComponent2
    };

    console.log("Adding exit rule:", ruleToAdd);
    setExitRules(prev => [...prev, ruleToAdd]);

    setNewExitRule({
      id: '',
      tradeType: 'long',
      indicator1: '',
      operator: 'Crossing above',
      compareType: 'value',
      indicator2: '',
      value: '',
      barRef: 'close', // Add bar reference field with default value
      band: undefined, // Reset band
      band2: undefined, // Reset band2
      macdComponent: undefined, // Reset macdComponent
      macdComponent2: undefined // Reset macdComponent2
    });
    setIsExitRuleModalOpen(false);
  };

  const addExitRule = () => {
    setIsExitRuleModalOpen(true);
  };

  const removeExitRule = (index) => {
    handleStrategyModification();
    setExitRules((prev) => prev.filter((_, i) => i !== index));
  };

  // Add this function to handle step navigation
  const handleStepClick = (stepId) => {
    // Only allow going to previous steps
    if (stepId >= currentStep) {
      return;
    }

    // Always allow going back to any previous step
    setCurrentStep(stepId);
  };

  const [isLoading, setIsLoading] = useState(true);

  // Add this useEffect to handle initial loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#0A0B0B] flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Add this function to handle strategy finalization
  const finalizeStrategy = async () => {
    // Helper function to check if indicator parameters are valid
    const hasValidIndicatorParameters = () => {
      if (stopLossMethod !== 'indicator' || !indicatorBasedSL) return true;

      // Get default parameters for the selected indicator
      const defaultParams = (() => {
        switch (indicatorBasedSL) {
          case 'atr':
            return { period: 14, multiplier: 2 };
          case 'bollinger':
            return { period: 20, stdDev: 2 };
          case 'support_resistance':
            return getDefaultIndicatorParams('support_resistance');
          default:
            return {};
        }
      })();

      // If no parameters are set, use default parameters
      if (Object.keys(indicatorParams).length === 0) {
        return true; // Default parameters will be used
      }

      // If parameters are set, make sure they're valid
      return Object.keys(indicatorParams).length > 0;
    };

    // Validate required fields
    const isValid = !!(
      strategyName.trim() &&
      forexPair.trim() &&
      selectedIndicators.length > 0 &&
      entryRules.length > 0 &&
      exitRules.length > 0 &&
      riskPercentage &&
      riskRewardRatio &&
      stopLossMethod &&
      (
        (stopLossMethod === 'fixed' && fixedPips) ||
        (stopLossMethod === 'indicator' && indicatorBasedSL && hasValidIndicatorParameters()) ||
        (stopLossMethod === 'risk' && lotSize)
      )
    );

    if (!isValid) {
      alert(
        "Please complete all required fields: Strategy Name, Forex Pair, at least one indicator, one entry rule, one exit rule, risk percentage, risk-reward ratio, and stop loss method with its parameters."
      );
      setIsStrategyValid(false);
      setIsStrategyFinalized(false);
      return false;
    }

    // Get the actual parameters to use (either user-set or default)
    const getActualIndicatorParameters = () => {
      if (stopLossMethod !== 'indicator' || !indicatorBasedSL) return {};

      // If no parameters are set, return default parameters
      if (Object.keys(indicatorParams).length === 0) {
        switch (indicatorBasedSL) {
          case 'atr':
            return { period: 14, multiplier: 2 };
          case 'bollinger':
            return { period: 20, stdDev: 2 };
          case 'support_resistance':
            return getDefaultIndicatorParams('support_resistance');
          default:
            return {};
        }
      }

      // Return user-set parameters
      return indicatorParams;
    };

    const strategyPayload = {
      name: strategyName,
      instruments: forexPair,
      timeframe: timeframe,
      indicators: selectedIndicators.map((ind) => ({
        id: ind.id,
        indicator_class: ind.type,
        parameters: ind.parameters,
        source: ind.source
      })),
      entryRules: entryRules,
      exitRules: exitRules,
      riskManagement: {
        riskPercentage,
        riskRewardRatio,
        stopLossMethod,
        fixedPips: stopLossMethod === 'fixed' ? String(fixedPips || "50") : "",
        indicatorBasedSL: stopLossMethod === 'indicator' ? {
          indicator: indicatorBasedSL,
          parameters: getActualIndicatorParameters()
        } : {
          indicator: "",
          parameters: {}
        },
        lotSize: stopLossMethod === 'risk' ? lotSize : ""
      },
      tradingSession: selectedTradingSessions.map(session =>
        tradingSessions.find(s => s.timezone === session)?.name || session
      ),
      ...(entryLongCount > 1 && { entryLongGroupOperator: entryBuyGroupOperator }),
      ...(entryShortCount > 1 && { entryShortGroupOperator: entrySellGroupOperator }),
      ...(exitLongCount > 1 && { exitLongGroupOperator: exitBuyGroupOperator }),
      ...(exitShortCount > 1 && { exitShortGroupOperator: exitSellGroupOperator })
    };

    try {
      console.log("Strategy generated successfully:", strategyPayload);
      setGeneratedStrategy(strategyPayload);
      setIsStrategyValid(true);
      setIsStrategyFinalized(true);
      return true;
    } catch (error) {
      console.error("Error finalizing strategy:", error);
      setIsStrategyValid(false);
      setIsStrategyFinalized(false);
      return false;
    }
  };

  // Update the setCurrentStep function to handle finalization
  const handleStepChange = async (newStep) => {
    // If moving to step 7 (Review), attempt to finalize the strategy
    if (newStep === 7) {
      const success = await finalizeStrategy();
      if (!success) {
        // If finalization fails, don't proceed to the Review step
        return;
      }
    }
    setCurrentStep(newStep);
  };

  // Add the OrynAI dialog handlers
  const handleOpenStrategyDescription = () => setIsStrategyDescriptionOpen(true);
  const handleCloseStrategyDescription = () => setIsStrategyDescriptionOpen(false);

  // Add the helper functions for saving and updating strategies
  const saveNewStrategy = async (strategyData) => {
    console.log("%c saveNewStrategy called", "background: #333; color: #0f0; font-weight: bold; padding: 2px 5px;");

    if (!firebaseUser) {
      throw new Error("Please log in to save your strategy.");
    }

    // Check for duplicate names
    const duplicate = savedStrategies.find(
      (s) => s.name.toLowerCase() === strategyName.trim().toLowerCase()
    );
    if (duplicate) {
      throw new Error("A strategy with this name already exists. Please choose a different name.");
    }

    const strategyToSave = {
      name: strategyData.name,
      description: strategyData.description,
      instruments: strategyData.forexPair,
      timeframe: strategyData.timeframe,
      tradingSession: strategyData.tradingSession.map(session =>
        tradingSessions.find(s => s.timezone === session)?.name || session
      ),
      indicators: strategyData.indicators.map(indicator => ({
        id: indicator.id || generateId(),
        indicator_class: indicator.type,
        type: indicator.type,
        parameters: { ...indicator.parameters },
        source: indicator.source || "price"
      })),
      entryRules: strategyData.entryRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close'
      })),
      exitRules: strategyData.exitRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close'
      })),
      riskManagement: {
        riskPercentage: strategyData.riskManagement.riskPercentage,
        riskRewardRatio: strategyData.riskManagement.riskRewardRatio,
        stopLossMethod: strategyData.riskManagement.stopLossMethod,
        fixedPips: strategyData.riskManagement.stopLossMethod === 'fixed' ?
          String(strategyData.riskManagement.fixedPips || "50") : "",
        indicatorBasedSL: strategyData.riskManagement.stopLossMethod === 'indicator' ?
          {
            indicator: strategyData.riskManagement.indicatorBasedSL.indicator,
            parameters: strategyData.riskManagement.indicatorBasedSL.parameters
          } :
          {
            indicator: "",
            parameters: {}
          },
        lotSize: strategyData.riskManagement.stopLossMethod === 'risk' ?
          strategyData.riskManagement.lotSize : ""
      },
      ...(entryLongCount > 1 && { entryLongGroupOperator: entryBuyGroupOperator }),
      ...(entryShortCount > 1 && { entryShortGroupOperator: entrySellGroupOperator }),
      ...(exitLongCount > 1 && { exitLongGroupOperator: exitBuyGroupOperator }),
      ...(exitShortCount > 1 && { exitShortGroupOperator: exitSellGroupOperator })
    };

    console.log("Saving strategy:", strategyToSave);

    const response = await axios.post(SAVE_STRATEGY_URL, {
      firebase_uid: firebaseUser.uid,
      strategy: strategyToSave
    }, {
      headers: { "Content-Type": "application/json" }
    });

    console.log("Save strategy response:", response.data);

    if (response.data.message !== "Strategy saved successfully") {
      throw new Error(response.data.message || "Failed to save strategy");
    }

    // Refresh saved strategies list
    const res = await axios.get(`${GET_STRATEGIES_URL}?firebase_uid=${firebaseUser.uid}`);
    setSavedStrategies(res.data);

    // Return the strategy ID so it can be used to set editingStrategyId
    return {
      strategyId: response.data.data?.strategyId,
      name: response.data.data?.name
    };
  };

  const updateStrategy = async (strategyId, strategyData) => {
    console.log("%c updateStrategy called", "background: #333; color: #0f0; font-weight: bold; padding: 2px 5px;");
    console.log(`Updating strategy with ID: ${strategyId}`);

    if (!firebaseUser) {
      throw new Error("Please log in to update your strategy.");
    }

    const strategyToUpdate = {
      name: strategyData.name,
      description: strategyData.description,
      instruments: strategyData.forexPair,
      timeframe: strategyData.timeframe,
      tradingSession: strategyData.tradingSession.map(session =>
        tradingSessions.find(s => s.timezone === session)?.name || session
      ),
      indicators: strategyData.indicators.map(indicator => ({
        id: indicator.id || generateId(),
        indicator_class: indicator.type,
        type: indicator.type,
        parameters: { ...indicator.parameters },
        source: indicator.source || "price"
      })),
      entryRules: strategyData.entryRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close'
      })),
      exitRules: strategyData.exitRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close'
      })),
      riskManagement: {
        riskPercentage: strategyData.riskManagement.riskPercentage,
        riskRewardRatio: strategyData.riskManagement.riskRewardRatio,
        stopLossMethod: strategyData.riskManagement.stopLossMethod,
        fixedPips: strategyData.riskManagement.stopLossMethod === 'fixed' ?
          String(strategyData.riskManagement.fixedPips || "50") : "",
        indicatorBasedSL: strategyData.riskManagement.stopLossMethod === 'indicator' ?
          {
            indicator: strategyData.riskManagement.indicatorBasedSL.indicator,
            parameters: strategyData.riskManagement.indicatorBasedSL.parameters
          } :
          {
            indicator: "",
            parameters: {}
          },
        lotSize: strategyData.riskManagement.stopLossMethod === 'risk' ?
          strategyData.riskManagement.lotSize : ""
      },
      ...(entryLongCount > 1 && { entryLongGroupOperator: entryBuyGroupOperator }),
      ...(entryShortCount > 1 && { entryShortGroupOperator: entrySellGroupOperator }),
      ...(exitLongCount > 1 && { exitLongGroupOperator: exitBuyGroupOperator }),
      ...(exitShortCount > 1 && { exitShortGroupOperator: exitSellGroupOperator })
    };

    console.log("Updating strategy:", strategyToUpdate);

    const response = await axios.put(UPDATE_STRATEGY_URL, {
      firebase_uid: firebaseUser.uid,
      strategyId: strategyId,
      strategy: strategyToUpdate
    }, {
      headers: { "Content-Type": "application/json" }
    });

    console.log("Update strategy response:", response.data);

    if (response.data.message !== "Strategy updated successfully") {
      throw new Error(response.data.message || "Failed to update strategy");
    }

    // Refresh saved strategies list
    const res = await axios.get(`${GET_STRATEGIES_URL}?firebase_uid=${firebaseUser.uid}`);
    setSavedStrategies(res.data);

    // Return the updated strategy ID for consistency with saveNewStrategy
    return {
      strategyId: strategyId,
      name: strategyData.name
    };
  };

  // Add this function to handle backtest dialog
  const handleCloseBacktestDialog = () => {
    setIsBacktestDialogOpen(false);
  };

  // Add this function to handle the reset button click
  const handleResetClick = () => {
    if (hasBeenModified) {
      const confirmReset = window.confirm(
        "Are you sure you want to reset the form? This will clear all your progress and cannot be undone."
      );
      if (!confirmReset) {
        return;
      }
    }
    clearStrategyForm();
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-[#0A0B0B] text-white p-4 md:p-6 lg:p-8 overflow-x-auto min-w-[320px]">
        {/* Header Section with OrynAI Assistant Button */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <h1 className="text-2xl md:text-3xl font-bold">Strategy Generation</h1>
          <div className="flex flex-wrap items-center gap-2 md:gap-4 w-full sm:w-auto">
            <button
              onClick={handleResetClick}
              className={`${buttonStyles.secondary} group text-sm md:text-base`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5 mr-1 md:mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Reset Form
            </button>
            <button
              onClick={handleOpenStrategyDescription}
              className={`${buttonStyles.primary} text-sm md:text-base`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5 mr-1 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              OrynAI Assistant
            </button>
          </div>
        </div>

        {/* Strategy Description Dialog */}
        <StrategyDescriptionDialog
          isOpen={isStrategyDescriptionOpen}
          onClose={handleCloseStrategyDescription}
          onStrategyGenerated={handleStrategyGenerated}
        />

        {/* Introduction Section */}
        <div className="mb-12">
          <p className="text-[#FEFEFF]/80 text-lg mb-6">
            Create your custom trading strategy in just a few steps. Our intuitive strategy builder will guide you through the process of defining your trading rules, from basic setup to advanced configurations.
          </p>
          <div className="bg-[#EFBD3A]/10 border border-[#EFBD3A]/20 rounded-lg p-4">
            <p className="text-[#EFBD3A] text-sm">
              <span className="font-semibold">Pro Tip:</span> Take your time to carefully consider each step. A well-thought-out strategy is key to successful trading. You can find your saved strategies in the Strategy Library.
            </p>
              </div>
              </div>

        {/* Add the Strategy Description section after the Introduction and before the Steps */}
        {strategyDescription && strategyDescription.trim() !== "" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-8 bg-gray-800/50 border border-gray-700/50 rounded-xl p-6"
          >
            <div className="flex items-start gap-4">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0012 18.75c-1.03 0-1.96-.474-2.56-1.213l-.548.547z" />
        </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-white mb-2">AI-Generated Strategy Overview</h3>
                <div className="prose prose-invert max-w-none">
                  <p className="text-gray-300 whitespace-pre-line">{strategyDescription}</p>
                </div>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-700/50">
              <p className="text-sm text-gray-400">
                This strategy was generated by OrynAI based on your requirements. You can modify any aspect of it in the steps below.
              </p>
            </div>
          </motion.div>
        )}

        <StepNavigation
          steps={steps}
          currentStep={currentStep}
          onStepClick={handleStepClick}
        />

        {/* Chart Section - Moved outside steps */}
        {forexPair && isForexPairValid && isForexPairExists && (
          <div className="mb-8 bg-[#1a1a1a] rounded-xl p-4 md:p-6 shadow-lg overflow-hidden min-w-[320px]">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
              <h3 className="text-lg font-semibold text-[#FEFEFF]">Price Chart</h3>
              <div className="flex items-center gap-2 md:gap-4">
                <div className="text-xs md:text-sm text-[#FEFEFF]/60">
                  {forexPair} • {timeframe} • {userTimezone}
                </div>
              </div>
            </div>
            <div className="w-full overflow-x-auto min-w-[300px]" style={{ minWidth: '300px', maxWidth: '100%' }}>
              <StrategyChart
                forexPair={forexPair}
                timeframe={timeframe}
                userTimezone={userTimezone}
                selectedTradingSessions={selectedTradingSessions
                  .map(getSessionNameFromTimezone)
                  .filter(session => session && session !== "All")}
                data={forexData}
                isLoading={isLoadingForexData}
                error={forexDataError}
                indicators={selectedIndicators}
                trades={backtestResults?.trades || []} // Pass trades to chart
                onIndicatorAdd={(success) => {
                  console.log('onIndicatorAdd called with success:', success);
                  // This callback is triggered when the user makes a choice in the indicator dialog

                  // Handle ATR indicator
                  if (pendingAtrIndicator && pendingAtrIndicator.type === 'ATR') {
                    // Immediately update the button state based on the user's choice
                    if (success) {
                      console.log('User clicked "Create new window" for ATR indicator');
                      // User clicked "Create new window" in the dialog

                      // Immediately set the button state to "Added to Chart"
                      setIsAtrAddedToChart(true);
                      setIsAddingAtrToChart(false);

                      // Update the indicator's displayPreference to mark it as processed
                      setSelectedIndicators(prev =>
                        prev.map(ind =>
                          ind.id === pendingAtrIndicator.id
                            ? { ...ind, displayPreference: 'new' }
                            : ind
                        )
                      );
                    } else {
                      console.log('User clicked "Cancel" for ATR indicator');
                      // User clicked "Cancel" - immediately reset button state
                      setIsAddingAtrToChart(false);
                      setIsAtrAddedToChart(false);

                      // Remove the indicator from the list
                      setSelectedIndicators(prev =>
                        prev.filter(ind => ind.id !== pendingAtrIndicator.id)
                      );
                    }

                    // Clear the pending indicator
                    setPendingAtrIndicator(null);
                  }

                  // Handle Bollinger Bands indicator
                  else if (pendingBollingerIndicator && pendingBollingerIndicator.type === 'BollingerBands') {
                    if (success) {
                      console.log('User clicked "Create new window" for Bollinger Bands indicator');

                      // Immediately set the button state to "Added to Chart"
                      setIsBollingerAddedToChart(true);
                      setIsAddingBollingerToChart(false);

                      // Update the indicator's displayPreference to mark it as processed
                      setSelectedIndicators(prev =>
                        prev.map(ind =>
                          ind.id === pendingBollingerIndicator.id
                            ? { ...ind, displayPreference: 'new' }
                            : ind
                        )
                      );
                    } else {
                      console.log('User clicked "Cancel" for Bollinger Bands indicator');

                      // Reset button state
                      setIsAddingBollingerToChart(false);
                      setIsBollingerAddedToChart(false);

                      // Remove the indicator from the list
                      setSelectedIndicators(prev =>
                        prev.filter(ind => ind.id !== pendingBollingerIndicator.id)
                      );
                    }

                    // Clear the pending indicator
                    setPendingBollingerIndicator(null);
                  }

                  // Handle Support & Resistance indicator
                  else if (pendingSRIndicator && pendingSRIndicator.type === 'SupportResistance') {
                    if (success) {
                      console.log('User clicked "Create new window" for Support & Resistance indicator');

                      // Immediately set the button state to "Added to Chart"
                      setIsSRAddedToChart(true);
                      setIsAddingSRToChart(false);

                      // Update the indicator's displayPreference to mark it as processed
                      setSelectedIndicators(prev =>
                        prev.map(ind =>
                          ind.id === pendingSRIndicator.id
                            ? { ...ind, displayPreference: 'new' }
                            : ind
                        )
                      );
                    } else {
                      console.log('User clicked "Cancel" for Support & Resistance indicator');

                      // Reset button state
                      setIsAddingSRToChart(false);
                      setIsSRAddedToChart(false);

                      // Remove the indicator from the list
                      setSelectedIndicators(prev =>
                        prev.filter(ind => ind.id !== pendingSRIndicator.id)
                      );
                    }

                    // Clear the pending indicator
                    setPendingSRIndicator(null);
                  }
                }}
              />
            </div>
          </div>
        )}

        {/* Step 1: Basic Information */}
        {currentStep === 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <h2 className="text-2xl font-bold text-[#FEFEFF] mb-6">Basic Strategy Information</h2>
              <div className="space-y-4">
                {/* Strategy Name */}
                <div>
                  <label htmlFor="strategyName" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Strategy Name
                  </label>
                  <input
                    type="text"
                    id="strategyName"
                    value={strategyName}
                    onChange={handleStrategyNameChange}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                    placeholder="Enter strategy name..."
                  />
                </div>

                {/* Forex Pair */}
                <div>
                  <label htmlFor="forexPair" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Forex Pair
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="forexPair"
                      value={forexPair}
                      onChange={handleForexPairChange}
                      className={`w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border ${
                        isForexPairValid && isForexPairExists
                          ? 'border-[#3a3a3a] focus:ring-[#EFBD3A]'
                          : 'border-red-500 focus:ring-red-500'
                      } focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200`}
                      placeholder="e.g., EUR/USD"
                    />
                    {forexPairError && (
                      <p className="mt-2 text-sm text-red-500">{forexPairError}</p>
                    )}
                  </div>
                </div>

                {/* Remove Chart Section from here */}
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-end space-x-4 mt-6">
              <button
                onClick={() => handleStepChange(2)}
                disabled={!strategyName || !forexPair || !isForexPairValid || !isForexPairExists}
                className={`${buttonStyles.primary} ${
                  (!strategyName || !forexPair || !isForexPairValid || !isForexPairExists)
                    ? 'opacity-50 cursor-not-allowed'
                    : ''
                }`}
              >
                Next Step
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 2: Time Settings */}
        {currentStep === 2 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <h2 className="text-2xl font-bold text-[#FEFEFF] mb-6">Time Settings</h2>
              <div className="space-y-4">
                {/* Trading Session */}
                <div>
                  <label htmlFor="tradingSession" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Trading Sessions
                  </label>
                  <select
                    id="tradingSession"
                    multiple
                    value={selectedTradingSessions}
                    onChange={handleTradingSessionChange}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                    size={5}
                  >
                    {tradingSessions.map((session) => (
                      <option
                        key={session.name}
                        value={session.timezone}
                        className="py-2 px-4 hover:bg-[#3a3a3a] cursor-pointer"
                      >
                        {session.name} - {session.description}
                      </option>
                    ))}
                  </select>
                  <p className="mt-2 text-sm text-[#FEFEFF]/60">
                    Hold Ctrl/Cmd to select multiple sessions. Selecting "All" will clear other selections.
                  </p>
                </div>

                {/* Timeframe */}
                <div>
                  <label htmlFor="timeframe" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Timeframe
                  </label>
                  <select
                    id="timeframe"
                    value={timeframe}
                    onChange={handleTimeframeChange}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                  >
                    {timeframeOptions.map((tf) => (
                      <option key={tf} value={tf}>
                        {tf}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Remove Chart Section from here */}
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(1)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(3)}
                className={buttonStyles.primary}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 3: Indicators */}
        {currentStep === 3 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <h2 className="text-2xl font-bold text-[#FEFEFF] mb-6">Technical Indicators</h2>

              {/* Add New Indicator */}
              <div className="space-y-4 mb-8">
                <h3 className="text-lg font-semibold text-[#FEFEFF]">Add Indicator</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Indicator Type Selection */}
                            <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                                Indicator Type
                              </label>
                  <select
                    value={newIndicator.type}
                    onChange={handleNewIndicatorTypeChange}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                    >
                      <option value="">Select an indicator...</option>
                      {supportedIndicators.map((ind) => (
                        <option key={ind.name} value={ind.name}>
                          {ind.name}
                                    </option>
                    ))}
                  </select>
                                </div>

                  {/* Source Selection */}
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Source
                    </label>
                    <select
                      value={newIndicator.source}
                      onChange={(e) => setNewIndicator(prev => ({ ...prev, source: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                    >
                      {getSourceOptions().map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {selectedIndicators.length === 0 && (
                      <p className="mt-2 text-sm text-[#FEFEFF]/60 italic">
                        Add indicators to use them as source for new indicators
                      </p>
                    )}
                  </div>
                            </div>

                {/* Parameters Configuration */}
                              <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                                  Parameters
                                </label>
                  {newIndicator.type ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {Object.entries(newIndicator.parameters).map(([key, value]) => (
                        <div key={key} className="flex items-center gap-2">
                          <span className="text-[#FEFEFF]/60 whitespace-nowrap">{key}:</span>
                          <input
                            type="number"
                            value={value}
                            onChange={(e) =>
                              handleNewIndicatorParamChange(key, e.target.value)
                            }
                            className="w-full px-3 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                          />
                        </div>
                  ))}
                                </div>
                  ) : (
                    <div className="h-[42px] flex items-center text-[#FEFEFF]/40 italic">
                      Select an indicator to configure parameters
                              </div>
                            )}
                          </div>

                {/* Add Button */}
                <div className="flex justify-end mt-4">
                    <button
                      onClick={addNewIndicator}
                    disabled={!newIndicator.type}
                    className={`${buttonStyles.add} ${
                      !newIndicator.type ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                    Add Indicator
                    </button>
                            </div>
            </div>

            {/* Selected Indicators List */}
            {selectedIndicators.length > 0 && (
                          <div className="mt-8">
                  <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Selected Indicators</h3>
                            <div className="space-y-3">
                              {selectedIndicators.map((indicator) => (
                                <div
                                  key={indicator.id}
                        className="flex items-center justify-between p-4 bg-[#2a2a2a] rounded-lg border border-[#3a3a3a] group hover:border-[#EFBD3A]/30 transition-all duration-200"
                    >
                      <div>
                          <span className="text-[#FEFEFF] font-medium">
                            {indicator.type}
                          </span>
                          <span className="text-[#FEFEFF]/60 text-sm ml-2">
                            (
                                      {Object.entries(indicator.parameters)
                            .map(([k, v]) => `${k}: ${v}`)
                            .join(", ")}
                            )
                          </span>
                          <span className="text-[#EFBD3A]/80 text-sm ml-2">
                            Source: {indicator.source === "volume" ? "Volume" :
                                    indicator.source === "open" ? "Open" :
                                    indicator.source === "close" ? "Close" :
                                    indicator.source === "high" ? "High" :
                                    indicator.source === "low" ? "Low" :
                                    selectedIndicators.find(ind => ind.id === indicator.source)?.type || "Price"}
                          </span>
                      </div>
                      <button
                                    onClick={() => removeSelectedIndicator(indicator.id)}
                          className={`${buttonStyles.remove} opacity-0 group-hover:opacity-100`}
                      >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ))}
                            </div>
              </div>
            )}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(2)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(4)}
                disabled={selectedIndicators.length === 0}
                className={`${buttonStyles.primary} ${
                  selectedIndicators.length === 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
                    </div>
          </motion.div>
                  )}

        {/* Step 4: Entry Rules */}
                  {currentStep === 4 && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <h2 className="text-2xl font-bold text-[#FEFEFF] mb-6">Entry Rules</h2>

              {/* Entry Rules Configuration */}
              <div className="space-y-6">
                {/* Entry Rules List */}
                <div className="space-y-4">
                  {entryRules.map((rule, index) => (
                    <div
                      key={rule.id}
                      className="flex items-center justify-between p-4 bg-[#2a2a2a] rounded-lg border border-[#3a3a3a] group hover:border-[#EFBD3A]/30 transition-all duration-200"
                    >
                      <div className="flex items-center gap-3">
                        <span className={`px-2 py-1 rounded ${
                          rule.tradeType === 'long' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                        }`}>
                          {rule.tradeType === 'long' ? 'Long' : 'Short'}
                                </span>
                        <span className="text-[#FEFEFF]">
                                {getIndicatorLabel(rule.indicator1, rule)}
                        </span>
                                <span className="text-[#EFBD3A]">{rule.operator}</span>
                        <span className="text-[#FEFEFF]">
                          {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                        </span>
                        {rule.indicator1 === 'price' && (
                          <span className="text-[#FEFEFF]/60">
                            on {rule.barRef}
                          </span>
                        )}
                      </div>
                      <button
                              onClick={() => removeEntryRule(index)}
                        className={`${buttonStyles.remove} opacity-0 group-hover:opacity-100`}
                  >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                      </button>
                    </div>
                  ))}
                </div>

                {/* Group Operator Selection - Bottom Corner */}
                <div className="flex justify-between items-end mt-4">
                  <div className="flex gap-8">
                    {entryRules.filter(r => r.tradeType === 'long').length > 1 && (
                      <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                        <span className="block text-sm text-[#FEFEFF]/60 mb-2">Combine long entries with:</span>
                        <div className="flex gap-4">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={entryBuyGroupOperator === "AND"}
                              onChange={() => setEntryBuyGroupOperator("AND")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF]">AND</span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={entryBuyGroupOperator === "OR"}
                              onChange={() => setEntryBuyGroupOperator("OR")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF]">OR</span>
                          </label>
                        </div>
                      </div>
                    )}

                    {entryRules.filter(r => r.tradeType === 'short').length > 1 && (
                      <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                        <span className="block text-sm text-[#FEFEFF]/60 mb-2">Combine short entries with:</span>
                        <div className="flex gap-4">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={entrySellGroupOperator === "AND"}
                              onChange={() => setEntrySellGroupOperator("AND")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF]">AND</span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={entrySellGroupOperator === "OR"}
                              onChange={() => setEntrySellGroupOperator("OR")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF]">OR</span>
                          </label>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Add Rule Button */}
                  <button
                          onClick={addEntryRule}
                    className={buttonStyles.add}
                        >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Add Entry Rule
                  </button>
                </div>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(3)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(5)}
                disabled={entryRules.length === 0}
                className={`${buttonStyles.primary} ${
                  entryRules.length === 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 5: Exit Rules */}
        {currentStep === 5 && (
                            <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <h2 className="text-2xl font-bold text-[#FEFEFF] mb-6">Exit Rules</h2>

              {/* Exit Rules Configuration */}
              <div className="space-y-6">
                {/* Exit Rules List */}
                              <div className="space-y-4">
                  {exitRules.map((rule, index) => (
                    <div
                      key={rule.id}
                      className="flex items-center justify-between p-4 bg-[#2a2a2a] rounded-lg border border-[#3a3a3a] group hover:border-[#EFBD3A]/30 transition-all duration-200"
                    >
                      <div className="flex items-center gap-3">
                        <span className={`px-2 py-1 rounded ${
                          rule.tradeType === 'long' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                        }`}>
                          {rule.tradeType === 'long' ? 'Long' : 'Short'}
                        </span>
                        <span className="text-[#FEFEFF]">
                          {getIndicatorLabel(rule.indicator1, rule, false)}
                        </span>
                        <span className="text-[#EFBD3A]">{rule.operator}</span>
                        <span className="text-[#FEFEFF]">
                          {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                        </span>
                        {rule.indicator1 === 'price' && (
                          <span className="text-[#FEFEFF]/60">
                            on {rule.barRef}
                          </span>
                        )}
                      </div>
                      <button
                        onClick={() => removeExitRule(index)}
                        className={`${buttonStyles.remove} opacity-0 group-hover:opacity-100`}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                      </svg>
                      </button>
                      </div>
                  ))}
                    </div>

                {/* Group Operator Selection - Bottom Corner */}
                <div className="flex justify-between items-end mt-4">
                  <div className="flex gap-8">
                    {exitRules.filter(r => r.tradeType === 'long').length > 1 && (
                      <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                        <span className="block text-sm text-[#FEFEFF]/60 mb-2">Combine long exits with:</span>
                        <div className="flex gap-4">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={exitBuyGroupOperator === "AND"}
                              onChange={() => setExitBuyGroupOperator("AND")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF]">AND</span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={exitBuyGroupOperator === "OR"}
                              onChange={() => setExitBuyGroupOperator("OR")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF]">OR</span>
                          </label>
                </div>
                      </div>
                    )}

                    {exitRules.filter(r => r.tradeType === 'short').length > 1 && (
                      <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                        <span className="block text-sm text-[#FEFEFF]/60 mb-2">Combine short exits with:</span>
                        <div className="flex gap-4">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={exitSellGroupOperator === "AND"}
                              onChange={() => setExitSellGroupOperator("AND")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF]">AND</span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={exitSellGroupOperator === "OR"}
                              onChange={() => setExitSellGroupOperator("OR")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF]">OR</span>
                          </label>
                      </div>
                                  </div>
                    )}
                                </div>

                  {/* Add Rule Button */}
                  <button
                    onClick={addExitRule}
                    className={buttonStyles.add}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                      </svg>
                    Add Exit Rule
                  </button>
                      </div>
                      </div>
                                </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(4)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(6)}
                disabled={exitRules.length === 0}
                className={`${buttonStyles.primary} ${
                  exitRules.length === 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
                      </div>
          </motion.div>
        )}

        {/* Step 6: Risk Management */}
        {currentStep === 6 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <h2 className="text-2xl font-bold text-[#FEFEFF] mb-6">Risk Management</h2>

              {/* Risk Percentage */}
              <div className="mb-6">
                <div className="flex items-center mb-4">
                  <h3 className="text-lg font-semibold text-[#FEFEFF]">Risk Percentage</h3>
                  <div className="relative ml-2 group">
                    <div className="cursor-help w-5 h-5 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="absolute z-10 w-72 p-3 bg-[#2a2a2a] rounded-lg shadow-lg border border-[#3a3a3a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 left-6 top-0">
                      <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">How it works:</span></p>
                      <p>This percentage determines how much of your account you're willing to risk on each trade. For example, with a 2% risk on a $10,000 account, you would risk $200 per trade.</p>
                      <p className="mt-2">Your position size will be calculated based on this percentage and your stop loss distance.</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <label htmlFor="riskPercentage" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Risk Percentage (1-100%)
                    </label>
                    <input
                      type="number"
                      id="riskPercentage"
                      value={riskPercentage}
                      onChange={handleRiskPercentageChange}
                      min="0"
                      max="100"
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                      placeholder="Enter risk percentage..."
                    />
                  </div>
                </div>
                <p className="mt-2 text-sm text-[#FEFEFF]/60">
                  The percentage of your account balance you're willing to risk per trade.
                </p>
              </div>

              {/* Risk-Reward Ratio */}
              <div className="mb-6">
                <div className="flex items-center mb-4">
                  <h3 className="text-lg font-semibold text-[#FEFEFF]">Risk-Reward Ratio</h3>
                  <div className="relative ml-2 group">
                    <div className="cursor-help w-5 h-5 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="absolute z-10 w-72 p-3 bg-[#2a2a2a] rounded-lg shadow-lg border border-[#3a3a3a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 left-6 top-0">
                      <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">How it works:</span></p>
                      <p>This ratio determines how your take profit level is calculated relative to your stop loss. For example, with a 2:1 ratio:</p>
                      <ul className="list-disc list-inside mt-1 mb-1">
                        <li>If your stop loss is 50 pips, your take profit will be 100 pips</li>
                        <li>If you risk $100 on a trade, your potential profit will be $200</li>
                      </ul>
                      <p className="mt-1">A higher ratio improves your chances of long-term profitability even with a lower win rate.</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <label htmlFor="riskRewardRatio" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Risk-Reward Ratio
                    </label>
                    <input
                      type="number"
                      id="riskRewardRatio"
                      value={riskRewardRatio}
                      onChange={handleRiskRewardRatioChange}
                      min="0"
                      step="0.1"
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                      placeholder="Enter risk-reward ratio (e.g., 2.0)"
                    />
                  </div>
                </div>
                <p className="mt-2 text-sm text-[#FEFEFF]/60">
                  The multiplier for your potential profit relative to your risk. For example, a ratio of 2.0 means your potential profit is twice your potential loss.
                </p>
              </div>

              {/* Stop Loss Determination Method */}
              <div className="mb-6">
                <div className="flex items-center mb-4">
                  <h3 className="text-lg font-semibold text-[#FEFEFF]">Stop Loss Determination</h3>
                  <div className="relative ml-2 group">
                    <div className="cursor-help w-5 h-5 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="absolute z-10 w-80 p-3 bg-[#2a2a2a] rounded-lg shadow-lg border border-[#3a3a3a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 left-6 top-0">
                      <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">How it works:</span></p>
                      <p className="mb-2">Choose how your stop loss will be calculated:</p>
                      <ul className="list-disc list-inside space-y-1 mb-2">
                        <li><span className="font-semibold">Fixed Pips:</span> Sets a fixed distance for stop loss. Your lot size will be calculated based on your risk percentage.</li>
                        <li><span className="font-semibold">Indicator Based:</span> Uses technical indicators to dynamically set stop loss levels based on market conditions.</li>
                        <li><span className="font-semibold">Risk Based:</span> Uses a fixed lot size and calculates stop loss distance based on your risk percentage.</li>
                      </ul>
                      <p>Your take profit will be calculated using your risk-reward ratio multiplied by your stop loss distance.</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="stopLossMethod" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Stop Loss Method
                    </label>
                    <select
                      id="stopLossMethod"
                      value={stopLossMethod}
                      onChange={handleStopLossMethodChange}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="fixed">Fixed Pips</option>
                      <option value="indicator">Indicator Based</option>
                      <option value="risk">Risk Based</option>
                    </select>
                  </div>

                  {/* Fixed Pips */}
                  {stopLossMethod === 'fixed' && (
                    <div>
                      <div className="flex items-center mb-2">
                        <label htmlFor="fixedPips" className="text-sm font-medium text-[#FEFEFF]">
                          Fixed Pips
                        </label>
                        <div className="relative ml-2 group">
                          <div className="cursor-help w-4 h-4 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div className="absolute z-10 w-72 p-3 bg-[#2a2a2a] rounded-lg shadow-lg border border-[#3a3a3a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 left-6 top-0">
                            <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">Fixed Pips Calculation:</span></p>
                            <ul className="list-disc list-inside space-y-1">
                              <li><span className="font-semibold">Stop Loss:</span> Fixed distance of {fixedPips || "X"} pips from entry price</li>
                              <li><span className="font-semibold">Take Profit:</span> {fixedPips || "X"} pips × {riskRewardRatio || "Y"} (risk-reward ratio)</li>
                              <li><span className="font-semibold">Lot Size:</span> (Account Balance × Risk%) ÷ (Stop Loss in pips × Pip Value)</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <input
                        type="number"
                        id="fixedPips"
                        value={fixedPips}
                        onChange={handleFixedPipsChange}
                        className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                        placeholder="Enter fixed pips..."
                      />
                      {riskWarning && (
                          <div className="mt-2 p-3 bg-yellow-900/50 border border-yellow-600/50 rounded-lg">
                            <div className="flex items-start">
                              <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div className="ml-3">
                                <p className="text-sm text-yellow-300">{riskWarning}</p>
                              </div>
                            </div>
                          </div>
                      )}
                    </div>
                  )}

                  {/* Indicator Based */}
                  {stopLossMethod === 'indicator' && (
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center mb-2">
                          <label htmlFor="indicatorBasedSL" className="text-sm font-medium text-[#FEFEFF]">
                            Select Indicator
                          </label>
                          <div className="relative ml-2 group">
                            <div className="cursor-help w-4 h-4 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </div>
                            <div className="absolute z-10 w-80 p-3 bg-[#2a2a2a] rounded-lg shadow-lg border border-[#3a3a3a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 left-6 top-0">
                              <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">Indicator-Based Calculations:</span></p>
                              <ul className="list-disc list-inside space-y-1">
                                <li><span className="font-semibold">ATR:</span> Stop loss = Entry price ± (ATR value × multiplier)</li>
                                <li><span className="font-semibold">Bollinger Bands:</span> Stop loss = Lower/Upper band value</li>
                                <li><span className="font-semibold">Support & Resistance:</span> Stop loss = Nearest support/resistance level</li>
                                <li><span className="font-semibold">Take Profit:</span> Distance to stop loss × risk-reward ratio</li>
                                <li><span className="font-semibold">Lot Size:</span> (Account Balance × Risk%) ÷ (Stop Loss distance in pips × Pip Value)</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                        <select
                          id="indicatorBasedSL"
                          value={indicatorBasedSL}
                          onChange={handleIndicatorBasedSLChange}
                          className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                        >
                          <option value="">Select an indicator</option>
                          <option value="atr">ATR</option>
                          <option value="bollinger">Bollinger Bands</option>
                          <option value="support_resistance">Support & Resistance</option>
                        </select>
                      </div>

                      {/* Indicator Parameters */}
                      {indicatorBasedSL === 'atr' && (
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="atrPeriod" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              ATR Period
                            </label>
                            <input
                              type="number"
                              id="atrPeriod"
                              value={indicatorParams.period || 14}
                              onChange={(e) => handleIndicatorParamChange('period', e.target.value)}
                              min="1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label htmlFor="atrMultiplier" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              ATR Multiplier
                            </label>
                            <input
                              type="number"
                              id="atrMultiplier"
                              value={indicatorParams.multiplier || 2}
                              onChange={(e) => handleIndicatorParamChange('multiplier', e.target.value)}
                              min="0.1"
                              step="0.1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <button
                            onClick={() => {
                              console.log('Adding ATR indicator...');
                              handleStrategyModification();

                              // Set the adding state to true to show loading state
                              setIsAddingAtrToChart(true);

                              // First, remove any existing ATR indicators
                              // This prevents multiple dialogs from appearing and ensures parameters are updated
                              setSelectedIndicators(prev =>
                                prev.filter(ind => ind.type !== 'ATR')
                              );

                              // Also reset the state to ensure we get a fresh start
                              setIsAtrAddedToChart(false);
                              setIsAddingAtrToChart(false);
                              setPendingAtrIndicator(null);

                              // Create the ATR indicator object with a unique ID
                              const atrId = generateId();
                              const atrIndicator = {
                                id: atrId,
                                type: 'ATR',
                                parameters: {
                                  period: indicatorParams.period || 14,
                                  multiplier: indicatorParams.multiplier || 2
                                },
                                source: 'price',
                                // Add a flag to track this indicator's display preference
                                displayPreference: null
                              };
                              console.log('ATR indicator object:', atrIndicator);

                              // Store the pending indicator for the callback to use
                              setPendingAtrIndicator(atrIndicator);

                              // Add the indicator to the chart
                              // This will trigger the indicator dialog in StrategyChart
                              // The onIndicatorAdd callback will be called when the user makes a choice
                              setSelectedIndicators(prev => {
                                console.log('Previous indicators:', prev);
                                const newIndicators = [...prev, atrIndicator];
                                console.log('New indicators array:', newIndicators);
                                return newIndicators;
                              });
                            }}
                            disabled={isAtrAddedToChart || isAddingAtrToChart}
                            className={`w-full px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                              isAtrAddedToChart
                                ? 'bg-gradient-to-r from-green-500 to-green-600 text-white opacity-80 cursor-not-allowed'
                                : isAddingAtrToChart
                                  ? 'bg-[#EFBD3A]/70 text-[#0A0B0B] cursor-wait'
                                  : 'bg-[#EFBD3A] text-[#0A0B0B] hover:bg-[#EFBD3A]/90'
                            }`}
                          >
                            {isAtrAddedToChart
                              ? 'Added to Chart'
                              : isAddingAtrToChart
                                ? 'Adding...'
                                : 'Add to Chart'
                            }
                          </button>
                        </div>
                      )}

                      {indicatorBasedSL === 'bollinger' && (
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="bbPeriod" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              Period
                            </label>
                            <input
                              type="number"
                              id="bbPeriod"
                              value={indicatorParams.period || 20}
                              onChange={(e) => handleIndicatorParamChange('period', e.target.value)}
                              min="1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label htmlFor="bbStdDev" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              Standard Deviation
                            </label>
                            <input
                              type="number"
                              id="bbStdDev"
                              value={indicatorParams.stdDev || 2}
                              onChange={(e) => handleIndicatorParamChange('stdDev', e.target.value)}
                              min="0.1"
                              step="0.1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <button
                            onClick={() => {
                              console.log('Adding Bollinger Bands indicator...');
                              handleStrategyModification();

                              // Check if Bollinger Bands is already added from step 3
                              const existingBollingerBands = selectedIndicators.some(
                                ind => ind.type === 'BollingerBands'
                              );

                              if (existingBollingerBands) {
                                // If Bollinger Bands is already on the chart, just mark it as added
                                console.log('Bollinger Bands already exists on chart from step 3');
                                setIsBollingerAddedToChart(true);
                                return;
                              }

                              // Set the adding state to true to show loading state
                              setIsAddingBollingerToChart(true);

                              // First, remove any existing Bollinger Bands indicators added from risk management
                              // This prevents multiple dialogs from appearing and ensures parameters are updated
                              setSelectedIndicators(prev =>
                                prev.filter(ind => !(ind.type === 'BollingerBands' && ind.fromRiskManagement))
                              );

                              // Also reset the state to ensure we get a fresh start
                              setIsBollingerAddedToChart(false);
                              setIsAddingBollingerToChart(false);
                              setPendingBollingerIndicator(null);

                              // Create the Bollinger Bands indicator object with a unique ID
                              const bbId = generateId();
                              const bbIndicator = {
                                id: bbId,
                                type: 'BollingerBands',
                                parameters: {
                                  period: indicatorParams.period || 20,
                                  devfactor: indicatorParams.stdDev || 2,
                                  offset: 0
                                },
                                source: 'close',
                                // Add a flag to track this indicator's display preference
                                displayPreference: null,
                                // Mark this indicator as coming from risk management
                                fromRiskManagement: true
                              };
                              console.log('Bollinger Bands indicator object:', bbIndicator);

                              // Store the pending indicator for the callback to use
                              setPendingBollingerIndicator(bbIndicator);

                              // Add the indicator to the chart
                              // This will trigger the indicator dialog in StrategyChart
                              // The onIndicatorAdd callback will be called when the user makes a choice
                              setSelectedIndicators(prev => {
                                console.log('Previous indicators:', prev);
                                const newIndicators = [...prev, bbIndicator];
                                console.log('New indicators array:', newIndicators);
                                return newIndicators;
                              });
                            }}
                            disabled={isBollingerAddedToChart || isAddingBollingerToChart}
                            className={`w-full px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                              isBollingerAddedToChart
                                ? 'bg-gradient-to-r from-green-500 to-green-600 text-white opacity-80 cursor-not-allowed'
                                : isAddingBollingerToChart
                                  ? 'bg-[#EFBD3A]/70 text-[#0A0B0B] cursor-wait'
                                  : 'bg-[#EFBD3A] text-[#0A0B0B] hover:bg-[#EFBD3A]/90'
                            }`}
                          >
                            {isBollingerAddedToChart
                              ? 'Added to Chart'
                              : isAddingBollingerToChart
                                ? 'Adding...'
                                : 'Add to Chart'
                            }
                          </button>
                        </div>
                      )}

                      {indicatorBasedSL === 'support_resistance' && (
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="srLeft" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              Left Lookback Periods
                            </label>
                            <input
                              type="number"
                              id="srLeft"
                              value={indicatorParams.left || getDefaultIndicatorParams('support_resistance').left}
                              onChange={(e) => handleIndicatorParamChange('left', e.target.value)}
                              min="1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label htmlFor="srRight" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              Right Lookback Periods
                            </label>
                            <input
                              type="number"
                              id="srRight"
                              value={indicatorParams.right || getDefaultIndicatorParams('support_resistance').right}
                              onChange={(e) => handleIndicatorParamChange('right', e.target.value)}
                              min="1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <button
                            onClick={() => {
                              console.log('Adding Support & Resistance indicator...');
                              handleStrategyModification();

                              // Set the adding state to true to show loading state
                              setIsAddingSRToChart(true);

                              // First, remove any existing Support & Resistance indicators
                              // This prevents multiple dialogs from appearing and ensures parameters are updated
                              setSelectedIndicators(prev =>
                                prev.filter(ind => ind.type !== 'SupportResistance')
                              );

                              // Also reset the state to ensure we get a fresh start
                              setIsSRAddedToChart(false);
                              setIsAddingSRToChart(false);
                              setPendingSRIndicator(null);

                              // Create the Support & Resistance indicator object with a unique ID
                              const srId = generateId();
                              const srIndicator = {
                                id: srId,
                                type: 'SupportResistance',
                                parameters: {
                                  left: indicatorParams.left || getDefaultIndicatorParams('support_resistance').left,
                                  right: indicatorParams.right || getDefaultIndicatorParams('support_resistance').right
                                },
                                source: 'close',
                                // Add a flag to track this indicator's display preference
                                // Set displayPreference to 'new' directly since S&R doesn't show a dialog
                                displayPreference: 'new'
                              };
                              console.log('Support & Resistance indicator object:', srIndicator);

                              // Store the pending indicator for the callback to use
                              setPendingSRIndicator(srIndicator);

                              // Add the indicator to the chart
                              // Support & Resistance doesn't trigger a dialog, so we need to update the button state directly
                              setSelectedIndicators(prev => {
                                console.log('Previous indicators:', prev);
                                const newIndicators = [...prev, srIndicator];
                                console.log('New indicators array:', newIndicators);

                                // Since Support & Resistance doesn't show a dialog, we need to update the button state directly
                                // Set a timeout to allow the chart to update first
                                setTimeout(() => {
                                  setIsSRAddedToChart(true);
                                  setIsAddingSRToChart(false);
                                }, 500);

                                return newIndicators;
                              });
                            }}
                            disabled={isSRAddedToChart || isAddingSRToChart}
                            className={`w-full px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                              isSRAddedToChart
                                ? 'bg-gradient-to-r from-green-500 to-green-600 text-white opacity-80 cursor-not-allowed'
                                : isAddingSRToChart
                                  ? 'bg-[#EFBD3A]/70 text-[#0A0B0B] cursor-wait'
                                  : 'bg-[#EFBD3A] text-[#0A0B0B] hover:bg-[#EFBD3A]/90'
                            }`}
                          >
                            {isSRAddedToChart
                              ? 'Added to Chart'
                              : isAddingSRToChart
                                ? 'Adding...'
                                : 'Add to Chart'
                            }
                          </button>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Risk Based */}
                  {stopLossMethod === 'risk' && (
                    <div>
                      <div className="flex items-center mb-2">
                        <label htmlFor="lotSize" className="text-sm font-medium text-[#FEFEFF]">
                          Lot Size
                        </label>
                        <div className="relative ml-2 group">
                          <div className="cursor-help w-4 h-4 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div className="absolute z-10 w-72 p-3 bg-[#2a2a2a] rounded-lg shadow-lg border border-[#3a3a3a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 left-6 top-0">
                            <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">Risk-Based Calculation:</span></p>
                            <ul className="list-disc list-inside space-y-1">
                              <li><span className="font-semibold">Lot Size:</span> Fixed at {lotSize || "X"} lots</li>
                              <li><span className="font-semibold">Stop Loss:</span> (Account Balance × Risk%) ÷ (Lot Size × Pip Value)</li>
                              <li><span className="font-semibold">Take Profit:</span> Stop Loss distance × {riskRewardRatio || "Y"} (risk-reward ratio)</li>
                            </ul>
                            <p className="mt-2 text-xs">This method uses a fixed lot size and calculates the stop loss distance based on your risk percentage.</p>
                          </div>
                        </div>
                      </div>
                      <input
                        type="number"
                        id="lotSize"
                        value={lotSize}
                        onChange={handleLotSizeChange}
                        className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                        placeholder="Enter lot size..."
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Risk Management Tips */}
              <div className="mt-8 p-4 bg-[#EFBD3A]/10 border border-[#EFBD3A]/20 rounded-lg">
                <h4 className="text-[#EFBD3A] font-semibold mb-2">Risk Management Tips</h4>
                <ul className="list-disc list-inside space-y-2 text-sm text-[#FEFEFF]/80">
                  <li>Risk Percentage: {riskPercentage ?
                    <>Keep your risk at {riskPercentage}% or lower per trade to ensure long-term account sustainability. {Number(riskPercentage) > 2 ?
                      <span className="text-red-400">Warning: Risk above 2% may lead to significant drawdowns during losing streaks.</span> :
                      <span className="text-green-400">Good choice! Risk of 1-2% is considered optimal by professional traders.</span>}</> :
                    'Consider using a risk of 1-2% of your account balance per trade.'}</li>

                  <li>Risk-Reward Ratio: {riskRewardRatio ?
                    <>Your {riskRewardRatio}:1 risk-reward ratio means you aim to make ${riskRewardRatio} for every $1 risked. {Number(riskRewardRatio) >= 1.5 ?
                      <span className="text-green-400">This ratio allows for profitability even with a win rate below 50%.</span> :
                      <span className="text-yellow-400">Consider increasing your ratio to at least 1.5:1 for better long-term results.</span>}</> :
                    'Aim for a risk-reward ratio of at least 1.5:1 to maintain profitability.'}</li>

                  {stopLossMethod === 'fixed' && (
                    <li>Fixed Pips Stop Loss: Using a {fixedPips} pip stop loss provides consistency but may not adapt to market volatility. Consider using wider stops in volatile markets and tighter stops in ranging markets.</li>
                  )}

                  {stopLossMethod === 'indicator' && indicatorBasedSL === 'atr' && (
                    <li>ATR-Based Stop Loss: Using ATR (Average True Range) with a period of {indicatorParams.period || 14} and multiplier of {indicatorParams.multiplier || 2} adapts your stop loss to market volatility. This is excellent for volatile markets where fixed stops might be hit too frequently.</li>
                  )}

                  {stopLossMethod === 'indicator' && indicatorBasedSL === 'bollinger' && (
                    <li>Bollinger Bands Stop Loss: Using Bollinger Bands with a period of {indicatorParams.period || 20} and standard deviation of {indicatorParams.stdDev || 2} provides dynamic support and resistance levels. This works well in trending markets but be cautious in highly volatile conditions.</li>
                  )}

                  {stopLossMethod === 'indicator' && indicatorBasedSL === 'support_resistance' && (
                    <li>Support & Resistance Stop Loss: Using key market levels with {indicatorParams.left || 5} left and {indicatorParams.right || 5} right lookback periods can provide strong stop loss points. This method works best in ranging markets or at major trend reversal points.</li>
                  )}

                  {stopLossMethod === 'risk' && (
                    <li>Risk-Based Position Sizing: Using a fixed lot size of {lotSize} lots means your position size remains constant. For better risk management, consider adjusting lot size based on your stop loss distance to maintain consistent risk percentage.</li>
                  )}

                  <li>Always use a combination of stop losses and take profits to automate your risk management and remove emotional decision-making.</li>

                </ul>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(5)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(7)}
                disabled={!riskPercentage || !riskRewardRatio || !stopLossMethod}
                className={`${buttonStyles.primary} ${
                  (!riskPercentage || !riskRewardRatio || !stopLossMethod) ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 7: Review */}
        {currentStep === 7 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-[#FEFEFF]">Strategy Review</h2>
                                    <button
                  onClick={handleSaveStrategy}
                  disabled={isSavingStrategy || (isStrategySaved && !hasBeenModified)}
                  className={`${buttonStyles.primary} ${
                    (isSavingStrategy || (isStrategySaved && !hasBeenModified)) ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {(() => {
                    // Add detailed debug logging to help diagnose the issue
                    console.log("%c Button rendering state:", "background: #333; color: #ff0; font-weight: bold; padding: 2px 5px;");
                    console.log(`- isSavingStrategy: ${isSavingStrategy}`);
                    console.log(`- isStrategySaved: ${isStrategySaved}`);
                    console.log(`- hasBeenModified: ${hasBeenModified}`);
                    console.log(`- editingStrategyId: ${editingStrategyId}`);
                    console.log(`- localStorage editingStrategyId: ${localStorage.getItem("editingStrategyId")}`);
                    console.log(`- savedStrategies count: ${savedStrategies.length}`);

                    // Get editingStrategyId from localStorage if not available in state
                    const effectiveEditingId = editingStrategyId || localStorage.getItem("editingStrategyId");
                    console.log(`- effectiveEditingId: ${effectiveEditingId}`);

                    // Log which button will be shown
                    if (isSavingStrategy) {
                      console.log("Showing: Saving Strategy...");
                    } else if (isStrategySaved && !hasBeenModified) {
                      console.log("Showing: Strategy Saved");
                    } else if (effectiveEditingId) {
                      console.log("Showing: Update Strategy");
                    } else {
                      console.log("Showing: Save Strategy");
                    }

                    if (isSavingStrategy) {
                      return (
                        <>
                          <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Saving Strategy...
                        </>
                      );
                    } else if (isStrategySaved && !hasBeenModified) {
                      return (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Strategy Saved
                        </>
                      );
                    } else if (effectiveEditingId) {
                      return (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          Update Strategy
                        </>
                      );
                    } else {
                      return (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                          </svg>
                          Save Strategy
                        </>
                      );
                    }
                  })()}
                                    </button>
                </div>

              {/* Show success message when strategy is saved */}
              {isStrategySaved && !hasBeenModified && (
                <div className="mb-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <p className="text-green-400">
                    <span className="font-semibold">Strategy successfully saved!</span> You can find this strategy in the Strategy Library anytime.
                  </p>
                  </div>
                )}

              {/* Strategy Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                  <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Basic Information</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-[#FEFEFF]/60">Strategy Name:</span>
                      <span className="text-[#FEFEFF] ml-2">{strategyName}</span>
                    </div>
                    <div>
                      <span className="text-[#FEFEFF]/60">Forex Pair:</span>
                      <span className="text-[#FEFEFF] ml-2">{forexPair}</span>
                    </div>
                    <div>
                      <span className="text-[#FEFEFF]/60">Timeframe:</span>
                      <span className="text-[#FEFEFF] ml-2">{timeframe}</span>
                    </div>
                    <div>
                      <span className="text-[#FEFEFF]/60">Trading Sessions:</span>
                      <span className="text-[#FEFEFF] ml-2">
                        {selectedTradingSessions.length === 0 ? 'All Sessions' :
                          selectedTradingSessions.map(session =>
                            tradingSessions.find(s => s.timezone === session)?.name
                          ).join(', ')}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                  <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Risk Management</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-[#FEFEFF]/60">Risk Percentage:</span>
                      <span className="text-[#FEFEFF] ml-2">{riskPercentage}%</span>
                    </div>
                    <div>
                      <span className="text-[#FEFEFF]/60">Risk-Reward Ratio:</span>
                      <span className="text-[#FEFEFF] ml-2">{riskRewardRatio}:1</span>
                    </div>
                    <div>
                      <span className="text-[#FEFEFF]/60">Stop Loss Method:</span>
                      <span className="text-[#FEFEFF] ml-2">
                        {(() => {
                          switch (stopLossMethod) {
                            case 'fixed':
                              return `Fixed (${fixedPips} pips)`;
                            case 'indicator':
                              return `${indicatorBasedSL} Based`;
                            case 'risk':
                              return `Risk Based (${lotSize} lots)`;
                            default:
                              return 'Not Set';
                          }
                        })()}
                      </span>
                    </div>
                    {stopLossMethod === 'indicator' && indicatorBasedSL && (
                      <div>
                        <span className="text-[#FEFEFF]/60">Indicator Parameters:</span>
                        <span className="text-[#FEFEFF] ml-2">
                          {(() => {
                            // 1. Use indicatorParams if not empty
                            if (indicatorParams && Object.keys(indicatorParams).length > 0) {
                              return Object.entries(indicatorParams)
                                .map(([key, value]) => `${key}: ${value}`)
                                .join(', ');
                            }
                            // 2. Use generatedStrategy if available
                            if (generatedStrategy && generatedStrategy.riskManagement && generatedStrategy.riskManagement.indicatorBasedSL && generatedStrategy.riskManagement.indicatorBasedSL.parameters && Object.keys(generatedStrategy.riskManagement.indicatorBasedSL.parameters).length > 0) {
                              return Object.entries(generatedStrategy.riskManagement.indicatorBasedSL.parameters)
                                .map(([key, value]) => `${key}: ${value}`)
                                .join(', ');
                            }
                            // 3. Fallback to defaults
                            const paramsToShow = getDefaultIndicatorParams(indicatorBasedSL);
                            return Object.entries(paramsToShow)
                              .map(([key, value]) => `${key}: ${value}`)
                              .join(', ');
                          })()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Technical Indicators */}
              <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a] mb-6">
                <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Technical Indicators</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {selectedIndicators.map((indicator) => (
                    <div
                      key={indicator.id}
                      className="bg-[#1a1a1a] rounded-lg p-3 border border-[#3a3a3a]"
                    >
                      <div className="font-medium text-[#FEFEFF]">{indicator.type}</div>
                      <div className="text-sm text-[#FEFEFF]/60">
                        {Object.entries(indicator.parameters)
                          .map(([k, v]) => `${k}: ${v}`)
                          .join(", ")}
                      </div>
                      <div className="text-sm text-[#EFBD3A]/80 mt-1">
                        Source: {indicator.source === "volume" ? "Volume" :
                                indicator.source === "open" ? "Open" :
                                indicator.source === "close" ? "Close" :
                                indicator.source === "high" ? "High" :
                                indicator.source === "low" ? "Low" :
                                selectedIndicators.find(ind => ind.id === indicator.source)?.type || "Price"}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Trading Rules */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Entry Rules */}
                <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                  <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Entry Rules</h3>
                  <div className="space-y-4">
                    {/* Long Entry Rules */}
                    {entryRules.filter(r => r.tradeType === 'long').length > 0 && (
                      <div>
                        <h4 className="text-green-400 font-medium mb-2">Long Entry Conditions</h4>
                        <div className="space-y-2">
                          {entryRules
                            .filter(r => r.tradeType === 'long')
                            .map(rule => (
                              <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#1a1a1a] p-2 rounded">
                                {getIndicatorLabel(rule.indicator1)}
                                {' '}
                                <span className="text-[#EFBD3A]">{rule.operator}</span>
                                {' '}
                                {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2)}
                                {rule.indicator1 === 'price' && (
                                  <>
                                    {' '}
                                    <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
                                  </>
                                )}
                              </div>
                            ))}
                          {entryRules.filter(r => r.tradeType === 'long').length > 1 && (
                            <div className="text-xs text-[#FEFEFF]/60 mt-1">
                              Combined with: {entryBuyGroupOperator}
              </div>
        )}
                      </div>
                      </div>
                    )}

                    {/* Short Entry Rules */}
                    {entryRules.filter(r => r.tradeType === 'short').length > 0 && (
                      <div>
                        <h4 className="text-red-400 font-medium mb-2">Short Entry Conditions</h4>
                        <div className="space-y-2">
                          {entryRules
                            .filter(r => r.tradeType === 'short')
                            .map(rule => (
                              <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#1a1a1a] p-2 rounded">
                                {getIndicatorLabel(rule.indicator1)}
                                {' '}
                                <span className="text-[#EFBD3A]">{rule.operator}</span>
                                {' '}
                                {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2)}
                                {rule.indicator1 === 'price' && (
                                  <>
                                    {' '}
                                    <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
                                  </>
                                )}
                      </div>
                            ))}
                          {entryRules.filter(r => r.tradeType === 'short').length > 1 && (
                            <div className="text-xs text-[#FEFEFF]/60 mt-1">
                              Combined with: {entrySellGroupOperator}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Exit Rules */}
                <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                  <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Exit Rules</h3>
                <div className="space-y-4">
                    {/* Long Exit Rules */}
                    {exitRules.filter(r => r.tradeType === 'long').length > 0 && (
                      <div>
                        <h4 className="text-green-400 font-medium mb-2">Long Exit Conditions</h4>
                        <div className="space-y-2">
                          {exitRules
                            .filter(r => r.tradeType === 'long')
                            .map(rule => (
                              <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#1a1a1a] p-2 rounded">
                                {getIndicatorLabel(rule.indicator1)}
                                {' '}
                                <span className="text-[#EFBD3A]">{rule.operator}</span>
                                {' '}
                                {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2)}
                                {rule.indicator1 === 'price' && (
                                  <>
                                    {' '}
                                    <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
                                  </>
                                )}
                              </div>
                            ))}
                          {exitRules.filter(r => r.tradeType === 'long').length > 1 && (
                            <div className="text-xs text-[#FEFEFF]/60 mt-1">
                              Combined with: {exitBuyGroupOperator}
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Short Exit Rules */}
                    {exitRules.filter(r => r.tradeType === 'short').length > 0 && (
                      <div>
                        <h4 className="text-red-400 font-medium mb-2">Short Exit Conditions</h4>
                        <div className="space-y-2">
                          {exitRules
                            .filter(r => r.tradeType === 'short')
                            .map(rule => (
                              <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#1a1a1a] p-2 rounded">
                                {getIndicatorLabel(rule.indicator1)}
                                {' '}
                                <span className="text-[#EFBD3A]">{rule.operator}</span>
                                {' '}
                                {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2)}
                                {rule.indicator1 === 'price' && (
                                  <>
                                    {' '}
                                    <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
                                  </>
                                )}
                              </div>
                            ))}
                          {exitRules.filter(r => r.tradeType === 'short').length > 1 && (
                            <div className="text-xs text-[#FEFEFF]/60 mt-1">
                              Combined with: {exitSellGroupOperator}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Backtest Section */}
              <div className="mt-8">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-[#FEFEFF]">Backtest Results</h3>
                  <button
                    onClick={() => setIsBacktestDialogOpen(true)}
                    className="px-6 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 flex items-center gap-2"
                    disabled={!isStrategyFinalized}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    Run Backtest
                  </button>
                </div>

                {backtestResults ? (
                  <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                    <BacktestResults results={backtestResults} />
                  </div>
                ) : (
                  <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a] text-center">
                    <p className="text-[#FEFEFF]/60">
                      No backtest results available. Click "Run Backtest" to evaluate your strategy's performance.
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(6)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
            </div>
          </motion.div>
        )}

        {/* Entry Rule Modal */}
        {isEntryRuleModalOpen && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsEntryRuleModalOpen(false)} />
            <div className="relative bg-[#1a1a1a] rounded-xl p-6 w-full max-w-2xl shadow-lg">
              <h3 className="text-xl font-bold text-[#FEFEFF] mb-6">Add Entry Rule</h3>

                              <div className="space-y-4">
                {/* Trade Type Selection */}
                        <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Trade Type
                  </label>
                  <div className="flex gap-4">
                    <button
                      onClick={() => setNewEntryRule(prev => ({ ...prev, tradeType: 'long' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newEntryRule.tradeType === 'long'
                          ? 'bg-green-500/20 border-green-500/50 text-green-400'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Long
                    </button>
                    <button
                      onClick={() => setNewEntryRule(prev => ({ ...prev, tradeType: 'short' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newEntryRule.tradeType === 'short'
                          ? 'bg-red-500/20 border-red-500/50 text-red-400'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Short
                    </button>
                                  </div>
                                </div>

                {/* First Indicator Selection */}
                <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    First Indicator
                  </label>
                  <select
                    value={newEntryRule.indicator1}
                    onChange={(e) => {
                      const value = e.target.value;
                      setNewEntryRule(prev => ({
                        ...prev,
                        indicator1: value,
                        // Reset barRef to empty string if not price
                        barRef: value === 'price' ? prev.barRef : '',
                        // Reset band if changing from a Bollinger Bands indicator
                        band: undefined,
                        // Reset macdComponent if changing from a MACD indicator
                        macdComponent: undefined
                      }));
                    }}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                  >
                    <option value="">Select an indicator...</option>
                    <option value="price">Price</option>
                    {selectedIndicators.map((ind) => (
                      <option key={ind.id} value={ind.id}>
                        {ind.type} ({Object.entries(ind.parameters)
                          .map(([k, v]) => `${k}: ${v}`)
                          .join(", ")})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Bar Reference Selection - Only show when price is selected */}
                {newEntryRule.indicator1 === 'price' && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Bar Reference
                    </label>
                    <select
                      value={newEntryRule.barRef}
                      onChange={(e) => setNewEntryRule(prev => ({ ...prev, barRef: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="open">Open</option>
                      <option value="high">High</option>
                      <option value="low">Low</option>
                      <option value="close">Close</option>
                    </select>
                  </div>
                )}

                {/* Bollinger Band Selection - Only show when a Bollinger Bands indicator is selected */}
                {newEntryRule.indicator1 && selectedIndicators.find(ind => ind.id === newEntryRule.indicator1 && ind.type === 'BollingerBands') && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Bollinger Band
                    </label>
                    <select
                      value={newEntryRule.band || 'middle'}
                      onChange={(e) => setNewEntryRule(prev => ({ ...prev, band: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="upper">Upper Band</option>
                      <option value="middle">Middle Band (SMA)</option>
                      <option value="lower">Lower Band</option>
                    </select>
                  </div>
                )}

                {/* MACD Component Selection - Only show when a MACD indicator is selected */}
                {newEntryRule.indicator1 && selectedIndicators.find(ind => ind.id === newEntryRule.indicator1 && ind.type === 'MACD') && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      MACD Component
                    </label>
                    <select
                      value={newEntryRule.macdComponent || 'macd'}
                      onChange={(e) => setNewEntryRule(prev => ({ ...prev, macdComponent: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="macd">MACD Line</option>
                      <option value="signal">Signal Line</option>
                    </select>
                  </div>
                )}

                {/* Operator Selection */}
                        <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Operator
                  </label>
                        <select
                    value={newEntryRule.operator}
                    onChange={(e) => setNewEntryRule(prev => ({ ...prev, operator: e.target.value }))}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                        >
                          {conditionOperators.map((op) => (
                      <option key={op} value={op}>
                        {op}
                      </option>
                          ))}
                        </select>
                                </div>

                {/* Compare Type Selection */}
                        <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Compare Against
                  </label>
                  <div className="flex gap-4">
                    <button
                      onClick={() => setNewEntryRule(prev => ({ ...prev, compareType: 'value' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newEntryRule.compareType === 'value'
                          ? 'bg-[#EFBD3A]/20 border-[#EFBD3A]/50 text-[#EFBD3A]'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Value
                    </button>
                    <button
                      onClick={() => setNewEntryRule(prev => ({ ...prev, compareType: 'indicator' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newEntryRule.compareType === 'indicator'
                          ? 'bg-[#EFBD3A]/20 border-[#EFBD3A]/50 text-[#EFBD3A]'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Indicator
                    </button>
                      </div>
              </div>

                {/* Value or Second Indicator */}
                {newEntryRule.compareType === 'value' ? (
                        <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Value
                    </label>
                                    <input
                                      type="number"
                      value={newEntryRule.value}
                      onChange={(e) => setNewEntryRule(prev => ({ ...prev, value: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                      placeholder="Enter value..."
                                    />
                                  </div>
                ) : (
                                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Second Indicator
                    </label>
                            <select
                      value={newEntryRule.indicator2}
                      onChange={(e) => {
                        const value = e.target.value;
                        setNewEntryRule(prev => ({
                          ...prev,
                          indicator2: value,
                          // Reset band2 if changing from a Bollinger Bands indicator
                          band2: undefined,
                          // Reset macdComponent2 if changing from a MACD indicator
                          macdComponent2: undefined
                        }));
                      }}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="">Select an indicator...</option>
                      <option value="price">Price</option>
                              {selectedIndicators.map((ind) => (
                                <option key={ind.id} value={ind.id}>
                                            {ind.type} ({Object.entries(ind.parameters)
                                              .map(([k, v]) => `${k}: ${v}`)
                                              .join(", ")})
                                </option>
                              ))}
                            </select>

                            {/* Bollinger Band Selection for Second Indicator - Only show when a Bollinger Bands indicator is selected */}
                            {newEntryRule.indicator2 && selectedIndicators.find(ind => ind.id === newEntryRule.indicator2 && ind.type === 'BollingerBands') && (
                              <div className="mt-4">
                                <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                                  Bollinger Band (Second Indicator)
                                </label>
                                <select
                                  value={newEntryRule.band2 || 'middle'}
                                  onChange={(e) => setNewEntryRule(prev => ({ ...prev, band2: e.target.value }))}
                                  className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                                >
                                  <option value="upper">Upper Band</option>
                                  <option value="middle">Middle Band (SMA)</option>
                                  <option value="lower">Lower Band</option>
                                </select>
                              </div>
                            )}

                            {/* MACD Component Selection for Second Indicator - Only show when a MACD indicator is selected */}
                            {newEntryRule.indicator2 && selectedIndicators.find(ind => ind.id === newEntryRule.indicator2 && ind.type === 'MACD') && (
                              <div className="mt-4">
                                <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                                  MACD Component (Second Indicator)
                                </label>
                                <select
                                  value={newEntryRule.macdComponent2 || 'macd'}
                                  onChange={(e) => setNewEntryRule(prev => ({ ...prev, macdComponent2: e.target.value }))}
                                  className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                                >
                                  <option value="macd">MACD Line</option>
                                  <option value="signal">Signal Line</option>
                                </select>
                              </div>
                            )}
              </div>
                                )}
              </div>

              {/* Modal Actions */}
              <div className="flex justify-end gap-4 mt-6">
                                    <button
                  onClick={() => setIsEntryRuleModalOpen(false)}
                  className={buttonStyles.secondary}
                >
                  Cancel
                                    </button>
                                    <button
                  onClick={handleAddEntryRule}
                  disabled={!newEntryRule.indicator1 || (newEntryRule.compareType === 'value' ? !newEntryRule.value : !newEntryRule.indicator2)}
                  className={`${buttonStyles.primary} ${
                    (!newEntryRule.indicator1 || (newEntryRule.compareType === 'value' ? !newEntryRule.value : !newEntryRule.indicator2))
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }`}
                >
                  Add Rule
                                    </button>
              </div>
              </div>
              </div>
                            )}

        {/* Exit Rule Modal */}
        {isExitRuleModalOpen && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsExitRuleModalOpen(false)} />
            <div className="relative bg-[#1a1a1a] rounded-xl p-6 w-full max-w-2xl shadow-lg">
              <h3 className="text-xl font-bold text-[#FEFEFF] mb-6">Add Exit Rule</h3>

              <div className="space-y-4">
                {/* Trade Type Selection */}
                              <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Trade Type
                  </label>
                  <div className="flex gap-4">
                                    <button
                      onClick={() => setNewExitRule(prev => ({ ...prev, tradeType: 'long' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newExitRule.tradeType === 'long'
                          ? 'bg-green-500/20 border-green-500/50 text-green-400'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Long
                                    </button>
                                    <button
                      onClick={() => setNewExitRule(prev => ({ ...prev, tradeType: 'short' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newExitRule.tradeType === 'short'
                          ? 'bg-red-500/20 border-red-500/50 text-red-400'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Short
                                    </button>
            </div>
            </div>

                {/* First Indicator Selection */}
                <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    First Indicator
                  </label>
                  <select
                    value={newExitRule.indicator1}
                    onChange={(e) => {
                      const value = e.target.value;
                      setNewExitRule(prev => ({
                        ...prev,
                        indicator1: value,
                        // Reset barRef to empty string if not price
                        barRef: value === 'price' ? prev.barRef : '',
                        // Reset band if changing from a Bollinger Bands indicator
                        band: undefined,
                        // Reset macdComponent if changing from a MACD indicator
                        macdComponent: undefined
                      }));
                    }}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                  >
                    <option value="">Select an indicator...</option>
                    <option value="price">Price</option>
                    {selectedIndicators.map((ind) => (
                      <option key={ind.id} value={ind.id}>
                        {ind.type} ({Object.entries(ind.parameters)
                          .map(([k, v]) => `${k}: ${v}`)
                          .join(", ")})
                      </option>
                    ))}
                  </select>
                    </div>

                {/* Bar Reference Selection - Only show when price is selected */}
                {newExitRule.indicator1 === 'price' && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Bar Reference
                    </label>
                    <select
                      value={newExitRule.barRef}
                      onChange={(e) => setNewExitRule(prev => ({ ...prev, barRef: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="open">Open</option>
                      <option value="high">High</option>
                      <option value="low">Low</option>
                      <option value="close">Close</option>
                    </select>
                  </div>
                )}

                {/* Bollinger Band Selection - Only show when a Bollinger Bands indicator is selected */}
                {newExitRule.indicator1 && selectedIndicators.find(ind => ind.id === newExitRule.indicator1 && ind.type === 'BollingerBands') && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Bollinger Band
                    </label>
                    <select
                      value={newExitRule.band || 'middle'}
                      onChange={(e) => setNewExitRule(prev => ({ ...prev, band: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="upper">Upper Band</option>
                      <option value="middle">Middle Band (SMA)</option>
                      <option value="lower">Lower Band</option>
                    </select>
                  </div>
                )}

                {/* MACD Component Selection - Only show when a MACD indicator is selected */}
                {newExitRule.indicator1 && selectedIndicators.find(ind => ind.id === newExitRule.indicator1 && ind.type === 'MACD') && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      MACD Component
                    </label>
                    <select
                      value={newExitRule.macdComponent || 'macd'}
                      onChange={(e) => setNewExitRule(prev => ({ ...prev, macdComponent: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="macd">MACD Line</option>
                      <option value="signal">Signal Line</option>
                    </select>
                  </div>
                )}

                {/* Operator Selection */}
                <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Operator
                  </label>
                  <select
                    value={newExitRule.operator}
                    onChange={(e) => setNewExitRule(prev => ({ ...prev, operator: e.target.value }))}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                  >
                    {conditionOperators.map((op) => (
                      <option key={op} value={op}>
                        {op}
                      </option>
                    ))}
                  </select>
                            </div>

                {/* Compare Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Compare Against
                  </label>
                  <div className="flex gap-4">
                  <button
                      onClick={() => setNewExitRule(prev => ({ ...prev, compareType: 'value' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newExitRule.compareType === 'value'
                          ? 'bg-[#EFBD3A]/20 border-[#EFBD3A]/50 text-[#EFBD3A]'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Value
                  </button>
                  <button
                      onClick={() => setNewExitRule(prev => ({ ...prev, compareType: 'indicator' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newExitRule.compareType === 'indicator'
                          ? 'bg-[#EFBD3A]/20 border-[#EFBD3A]/50 text-[#EFBD3A]'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Indicator
                  </button>
                </div>
            </div>

                {/* Value or Second Indicator */}
                {newExitRule.compareType === 'value' ? (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Value
                    </label>
                            <input
                              type="number"
                      value={newExitRule.value}
                      onChange={(e) => setNewExitRule(prev => ({ ...prev, value: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                      placeholder="Enter value..."
                    />
        </div>
                ) : (
                                <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Second Indicator
                    </label>
                    <select
                      value={newExitRule.indicator2}
                      onChange={(e) => {
                        const value = e.target.value;
                        setNewExitRule(prev => ({
                          ...prev,
                          indicator2: value,
                          // Reset band2 if changing from a Bollinger Bands indicator
                          band2: undefined,
                          // Reset macdComponent2 if changing from a MACD indicator
                          macdComponent2: undefined
                        }));
                      }}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="">Select an indicator...</option>
                      <option value="price">Price</option>
                      {selectedIndicators.map((ind) => (
                        <option key={ind.id} value={ind.id}>
                          {ind.type} ({Object.entries(ind.parameters)
                            .map(([k, v]) => `${k}: ${v}`)
                            .join(", ")})
                        </option>
                      ))}
                    </select>

                    {/* Bollinger Band Selection for Second Indicator - Only show when a Bollinger Bands indicator is selected */}
                    {newExitRule.indicator2 && selectedIndicators.find(ind => ind.id === newExitRule.indicator2 && ind.type === 'BollingerBands') && (
                      <div className="mt-4">
                        <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                          Bollinger Band (Second Indicator)
                        </label>
                        <select
                          value={newExitRule.band2 || 'middle'}
                          onChange={(e) => setNewExitRule(prev => ({ ...prev, band2: e.target.value }))}
                          className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                        >
                          <option value="upper">Upper Band</option>
                          <option value="middle">Middle Band (SMA)</option>
                          <option value="lower">Lower Band</option>
                        </select>
                      </div>
                    )}

                    {/* MACD Component Selection for Second Indicator - Only show when a MACD indicator is selected */}
                    {newExitRule.indicator2 && selectedIndicators.find(ind => ind.id === newExitRule.indicator2 && ind.type === 'MACD') && (
                      <div className="mt-4">
                        <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                          MACD Component (Second Indicator)
                        </label>
                        <select
                          value={newExitRule.macdComponent2 || 'macd'}
                          onChange={(e) => setNewExitRule(prev => ({ ...prev, macdComponent2: e.target.value }))}
                          className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                        >
                          <option value="macd">MACD Line</option>
                          <option value="signal">Signal Line</option>
                        </select>
                      </div>
                    )}
          </div>
        )}
                </div>

              {/* Modal Actions */}
              <div className="flex justify-end gap-4 mt-6">
                <button
                  onClick={() => setIsExitRuleModalOpen(false)}
                  className={buttonStyles.secondary}
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddExitRule}
                  disabled={!newExitRule.indicator1 || (newExitRule.compareType === 'value' ? !newExitRule.value : !newExitRule.indicator2)}
                  className={`${buttonStyles.primary} ${
                    (!newExitRule.indicator1 || (newExitRule.compareType === 'value' ? !newExitRule.value : !newExitRule.indicator2))
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }`}
                >
                  Add Rule
                </button>
                              </div>
                            </div>
          </div>
        )}

        {/* Other steps will be added here */}
        {/* Add BacktestDialog */}
        {isBacktestDialogOpen && (
          <BacktestDialogWrapper
            isOpen={isBacktestDialogOpen}
            onClose={handleCloseBacktestDialog}
            strategy={generatedStrategy}
            onBacktestComplete={(results) => {
              setBacktestResults(results);
              handleCloseBacktestDialog();
              // Show success toast
              toast.success('Backtest completed successfully!', {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
              });
            }}
          />
        )}
      </div>
    </DashboardLayout>
  );
}