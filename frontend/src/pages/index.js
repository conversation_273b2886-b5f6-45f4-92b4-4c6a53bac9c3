import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import PrivacyPolicyModal from '../components/PrivacyPolicyModal';
import TermsOfServiceModal from '../components/TermsOfServiceModal';
import ContactModal from '../components/ContactModal';
import ScrollArrow from '../components/ScrollArrow';
import { USE_FIREBASE_EMULATOR } from '../config';
import { getFirestore, collection, onSnapshot } from 'firebase/firestore';
import { app } from '../../firebaseConfig';
import { toast } from 'react-hot-toast';

export default function Home() {
  const [email, setEmail] = useState('');
  const [feedback, setFeedback] = useState({});
  const [isPrivacyPolicyOpen, setIsPrivacyPolicyOpen] = useState(false);
  const [isTermsOfServiceOpen, setIsTermsOfServiceOpen] = useState(false);
  const [isContactOpen, setIsContactOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);
  const [selectedFeature, setSelectedFeature] = useState(null);
  const [voteCounts, setVoteCounts] = useState({});
  const [loading, setLoading] = useState(true);
  const [showScrollArrow, setShowScrollArrow] = useState(true);
  const sectionsRef = useRef([]);

  useEffect(() => {
    // Get all sections
    const sections = document.querySelectorAll('section');
    sectionsRef.current = Array.from(sections);
    
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      
      // Show arrow if we're not at the bottom of the page
      setShowScrollArrow(scrollPosition + windowHeight < documentHeight - 100);
    };

    window.addEventListener('scroll', handleScroll);
    // Initial check
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToNextSection = () => {
    // Find the current section in view
    let currentSectionIndex = -1;
    for (let i = 0; i < sectionsRef.current.length; i++) {
      const section = sectionsRef.current[i];
      const sectionTop = section.offsetTop;
      const sectionBottom = sectionTop + section.offsetHeight;
      
      if (window.scrollY >= sectionTop - window.innerHeight * 0.3 && 
          window.scrollY < sectionBottom - window.innerHeight * 0.3) {
        currentSectionIndex = i;
        break;
      }
    }
    
    // Scroll to the next section if available
    if (currentSectionIndex >= 0 && currentSectionIndex < sectionsRef.current.length - 1) {
      const nextSection = sectionsRef.current[currentSectionIndex + 1];
      nextSection.scrollIntoView({ behavior: 'smooth' });
    } else {
      // If no specific section is found, scroll down by viewport height
      window.scrollTo({
        top: window.scrollY + window.innerHeight * 0.8,
        behavior: 'smooth'
      });
    }
  };

  const handleEmailSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);
    try {
      const baseUrl = USE_FIREBASE_EMULATOR
        ? 'http://127.0.0.1:5001/oryntrade/us-central1/handle_email_signup'
        : 'https://handle-email-signup-ihjc6tjxia-uc.a.run.app';
      
      console.log('Submitting email to:', baseUrl);
      
      const response = await fetch(baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ email }),
      });

      console.log('Response status:', response.status);
      const responseData = await response.json();
      console.log('Response data:', responseData);

      if (response.ok) {
        if (responseData.status === 'already_subscribed') {
          toast(responseData.message, {
            icon: '📧',
            style: {
              borderRadius: '10px',
              background: '#333',
              color: '#fff',
            },
          });
          setEmail('');
          return;
        }
        
        setEmail('');
        setSubmitStatus('success');
        toast.success('Thank you for subscribing! We\'ll keep you updated.', {
          icon: '🎉',
          style: {
            borderRadius: '10px',
            background: '#333',
            color: '#fff',
          },
        });
      } else {
        throw new Error(`Failed to submit email: ${responseData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error in handleEmailSubmit:', error);
      setSubmitStatus('error');
      toast.error('Failed to subscribe. Please try again later.', {
        icon: '❌',
        style: {
          borderRadius: '10px',
          background: '#333',
          color: '#fff',
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    // Set up real-time listener for vote updates
    const db = getFirestore(app);
    const votesRef = collection(db, 'feature_votes');
    
    console.log('Setting up Firestore listener for feature votes');
    
    const unsubscribe = onSnapshot(votesRef, (snapshot) => {
      console.log('Firestore update received:', snapshot.docs.length, 'documents');
      const counts = {};
      snapshot.forEach(doc => {
        counts[doc.id] = doc.data().votes || 0;
        console.log(`Feature ${doc.id} has ${doc.data().votes} votes`);
      });
      setVoteCounts(counts);
      setLoading(false);
    }, (error) => {
      console.error('Error listening to Firestore updates:', error);
      setLoading(false);
    });

    // Clean up the listener when component unmounts
    return () => {
      console.log('Cleaning up Firestore listener');
      unsubscribe();
    };
  }, []);

  const handleVote = async (feature) => {
    if (isSubmitting) return;
    
    // Check if user has already voted
    const votedFeatures = JSON.parse(localStorage.getItem('votedFeatures') || '[]');
    if (votedFeatures.includes(feature.id)) {
      toast.error('You have already voted for this feature', {
        icon: '🎯',
        style: {
          borderRadius: '10px',
          background: '#333',
          color: '#fff',
        },
      });
      return;
    }
    
    setIsSubmitting(true);
    setSelectedFeature(feature);

    try {
      console.log('Submitting vote for feature:', feature);
      const response = await fetch(
        USE_FIREBASE_EMULATOR
          ? "http://127.0.0.1:5001/oryntrade/us-central1/handle_feature_vote"
          : "https://handle-feature-vote-ihjc6tjxia-uc.a.run.app",
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({ 
            featureId: feature.id,
            featureName: feature.name,
            featureDescription: feature.description
          }),
        }
      );

      console.log('Vote response status:', response.status);
      const responseData = await response.json();
      console.log('Vote response data:', responseData);

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to submit vote');
      }

      // Store the voted feature in localStorage
      votedFeatures.push(feature.id);
      localStorage.setItem('votedFeatures', JSON.stringify(votedFeatures));
      
      // Vote was successful, show success toast
      toast.success('Thank you for your vote!', {
        icon: '🎉',
        style: {
          borderRadius: '10px',
          background: '#333',
          color: '#fff',
        },
      });
      
      setSelectedFeature(null);
    } catch (error) {
      console.error('Error submitting vote:', error);
      setSelectedFeature(null);
      toast.error('Failed to submit vote. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-black to-gray-900 overflow-hidden">
      {/* Hero Section */}
      <section id="hero" className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-[#FFB800]/10 via-transparent to-[#FFB800]/10"></div>
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-10"></div>
        
        <div className="container mx-auto px-4 z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#FFB800] to-[#FFA000]">
                Oryn
              </span>
              <span className="text-white"> - Your AI-Powered Trading Companion</span>
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Create, backtest, and automate your trading strategies without coding. 
              Let AI handle the complexity while you focus on trading.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/login">
                <button className="px-8 py-4 bg-gradient-to-r from-[#FFB800] to-[#FFA000] text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 transform hover:scale-105 shadow-lg shadow-[#FFB800]/20">
        Get Started
                </button>
              </Link>
              <Link href="#features">
                <button className="px-8 py-4 bg-gray-800 text-white rounded-lg font-semibold hover:bg-gray-700 transition-all duration-200 border border-gray-700">
                  Learn More
                </button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 relative">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-16">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#FFB800] to-[#FFA000]">
              Powerful Features
            </span>
            <span className="text-white"> for Every Trader</span>
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Strategy Generation */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-gray-800 rounded-xl p-6 border border-[#FFB800]/20 hover:border-[#FFB800]/40 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-[#FFB800]/20 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#FFB800]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Interactive Strategy Builder</h3>
              <p className="text-gray-400">Create trading strategies using our intuitive interface with a wide selection of technical indicators.</p>
            </motion.div>

            {/* OrynAI */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-gray-800 rounded-xl p-6 border border-[#FFB800]/20 hover:border-[#FFB800]/40 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-[#FFB800]/20 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#FFB800]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">OrynAI Assistant</h3>
              <p className="text-gray-400">Let our AI create profitable trading strategies based on your preferences and market conditions.</p>
            </motion.div>

            {/* Backtesting */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-gray-800 rounded-xl p-6 border border-[#FFB800]/20 hover:border-[#FFB800]/40 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-[#FFB800]/20 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#FFB800]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Advanced Backtesting</h3>
              <p className="text-gray-400">Test your strategies with historical data and optimize them for better performance.</p>
            </motion.div>

            {/* Trading Bots */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-gray-800 rounded-xl p-6 border border-[#FFB800]/20 hover:border-[#FFB800]/40 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-[#FFB800]/20 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#FFB800]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Automated Trading Bots</h3>
              <p className="text-gray-400">Deploy your strategies as automated trading bots that execute trades on your behalf.</p>
            </motion.div>

            {/* Broker Integration */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-gray-800 rounded-xl p-6 border border-[#FFB800]/20 hover:border-[#FFB800]/40 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-[#FFB800]/20 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#FFB800]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Multi-Broker Support</h3>
              <p className="text-gray-400">Connect with multiple brokers including OANDA, with more integrations coming soon.</p>
            </motion.div>

            {/* Community */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-gray-800 rounded-xl p-6 border border-[#FFB800]/20 hover:border-[#FFB800]/40 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-[#FFB800]/20 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#FFB800]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Trading Community</h3>
              <p className="text-gray-400">Share strategies, learn from others, and grow together in our vibrant trading community.</p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Roadmap Section */}
      <section id="roadmap" className="py-20 bg-gray-900/50 relative">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-16">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#FFB800] to-[#FFA000]">
              Development Roadmap
            </span>
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Version 1 */}
            <div className="bg-gray-800 rounded-xl p-6 border border-[#FFB800]/20">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-[#FFB800]/20 rounded-full flex items-center justify-center mr-3">
                  <span className="text-[#FFB800] font-bold">1.0</span>
                </div>
                <h3 className="text-xl font-semibold text-white">Initial Release</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-[#FFB800] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Interactive Strategy Builder
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-[#FFB800] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  OrynAI Assistant
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-[#FFB800] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Automated Trading Bots
                </li>
              </ul>
            </div>

            {/* Version 2 */}
            <div className="bg-gray-800 rounded-xl p-6 border border-[#FFB800]/20">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-[#FFB800]/20 rounded-full flex items-center justify-center mr-3">
                  <span className="text-[#FFB800] font-bold">2.0</span>
                </div>
                <h3 className="text-xl font-semibold text-white">Community Features</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-[#FFB800] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Strategy Marketplace
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-[#FFB800] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Social Trading Features
                </li>
                <li className="flex items-center text-gray-300">
                  <svg className="w-5 h-5 text-[#FFB800] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Performance Analytics
                </li>
              </ul>
            </div>
          </div>

          {/* Feature Poll */}
          <div className="mt-12 bg-gray-800 rounded-xl p-6 border border-[#FFB800]/20">
            <h3 className="text-xl font-semibold text-white mb-4">Which Feature Would You Like to See First?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { id: 'marketplace', name: 'Strategy Marketplace' },
                { id: 'social', name: 'Social Trading' },
                { id: 'analytics', name: 'Advanced Analytics' },
                { id: 'mobile', name: 'Mobile App' }
              ].map(feature => (
                <button
                  key={feature.id}
                  onClick={() => handleVote(feature)}
                  className="flex items-center justify-between p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
                >
                  <span className="text-white">{feature.name}</span>
                  <span className="text-[#FFB800]">{voteCounts[feature.id] || 0} votes</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Email Signup Section */}
      <section id="signup" className="py-20 relative">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-4xl font-bold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#FFB800] to-[#FFA000]">
                Get Early Access
              </span>
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Sign up now to get 50% off for the first year when we launch. 
              Be among the first to experience the future of automated trading.
            </p>
            
            <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="flex-1 px-6 py-4 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-[#FFB800] focus:border-transparent"
                required
                disabled={isSubmitting}
              />
              <button
                type="submit"
                className="px-8 py-4 bg-gradient-to-r from-[#FFB800] to-[#FFA000] text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 transform hover:scale-105 shadow-lg shadow-[#FFB800]/20 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Sending...
                  </span>
                ) : submitStatus === 'success' ? (
                  <span className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Sent!
                  </span>
                ) : submitStatus === 'error' ? (
                  <span className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Failed
                  </span>
                ) : (
                  'Sign Up'
                )}
              </button>
            </form>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 border-t border-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 mb-4 md:mb-0">
              © {new Date().getFullYear()} A&Y Ventures LLC. All rights reserved.
            </div>
            <div className="flex space-x-6">
              <button
                onClick={() => setIsPrivacyPolicyOpen(true)}
                className="text-gray-400 hover:text-[#FFB800] transition-colors"
              >
                Privacy Policy
              </button>
              <button
                onClick={() => setIsTermsOfServiceOpen(true)}
                className="text-gray-400 hover:text-[#FFB800] transition-colors"
              >
                Terms of Service
              </button>
              <button
                onClick={() => setIsContactOpen(true)}
                className="text-gray-400 hover:text-[#FFB800] transition-colors"
              >
                Contact
              </button>
            </div>
          </div>
        </div>
      </footer>

      {/* Universal Scroll Arrow */}
      <ScrollArrow show={showScrollArrow} onClick={scrollToNextSection} />

      {/* Modals */}
      <PrivacyPolicyModal
        isOpen={isPrivacyPolicyOpen}
        onClose={() => setIsPrivacyPolicyOpen(false)}
      />
      <TermsOfServiceModal
        isOpen={isTermsOfServiceOpen}
        onClose={() => setIsTermsOfServiceOpen(false)}
      />
      <ContactModal
        isOpen={isContactOpen}
        onClose={() => setIsContactOpen(false)}
      />
    </div>
  );
}