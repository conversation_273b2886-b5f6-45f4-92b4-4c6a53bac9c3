// Process indicators with simplified logic
console.log("Indicator structure:", chartData.indicators);

// Get RSI data with fallbacks for backward compatibility
let rsiData = [];

// First, try to get RSI data directly by type (new format)
if (chartData.indicators?.RSI) {
  console.log("Found RSI data by type key (new format)");
  rsiData = chartData.indicators.RSI;
}
// Fallback to lowercase 'rsi' (alternative format)
else if (chartData.indicators?.rsi) {
  console.log("Found RSI data by lowercase type key");
  rsiData = chartData.indicators.rsi;
}
// Last resort: look for RSI in the strategy JSON indicators
else if (strategy?.strategy_json?.indicators) {
  console.log("Looking for RSI in strategy JSON indicators");
  const rsiIndicator = strategy.strategy_json.indicators.find(ind => 
    ind.type === 'RSI' || ind.indicator_class === 'RSI'
  );
  
  if (rsiIndicator && rsiIndicator.id && chartData.indicators[rsiIndicator.id]) {
    console.log("Found RSI by ID from strategy JSON:", rsiIndicator.id);
    rsiData = chartData.indicators[rsiIndicator.id];
  }
}

// Ensure RSI data is properly formatted
if (rsiData.length > 0) {
  console.log("RSI data found, sample:", rsiData.slice(0, 3));
  
  // Format the data if it's not already in the right format
  if (typeof rsiData[0] !== 'object' || !('time' in rsiData[0])) {
    console.log("Formatting RSI data with timestamps");
    rsiData = rsiData.map((value, index) => ({
      time: chartData.candles[index]?.time || Math.floor(Date.now() / 1000) - (index * 60),
      value: typeof value === 'number' ? value : 
             typeof value === 'object' && value !== null ? (value.value || 0) : 0
    }));
  }
} else {
  console.log("No RSI data found");
}

const indicators = {
  rsi: rsiData.map((point) => ({
    time: point.time,
    value: parseFloat(point.value || 0),
  })),
