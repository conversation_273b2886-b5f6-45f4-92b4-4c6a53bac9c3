import "../tailwind.css"; // Ensure Tailwind styles are applied globally
// pages/_app.js
import { useEffect } from "react";
import { signOut } from "firebase/auth";
import { auth } from "../../firebaseConfig";
import { USE_FIREBASE_EMULATOR } from "../config"; // adjust path if needed
import { setPersistence, browserLocalPersistence } from "firebase/auth";
import { Toaster } from 'react-hot-toast';

// Add global styles to ensure consistent background color
const globalStyles = `
  html, body {
    background-color: #111827; /* Dark background color */
    color: white;
    min-height: 100vh;
    overflow-x: hidden;
  }
`;

function MyApp({ Component, pageProps }) {
  useEffect(() => {
    if (typeof window !== "undefined") {
      // This code runs only in the browser
      setPersistence(auth, browserLocalPersistence)
        .then(() => {
          console.log("Auth persistence set to local");
        })
        .catch((error) => {
          console.error("Error setting auth persistence:", error);
        });
    }
  }, []);

  return (
    <>
      <style jsx global>{globalStyles}</style>
      <Component {...pageProps} />
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#333',
            color: '#fff',
            borderRadius: '10px',
          },
        }}
      />
    </>
  );
}

export default MyApp;
