import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";

const LOGOUT_TIME = 15 * 60 * 1000; // 15 minutes
const WARNING_TIME = 14 * 60 * 1000; // 14 minutes (1 min before logout)

export function useAuth() {
  const router = useRouter();
  const [isWarningVisible, setIsWarningVisible] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const timeLeftRef = useRef(60);
  const warningTimeoutRef = useRef(null);
  const logoutTimeoutRef = useRef(null);
  const countdownIntervalRef = useRef(null);

  //   Redirect logged-out users to /login, except for the landing page
  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    const isLandingPage = router.pathname === "/";

    if (!userId && !isLandingPage) {
      router.push("/login"); // Redirect to login if not authenticated
    }
  }, [router.pathname]);

  const startCountdown = () => {
    clearInterval(countdownIntervalRef.current);
    timeLeftRef.current = 60;
    setTimeLeft(60);

    countdownIntervalRef.current = setInterval(() => {
      if (timeLeftRef.current <= 0) {
        clearInterval(countdownIntervalRef.current);
        return;
      }

      timeLeftRef.current -= 1;
      setTimeLeft((prev) => prev - 1);
    }, 1000);
  };

  const startInactivityTimers = () => {
    console.log("🔄 Resetting inactivity timers...");

    //   Clear previous timers
    clearTimeout(warningTimeoutRef.current);
    clearTimeout(logoutTimeoutRef.current);
    clearInterval(countdownIntervalRef.current);

    //   Set warning timeout (triggers popup at 14 minutes)
    warningTimeoutRef.current = setTimeout(() => {
      setIsWarningVisible(true);
      startCountdown();
      console.log("⏳ Warning popup triggered!");
    }, WARNING_TIME);

    //   Set logout timeout (logs user out at 15 minutes)
    logoutTimeoutRef.current = setTimeout(() => {
      console.log("🔴 Logging out user due to inactivity.");
      localStorage.removeItem("user_id");
      router.push("/login");
    }, LOGOUT_TIME);
  };

  useEffect(() => {
    startInactivityTimers();

    const handleUserActivity = () => {
      if (!isWarningVisible) {
        console.log("🎯 User activity detected, resetting timers...");
        startInactivityTimers();
      }
    };

    // window.addEventListener("mousemove", handleUserActivity);
    window.addEventListener("keydown", handleUserActivity);

    return () => {
      // window.removeEventListener("mousemove", handleUserActivity);
      window.removeEventListener("keydown", handleUserActivity);
      clearTimeout(warningTimeoutRef.current);
      clearTimeout(logoutTimeoutRef.current);
      clearInterval(countdownIntervalRef.current);
    };
  }, []);

  return {
    isWarningVisible,
    timeLeft,
    stayLoggedIn: () => {
      console.log("  User clicked 'Stay Logged In' button, stopping logout...");

      //   Clear both timers to prevent forced logout
      clearTimeout(warningTimeoutRef.current);
      clearTimeout(logoutTimeoutRef.current);
      clearInterval(countdownIntervalRef.current);

      setIsWarningVisible(false);
      startInactivityTimers(); //   Restart inactivity detection
    },
    logout: () => {
      console.log("🚪 User manually logged out.");
      localStorage.removeItem("user_id");
      router.push("/login");
    },
  };
}
