import React from 'react';
import { motion } from 'framer-motion';

const MarketConditionsPanel = ({ marketStatus, strategy }) => {
  // Extract trading sessions from strategy if available
  const tradingSessions = strategy?.strategy_json?.tradingSession || [];

  // Check if current time is in any of the trading sessions
  const isInTradingSession = () => {
    // If no trading sessions specified, assume all sessions are valid
    if (!tradingSessions || tradingSessions.length === 0) {
      return true;
    }

    const currentHour = new Date().getUTCHours();

    // Check if current time is in any of the specified trading sessions
    return tradingSessions.some(session => {
      switch(session) {
        case 'New York':
          // New York session: 8:00-17:00 EST (13:00-22:00 UTC)
          return currentHour >= 13 && currentHour < 22;
        case 'London':
          // London session: 8:00-17:00 GMT (8:00-17:00 UTC)
          return currentHour >= 8 && currentHour < 17;
        case 'Tokyo':
          // Tokyo session: 9:00-18:00 JST (0:00-9:00 UTC)
          return currentHour >= 0 && currentHour < 9;
        case 'Sydney':
          // Sydney session: 8:00-17:00 AEST (22:00-7:00 UTC)
          return currentHour >= 22 || currentHour < 7;
        case 'All':
          return true;
        default:
          return false;
      }
    });
  };

  const inSession = isInTradingSession();
  if (!marketStatus) {
    return null;
  }

  // Helper function to get status color
  const getStatusColor = (isPositive) => {
    return isPositive ? 'text-green-400' : 'text-red-400';
  };

  // Helper function to get status icon
  const getStatusIcon = (isPositive) => {
    if (isPositive) {
      return (
        <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
    } else {
      return (
        <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      );
    }
  };

  // Helper function to get activity level color
  const getActivityLevelColor = (level) => {
    switch (level) {
      case 'high':
        return 'text-green-400';
      case 'medium':
        return 'text-yellow-400';
      case 'low':
        return 'text-orange-400';
      case 'very low':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // Helper function to get activity level icon
  const getActivityLevelIcon = (level) => {
    switch (level) {
      case 'high':
        return (
          <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        );
      case 'medium':
        return (
          <svg className="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
          </svg>
        );
      case 'low':
      case 'very low':
        return (
          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  return (
    <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] overflow-hidden">
      <div className="px-4 py-3 border-b border-[#1a1a1a] flex items-center justify-between">
        <h2 className="text-lg font-semibold text-[#FEFEFF]">Market Conditions</h2>
        <div className="flex items-center space-x-2">
          <span className={`text-xs px-2 py-1 rounded-full ${marketStatus.is_safe_to_trade ? 'bg-green-900/20 text-green-400' : 'bg-red-900/20 text-red-400'}`}>
            {marketStatus.is_safe_to_trade ? 'Safe to Trade' : 'Not Safe to Trade'}
          </span>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Market Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Market Open Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className={`rounded-lg p-4 border ${marketStatus.is_open ? 'border-green-500/30 bg-green-900/20' : 'border-red-500/30 bg-red-900/20'}`}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-400">Market Status</h3>
              {getStatusIcon(marketStatus.is_open)}
            </div>
            <p className={`text-lg font-bold ${getStatusColor(marketStatus.is_open)}`}>
              {marketStatus.is_open ? 'Open' : 'Closed'}
            </p>
            {!marketStatus.is_open && marketStatus.reason && (
              <p className="text-sm text-gray-400 mt-1">{marketStatus.reason}</p>
            )}
          </motion.div>

          {/* Market Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="rounded-lg p-4 border border-[#333] bg-[#1a1a1a]"
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-400">Market Activity</h3>
              {getActivityLevelIcon(marketStatus.market_activity)}
            </div>
            <p className={`text-lg font-bold ${getActivityLevelColor(marketStatus.market_activity)}`}>
              {marketStatus.market_activity ? marketStatus.market_activity.charAt(0).toUpperCase() + marketStatus.market_activity.slice(1) : 'Unknown'}
            </p>
            {marketStatus.active_centers && marketStatus.active_centers.length > 0 && (
              <p className="text-sm text-gray-400 mt-1">
                Active centers: {marketStatus.active_centers.join(', ')}
              </p>
            )}
          </motion.div>
        </div>

        {/* Trading Session Status */}
        {tradingSessions && tradingSessions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className={`rounded-lg p-4 border ${inSession ? 'border-green-500/30 bg-green-900/20' : 'border-yellow-500/30 bg-yellow-900/20'}`}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-400">Trading Session</h3>
              {getStatusIcon(inSession)}
            </div>
            <p className={`text-lg font-bold ${getStatusColor(inSession)}`}>
              {inSession ? 'Active' : 'Inactive'}
            </p>
            <p className="text-sm text-gray-400 mt-1">
              {tradingSessions.join(', ')} {inSession ? '(Current UTC time is in session)' : '(Current UTC time is outside session)'}
            </p>
          </motion.div>
        )}

        {/* Spread Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className={`rounded-lg p-4 border ${marketStatus.spread_acceptable ? 'border-green-500/30 bg-green-900/20' : 'border-red-500/30 bg-red-900/20'}`}
        >
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Spread</h3>
            {getStatusIcon(marketStatus.spread_acceptable)}
          </div>
          <p className={`text-lg font-bold ${getStatusColor(marketStatus.spread_acceptable)}`}>
            {marketStatus.spread_in_pips ? `${marketStatus.spread_in_pips.toFixed(1)} pips` : 'Unknown'}
          </p>
          <p className="text-sm text-gray-400 mt-1">
            {marketStatus.spread_acceptable ? 'Acceptable for trading' : 'Too wide for trading'}
          </p>
        </motion.div>

        {/* Reason for not trading (if applicable) */}
        {!marketStatus.is_safe_to_trade && marketStatus.reason && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="rounded-lg p-4 border border-red-500/30 bg-red-900/20"
          >
            <div className="flex items-start space-x-2">
              <svg className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-red-400">Trading Paused</h3>
                <p className="text-sm text-gray-300 mt-1">{marketStatus.reason}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Last updated timestamp */}
        {marketStatus.timestamp && (
          <div className="text-xs text-gray-500 text-right">
            Last updated: {new Date(marketStatus.timestamp).toLocaleString()}
          </div>
        )}
      </div>
    </div>
  );
};

export default MarketConditionsPanel;
