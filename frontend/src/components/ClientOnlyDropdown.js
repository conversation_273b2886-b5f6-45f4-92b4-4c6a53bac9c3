import { useState, useRef, useEffect } from 'react';

export default function ClientOnlyDropdown({
    savedStrategies,
    selectedStoredStrategyId,
    onChange,
  }) {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef(null);

    // Close dropdown when clicking outside
    useEffect(() => {
      function handleClickOutside(event) {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
          setIsOpen(false);
        }
      }

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const selectedStrategy = savedStrategies.find(s => s.id.toString() === selectedStoredStrategyId);

    return (
        <div className="relative w-full max-w-2xl mx-auto" ref={dropdownRef}>
            <div className="relative">
                <button
                    onClick={() => setIsOpen(!isOpen)}
                    className="w-full px-6 py-3 bg-gray-800 text-white rounded-lg shadow-lg border border-gray-700 hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 flex items-center justify-between"
                >
                    <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0h8v12H6V4z" clipRule="evenodd" />
                        </svg>
                        {selectedStrategy ? selectedStrategy.name : "Load Saved Strategy"}
                    </span>
                    <svg
                        className={`w-5 h-5 transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </button>

                {isOpen && (
                    <div className="absolute z-50 w-full mt-1 bg-gray-800 rounded-lg shadow-xl border border-gray-700 max-h-60 overflow-y-auto">
                        <div className="py-1">
                            <div
                                className="px-6 py-3 text-gray-400 hover:bg-gray-700 cursor-pointer transition-colors duration-150"
                                onClick={() => {
                                    onChange({ target: { value: '' } });
                                    setIsOpen(false);
                                }}
                            >
                                <div className="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0h8v12H6V4z" clipRule="evenodd" />
                                    </svg>
                                    Create New Strategy
                                </div>
                            </div>
                            {savedStrategies.map((strategy) => (
                                <div
                                    key={strategy.id}
                                    className={`px-6 py-3 cursor-pointer transition-colors duration-150 ${
                                        selectedStoredStrategyId === strategy.id.toString()
                                            ? 'bg-blue-600 text-white'
                                            : 'text-gray-300 hover:bg-gray-700'
                                    }`}
                                    onClick={() => {
                                        onChange({ target: { value: strategy.id.toString() } });
                                        setIsOpen(false);
                                    }}
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0h8v12H6V4z" clipRule="evenodd" />
                                            </svg>
                                            {strategy.name}
                                        </div>
                                        <span className="text-sm text-gray-400">
                                            {new Date(strategy.created_at).toLocaleDateString()}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
  