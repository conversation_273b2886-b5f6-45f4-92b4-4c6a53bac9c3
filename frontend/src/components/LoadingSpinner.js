import React from 'react';

const LoadingSpinner = ({ size = 'medium', text = 'Loading...', fullScreen = false }) => {
  // Size classes
  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-12 h-12',
    large: 'w-16 h-16',
    xlarge: 'w-24 h-24'
  };

  // Text size classes
  const textSizeClasses = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg',
    xlarge: 'text-xl'
  };

  const spinnerContent = (
    <div className="flex flex-col items-center justify-center">
      <div className={`relative ${sizeClasses[size]}`}>
        {/* Outer ring */}
        <div className={`absolute inset-0 rounded-full border-4 border-gray-200/20`} />
        
        {/* Animated ring */}
        <div 
          className={`absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 animate-spin`}
          style={{
            animationDuration: '1s',
            animationTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
            borderImage: 'linear-gradient(45deg, #3B82F6, #60A5FA, #93C5FD) 1'
          }}
        />
        
        {/* Inner gradient circle */}
        <div 
          className="absolute inset-1 rounded-full bg-gradient-to-br from-blue-500/20 to-blue-600/20"
          style={{
            background: 'radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%)'
          }}
        />
      </div>
      
      {/* Loading text */}
      {text && (
        <div className={`mt-4 text-center ${textSizeClasses[size]}`}>
          <span className="text-gray-300 font-medium">{text}</span>
        </div>
      )}
    </div>
  );

  // If fullScreen is true, wrap in a full-screen overlay
  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/95 backdrop-blur-sm">
        <div className="relative">
          {spinnerContent}
          
          {/* Decorative elements */}
          <div className="absolute -inset-4 bg-gradient-to-r from-yellow-500/10 to-amber-500/10 rounded-full blur-xl" />
          <div className="absolute -inset-4 bg-gradient-to-r from-amber-500/10 to-orange-500/10 rounded-full blur-xl" />
        </div>
      </div>
    );
  }

  return spinnerContent;
};

export default LoadingSpinner; 