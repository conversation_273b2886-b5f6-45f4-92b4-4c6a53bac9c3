import React, { useEffect } from 'react';

const formatNumber = (value, options = {}) => {
  if (value === undefined || value === null) return 'N/A';
  return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2, ...options });
};

const BacktestResults = ({ results, loading, error }) => {
  // Add more detailed debug console log
  useEffect(() => {
    if (results && results.performance) {
      console.log("BacktestResults received performance data:", {
        initial_value: results.performance.initial_value,
        final_value: results.performance.final_value,
        pnl: results.performance.pnl,
        roi_percent: results.performance.roi_percent,
        total_trades: results.performance.total_trades,
        win_rate: results.performance.win_rate
      });
    }
  }, [results]);

  if (loading) {
    return (
      <div className="mt-8 p-6 bg-gray-800 rounded-xl shadow-xl">
        <h2 className="text-2xl font-bold text-white mb-4">Running Backtest...</h2>
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error && error !== "Completed") {
    return (
      <div className="mt-8 p-6 bg-gray-800 rounded-xl shadow-xl">
        <h2 className="text-2xl font-bold text-white mb-4">Backtest Error</h2>
        <p className="text-red-400">{error}</p>
      </div>
    );
  }

  if (!results || !results.performance) {
    return null;
  }

  const { 
    initial_value = 0, 
    final_value = 0, 
    pnl = 0, 
    roi_percent = 0, 
    total_trades = 0, 
    win_rate = 0,
    winning_trades = 0,
    losing_trades = 0,
    average_win = 0,
    average_loss = 0,
    profit_factor = 0,
    best_trade = 0,
    worst_trade = 0,
    max_drawdown = 0,
    sharpe_ratio = 0,
    expectancy = 0
  } = results.performance;

  return (
    <div id="backtest-results" className="mt-8 p-6 bg-gray-800 rounded-xl shadow-xl">
      <h2 className="text-2xl font-bold text-white mb-6">Backtest Results</h2>
      
      {/* Performance Summary */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Initial Balance</h3>
          <p className="text-xl font-bold text-white">${formatNumber(initial_value)}</p>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Final Balance</h3>
          <p className="text-xl font-bold text-white">${formatNumber(final_value)}</p>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Total Return</h3>
          <p className={`text-xl font-bold ${roi_percent >= 0 ? 'text-green-500' : 'text-red-500'}`}>
            {formatNumber(roi_percent)}%
          </p>
        </div>
      </div>

      {/* Trade Statistics */}
      <h3 className="text-lg font-semibold text-white mb-4">Trade Statistics</h3>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Total Trades</h3>
          <p className="text-xl font-bold text-white">{total_trades || 0}</p>
        </div>
        
        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Winning Trades</h3>
          <p className="text-xl font-bold text-green-500">{winning_trades || 0}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Losing Trades</h3>
          <p className="text-xl font-bold text-red-500">{losing_trades || 0}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Win Rate</h3>
          <p className="text-xl font-bold text-blue-400">{formatNumber(win_rate)}%</p>
        </div>
      </div>

      {/* Profit Metrics */}
      <h3 className="text-lg font-semibold text-white mb-4">Profit Metrics</h3>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Average Win</h3>
          <p className="text-xl font-bold text-green-500">${formatNumber(average_win)}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Average Loss</h3>
          <p className="text-xl font-bold text-red-500">${formatNumber(average_loss)}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Profit Factor</h3>
          <p className="text-xl font-bold text-white">{formatNumber(profit_factor)}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Best Trade</h3>
          <p className="text-xl font-bold text-green-500">${formatNumber(best_trade)}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Worst Trade</h3>
          <p className="text-xl font-bold text-red-500">${formatNumber(worst_trade)}</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Max Drawdown</h3>
          <p className="text-xl font-bold text-red-500">{formatNumber(max_drawdown)}%</p>
        </div>

        <div className="bg-gray-700 p-4 rounded-lg">
          <h3 className="text-sm text-gray-400 mb-1">Sharpe Ratio</h3>
          <p className="text-xl font-bold text-white">{formatNumber(sharpe_ratio)}</p>
        </div>
      </div>
      
      {/* Equity Curve */}
      {results.equity_curve && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-white mb-3">Equity Curve</h3>
          <div className="bg-gray-700 p-2 rounded-lg">
            <img 
              src={`data:image/png;base64,${results.equity_curve}`} 
              alt="Equity Curve" 
              className="w-full"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default BacktestResults; 