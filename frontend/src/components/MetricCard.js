import { motion } from "framer-motion";

export default function MetricCard({
  title,
  value,
  change,
  icon,
  valueColor = "text-[#FEFEFF]",
}) {
  const formatValue = (value) => {
    if (value === null || value === undefined) {
      return "N/A";
    }

    // Convert value to number if it's not already
    const numericValue = typeof value === "number" ? value : parseFloat(value);

    // Handle NaN after conversion
    if (isNaN(numericValue)) {
      return "N/A";
    }

    if (title.includes("Rate")) {
      return `${(numericValue * 100).toFixed(1)}%`;
    }
    if (
      title.includes("Balance") ||
      title.includes("Profit") ||
      title.includes("P/L")
    ) {
      return `$${numericValue.toFixed(2)}`;
    }
    return numericValue.toFixed(2);
  };

  const getTrendColor = (change) => {
    if (!change && change !== 0) return "text-[#FEFEFF]";
    if (change > 0) return "text-[#EFBD3A]";
    if (change < 0) return "text-red-500";
    return "text-[#FEFEFF]";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-[#FEFEFF]">{title}</h3>
        {icon && <div className="text-[#EFBD3A]">{icon}</div>}
      </div>
      <div className="mt-2 flex items-baseline">
        <p className={`text-2xl font-semibold ${valueColor}`}>
          {formatValue(value)}
        </p>
        {change !== undefined && (
          <p className={`ml-2 text-sm font-medium ${getTrendColor(change)}`}>
            {change > 0 ? "+" : ""}
            {change.toFixed(2)}%
          </p>
        )}
      </div>
    </motion.div>
  );
}
