import { motion } from "framer-motion";

export default function StrategyRules({ rules }) {
  if (!rules) return null;

  const formatIndicator = (indicator) => {
    if (!indicator) return "Unknown";
    return {
      name: indicator.name,
      parameters: indicator.parameters,
    };
  };

  const IndicatorBox = ({ indicator }) => {
    if (!indicator) return null;
    return (
      <div className="flex items-center space-x-2 bg-[#0A0B0B] px-2 py-1.5 rounded-lg border border-[#1a1a1a]">
        <span className="text-[#EFBD3A] text-sm font-medium">
          {indicator.name}
        </span>
        <div className="flex items-center space-x-1">
          {Object.entries(indicator.parameters).map(([key, value]) => (
            <span key={key} className="text-xs text-gray-400">
              {key}: <span className="text-[#FEFEFF]">{value}</span>
            </span>
          ))}
        </div>
      </div>
    );
  };

  const OperatorBox = ({ operator, value }) => {
    const getOperatorIcon = () => {
      switch (operator) {
        case "Crossing above":
          return (
            <div className="flex items-center space-x-1.5">
              <svg
                className="w-4 h-4 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                />
              </svg>
              <span className="text-green-400 text-xs">Crossing above</span>
            </div>
          );
        case "Crossing below":
          return (
            <div className="flex items-center space-x-1.5">
              <svg
                className="w-4 h-4 text-red-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6"
                />
              </svg>
              <span className="text-red-400 text-xs">Crossing below</span>
            </div>
          );
        case ">":
          return (
            <div className="flex items-center space-x-1.5">
              <span className="text-[#EFBD3A] font-bold">&gt;</span>
              <span className="text-[#EFBD3A] text-xs">Greater than</span>
            </div>
          );
        case "<":
          return (
            <div className="flex items-center space-x-1.5">
              <span className="text-[#EFBD3A] font-bold">&lt;</span>
              <span className="text-[#EFBD3A] text-xs">Less than</span>
            </div>
          );
        case "==":
          return (
            <div className="flex items-center space-x-1.5">
              <span className="text-[#EFBD3A] font-bold">=</span>
              <span className="text-[#EFBD3A] text-xs">Equal to</span>
            </div>
          );
        default:
          return <span className="text-[#EFBD3A] text-xs">{operator}</span>;
      }
    };

    return (
      <div className="flex items-center space-x-2">
        {getOperatorIcon()}
        <span className="text-[#FEFEFF] font-medium text-base">{value}</span>
      </div>
    );
  };

  const RuleCard = ({ rule }) => {
    const indicator1 = formatIndicator(rule.indicator1);
    const comparison = rule.comparison;

    return (
      <div className="flex items-center space-x-3">
        <IndicatorBox indicator={indicator1} />
        <OperatorBox operator={comparison.operator} value={comparison.value} />
      </div>
    );
  };

  const RulePairCard = ({ rulePair }) => {
    return (
      <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] p-3">
        <div className="flex items-center justify-between mb-2">
          <span
            className={`text-xs px-2 py-0.5 rounded-full ${
              rulePair.entry.trade_type === "buy"
                ? "bg-green-500/20 text-green-400"
                : "bg-red-500/20 text-red-400"
            }`}
          >
            {rulePair.entry.trade_type.toUpperCase()}
          </span>
        </div>

        <div className="space-y-3">
          <div>
            <p className="text-xs text-gray-400 mb-1">Entry Rule</p>
            <RuleCard rule={rulePair.entry} />
          </div>

          <div className="border-t border-gray-700/50 pt-3">
            <p className="text-xs text-gray-400 mb-1">Exit Rule</p>
            <RuleCard rule={rulePair.exit} />
          </div>
        </div>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a] overflow-x-auto w-full"
      style={{ maxWidth: '100%', overflowX: 'auto' }}
    >
      <h2 className="text-base font-semibold text-[#FEFEFF] mb-3">
        Strategy Rules
      </h2>

      <div className="space-y-3">
        {rules.rule_pairs.map((rulePair) => (
          <RulePairCard key={rulePair.entry.id} rulePair={rulePair} />
        ))}
      </div>

      {/* Risk Management section removed as it's now in a separate panel */}
    </motion.div>
  );
}
