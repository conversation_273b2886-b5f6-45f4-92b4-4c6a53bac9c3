import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "react-hot-toast";
import axios from "axios";
import { USE_FIREBASE_EMULATOR } from "../config";

export default function RegisterForm({ firebaseUser, onApiKeyRegistered }) {
  const [selectedBroker, setSelectedBroker] = useState(null);
  const [hasAccount, setHasAccount] = useState(null);
  const [apiKey, setApiKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);

  const REGISTER_API_KEY_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/register_api_key"
    : "https://register-api-key-your-prod-endpoint.a.run.app";

  const brokers = [
    {
      id: 'oanda',
      name: '<PERSON>AN<PERSON>',
      logo: '/brokers/oanda.png',
      description: 'Global forex and CFD broker with advanced trading tools',
      supported: true
    },
    {
      id: 'forexcom',
      name: 'Forex.com',
      logo: '/brokers/forexcom.png',
      description: 'Leading forex and CFD trading platform',
      supported: false
    },
    {
      id: 'zerodha',
      name: 'Zerodha',
      logo: '/brokers/zerodha.png',
      description: 'India\'s largest stock broker',
      supported: false
    }
  ];

  const handleGoBack = () => {
    if (step === 3) {
      setStep(2);
      setApiKey("");
    } else if (step === 2) {
      setStep(1);
      setHasAccount(null);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await axios.post(
        REGISTER_API_KEY_URL,
        { firebase_uid: firebaseUser.uid, api_key: apiKey },
        { headers: { "Content-Type": "application/json" } }
      );

      console.log("API Key Registration Response:", response.data);
      toast.success("✅ API Key registered successfully! Fetching account...", {
        icon: '🎉',
        style: {
          borderRadius: '10px',
          background: '#333',
          color: '#fff',
        },
      });
      setTimeout(() => onApiKeyRegistered(apiKey), 1000);
    } catch (err) {
      console.error("API Key Registration Error:", err.response?.data?.message);

      if (
        err.response?.status === 401 &&
        err.response?.data?.message &&
        err.response?.data?.message.includes("Invalid API key")
      ) {
        toast.error("❌ The API Key you entered is invalid. Please check your key and try again.", {
          icon: '❌',
          style: {
            borderRadius: '10px',
            background: '#333',
            color: '#fff',
          },
        });
      } else if (
        err.response?.status === 404 &&
        err.response?.data?.message &&
        err.response?.data?.message.includes("No OANDA accounts found")
      ) {
        toast.error("⚠️ This API Key is not associated with any OANDA accounts. Please verify your key.", {
          icon: '⚠️',
          style: {
            borderRadius: '10px',
            background: '#333',
            color: '#fff',
          },
        });
      } else {
        toast.error(
          err.response?.data?.message ||
            "❌ API Key registration failed. Please try again.",
          {
            icon: '❌',
            style: {
              borderRadius: '10px',
              background: '#333',
              color: '#fff',
            },
          }
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const BrokerCard = ({ broker }) => (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`relative p-6 rounded-2xl border-2 transition-all duration-300 ${
        selectedBroker === broker.id
          ? 'border-[#FFB800] bg-[#FFB800]/5'
          : 'border-gray-800 hover:border-gray-700'
      } ${!broker.supported ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
      onClick={() => broker.supported && setSelectedBroker(broker.id)}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-[#FFB800]/5 via-transparent to-[#FFB800]/5 rounded-2xl"></div>
      <div className="relative flex flex-col items-center text-center">
        <div className="w-16 h-16 mb-4">
          <img src={broker.logo} alt={broker.name} className="w-full h-full object-contain" />
        </div>
        <h3 className="text-xl font-semibold mb-2">{broker.name}</h3>
        <p className="text-gray-400 text-sm mb-4">{broker.description}</p>
        {!broker.supported && (
          <span className="text-xs text-gray-500">Coming Soon</span>
        )}
      </div>
    </motion.div>
  );

  const AccountQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center"
    >
      <h3 className="text-2xl font-semibold mb-6">
        Do you already have a {brokers.find(b => b.id === selectedBroker)?.name} account?
      </h3>
      <div className="flex justify-center gap-4">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className={`px-8 py-3 rounded-xl font-medium transition-all duration-300 ${
            hasAccount === true
              ? 'bg-[#FFB800] text-black'
              : 'bg-gray-800 text-white hover:bg-gray-700'
          }`}
          onClick={() => setHasAccount(true)}
        >
          Yes, I have an account
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className={`px-8 py-3 rounded-xl font-medium transition-all duration-300 ${
            hasAccount === false
              ? 'bg-[#FFB800] text-black'
              : 'bg-gray-800 text-white hover:bg-gray-700'
          }`}
          onClick={() => setHasAccount(false)}
        >
          No, I need to create one
        </motion.button>
      </div>
    </motion.div>
  );

  const AccountCreationInstructions = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
        <h3 className="text-xl font-semibold mb-4 text-white">Create Your OANDA Account</h3>
        <ol className="space-y-4 text-gray-300">
          <li className="flex items-start">
            <span className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-[#FFB800] text-black font-bold mr-3">1</span>
            <div>
              <p className="font-medium">Sign up for an OANDA account</p>
              <p className="text-sm text-gray-400">Click the button below to go to OANDA's website and create your account</p>
            </div>
          </li>
          <li className="flex items-start">
            <span className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-[#FFB800] text-black font-bold mr-3">2</span>
            <div>
              <p className="font-medium">Create a Trading Account</p>
              <p className="text-sm text-gray-400">Choose between a demo account (practice) or live account (real trading)</p>
            </div>
          </li>
          <li className="flex items-start">
            <span className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-[#FFB800] text-black font-bold mr-3">3</span>
            <div>
              <p className="font-medium">Generate an API Key</p>
              <p className="text-sm text-gray-400">After logging in, go to Settings → API Access → Create New API Key</p>
            </div>
          </li>
          <li className="flex items-start">
            <span className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-[#FFB800] text-black font-bold mr-3">4</span>
            <div>
              <p className="font-medium">Return Here</p>
              <p className="text-sm text-gray-400">Once you have your API key, come back to this page to connect your account</p>
            </div>
          </li>
        </ol>
      </div>

      <div className="flex justify-center space-x-4">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => window.open('https://www.oanda.com/', '_blank')}
          className="px-6 py-3 bg-[#FFB800] text-black rounded-xl font-medium"
        >
          Go to OANDA
        </motion.button>
      </div>
    </motion.div>
  );

  const ApiKeyForm = () => {
    const [showApiKey, setShowApiKey] = useState(false);

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-md mx-auto"
      >
        <h3 className="text-2xl font-semibold mb-6 text-center">
          Enter your {brokers.find(b => b.id === selectedBroker)?.name} API Key
        </h3>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="relative">
            <input
              type={showApiKey ? "text" : "password"}
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter your API Key"
              required
              className="w-full p-4 text-lg border border-gray-700 rounded-xl bg-gray-900 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#FFB800] focus:border-[#FFB800]/50 transition-all duration-300 pr-12"
            />
            <button
              type="button"
              onClick={() => setShowApiKey(!showApiKey)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200 focus:outline-none"
              aria-label={showApiKey ? "Hide API Key" : "Show API Key"}
            >
              {showApiKey ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          </div>
          <p className="text-sm text-gray-400 mt-2">
            Your API key is sensitive information. Keep it secure and never share it with others.
          </p>
          <button
            type="submit"
            disabled={loading}
            className={`w-full p-4 rounded-xl text-lg font-medium transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#FFB800] focus:ring-offset-2 focus:ring-offset-gray-900 ${
              loading
                ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                : "bg-gradient-to-r from-[#FFB800] to-[#FFA000] text-black hover:from-[#FFA000] hover:to-[#FFB800]"
            }`}
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Connecting...
              </span>
            ) : 'Connect Account'}
          </button>
        </form>
      </motion.div>
    );
  };

  return (
    <motion.div
      className="bg-gradient-to-br from-gray-900 via-black to-gray-900 rounded-2xl p-8 shadow-lg border border-[#FFB800]/20 relative group hover:border-[#FFB800]/40 transition-all duration-300"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-[#FFB800]/5 via-transparent to-[#FFB800]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>

      <div className="relative">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold text-white">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#FFB800] to-[#FFA000]">
              Connect Your Trading Account
            </span>
          </h2>
          {step > 1 && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleGoBack}
              className="px-4 py-2 text-sm font-medium text-gray-300 hover:text-white bg-gray-800 rounded-lg transition-colors duration-200"
            >
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Go Back
              </div>
            </motion.button>
          )}
        </div>

        <p className="text-gray-300 text-lg mb-8 text-center">
          To use Oryn's trading features, connect your trading account. We'll help you set it up securely.
        </p>

        <AnimatePresence mode="wait">
          {step === 1 && (
            <motion.div
              key="broker-selection"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6"
            >
              {brokers.map((broker) => (
                <BrokerCard key={broker.id} broker={broker} />
              ))}
            </motion.div>
          )}

          {step === 2 && selectedBroker && (
            <motion.div
              key="account-question"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
            >
              {hasAccount === null ? (
                <AccountQuestion />
              ) : hasAccount === false ? (
                <AccountCreationInstructions />
              ) : (
                <ApiKeyForm />
              )}
            </motion.div>
          )}
        </AnimatePresence>

        <div className="flex justify-center mt-8">
          {selectedBroker && step === 1 && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-6 py-3 bg-[#FFB800] text-black rounded-xl font-medium"
              onClick={() => setStep(2)}
            >
              Continue
            </motion.button>
          )}
        </div>
      </div>
    </motion.div>
  );
}