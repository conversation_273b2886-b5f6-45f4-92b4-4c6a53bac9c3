import { motion } from 'framer-motion';

const ScrollArrow = ({ show, onClick }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ 
        opacity: show ? 1 : 0,
        y: show ? 0 : -20
      }}
      transition={{ duration: 0.5 }}
      className="fixed bottom-8 left-[calc(50%-16px)] -translate-x-1/2 cursor-pointer z-50"
      onClick={onClick}
    >
      <motion.div
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 1.5, repeat: Infinity }}
        className="text-white/50 hover:text-white/80 transition-colors duration-200"
      >
        <svg
          className="w-8 h-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 14l-7 7m0 0l-7-7m7 7V3"
          />
        </svg>
      </motion.div>
    </motion.div>
  );
};

export default ScrollArrow; 