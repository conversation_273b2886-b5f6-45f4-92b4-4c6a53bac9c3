import Link from "next/link";
import { signOut, onAuthStateChanged } from "firebase/auth";
import { auth } from "../../firebaseConfig";
import { useState, useEffect } from "react";
import React from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import { HomeIcon, BeakerIcon, ChartBarIcon, CpuChipIcon, Cog6ToothIcon } from "@heroicons/react/24/outline";
// import { classNames } from "../../utils/classNames";

export default function DashboardLayout({ children }) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [user, setUser] = useState(auth.currentUser);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();

  // Check authentication status with improved handling
  useEffect(() => {
    // Check if we have a user in localStorage first
    const storedUserId = localStorage.getItem("user_id");
    let isNavigating = false;

    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      // If we're in the middle of navigation, don't do anything
      if (isNavigating) return;

      if (currentUser) {
        console.log("User authenticated in DashboardLayout:", currentUser.uid);
        setUser(currentUser);
        setIsAuthenticated(true);
        // Ensure user_id is set in localStorage
        localStorage.setItem("user_id", currentUser.uid);
      } else if (!storedUserId) {
        // Only redirect if we don't have a stored user ID
        // This prevents logout during navigation
        console.log("No stored user ID, redirecting to login");
        isNavigating = true;
        router.push("/login");
      } else {
        console.log("No current user but found stored ID:", storedUserId);
        // We have a stored user ID but no current user
        // This might be a temporary state during navigation
        // Let's wait a bit longer before deciding to log out

        const timer = setTimeout(() => {
          // Check again after delay
          if (!auth.currentUser && !isNavigating) {
            console.log("Still no current user after delay, redirecting to login");
            isNavigating = true;
            router.push("/login");
          }
        }, 1000); // Longer delay

        return () => clearTimeout(timer);
      }
    });

    // Handle route change start
    const handleRouteChangeStart = () => {
      console.log("Route change starting");
      isNavigating = true;
    };

    // Handle route change complete
    const handleRouteChangeComplete = () => {
      console.log("Route change complete");
      isNavigating = false;
    };

    // Add router event listeners
    router.events.on('routeChangeStart', handleRouteChangeStart);
    router.events.on('routeChangeComplete', handleRouteChangeComplete);

    return () => {
      unsubscribe();
      router.events.off('routeChangeStart', handleRouteChangeStart);
      router.events.off('routeChangeComplete', handleRouteChangeComplete);
    };
  }, [router]);

  // Handle mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    // Initial check
    checkMobile();

    // Add event listener
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleLogout = async () => {
    try {
      await signOut(auth);
      window.location.href = "/login";
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const handleNavigation = (href) => {
    // Check if we're already on this page
    if (router.pathname === href) {
      console.log("Already on this page, not navigating");
      if (isMobile) {
        setIsSidebarOpen(false);
      }
      return;
    }

    // Store the current user ID in localStorage before navigation
    const currentUser = auth.currentUser;
    if (currentUser) {
      console.log("Storing user ID before navigation:", currentUser.uid);
      localStorage.setItem("user_id", currentUser.uid);
      localStorage.setItem("last_navigation", new Date().toISOString());
    }

    // Close sidebar on mobile
    if (isMobile) {
      setIsSidebarOpen(false);
    }

    // Navigate to the new page
    console.log("Navigating to:", href);
    router.push(href);
  };

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: HomeIcon,
      current: router.pathname === '/dashboard',
    },
    {
      name: 'Strategy Generation',
      href: '/strategy-generation',
      icon: BeakerIcon,
      current: router.pathname === '/strategy-generation',
    },
    {
      name: 'Strategy Library',
      href: '/StrategyLibrary',
      icon: ChartBarIcon,
      current: router.pathname === '/StrategyLibrary',
    },
    {
      name: 'Trading Bot',
      href: '/trade-bots',
      icon: CpuChipIcon,
      current: router.pathname === '/trade-bots',
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Cog6ToothIcon,
      current: router.pathname === '/settings',
    },
  ];

  return (
    <div className="min-h-screen bg-[#0A0B0B]">
      <div className="flex min-h-screen">
        {/* Sidebar */}
        <div
          className={`lg:sticky lg:top-0 lg:h-screen bg-[#0A0B0B] border-r border-[#1a1a1a] transform transition-all duration-200 ease-in-out ${
            isSidebarOpen ? "w-64" : "w-16"
          }`}
        >
          {/* Sidebar content */}
          <div className="flex flex-col h-screen">
            <div className="flex items-center justify-between h-16 px-4 border-b border-[#1a1a1a]">
              {isSidebarOpen ? (
                <>
                  <Link href="/" className="flex items-center space-x-2">
                    <Image
                      src="/logo.png"
                      alt="Oryn Logo"
                      width={40}
                      height={40}
                      quality={100}
                      priority
                    />
                    <span className="text-xl font-bold text-[#FEFEFF]">OrynTrade</span>
                  </Link>
                  <button
                    onClick={() => setIsSidebarOpen(false)}
                    className="text-[#FEFEFF] hover:text-[#EFBD3A] transition-colors duration-200"
                    aria-label="Close sidebar"
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setIsSidebarOpen(true)}
                  className="text-[#FEFEFF] hover:text-[#EFBD3A] flex items-center justify-center w-full"
                >
                  <Image
                    src="/logo.png"
                    alt="Oryn Logo"
                    width={32}
                    height={32}
                    quality={100}
                    priority
                    className="transition-transform duration-200 hover:scale-105"
                  />
                </button>
              )}
            </div>
            <nav className="flex-1 p-4 space-y-2">
              {navigation.map((item) => (
                <button
                  key={item.name}
                  onClick={() => handleNavigation(item.href)}
                  className={`w-full flex items-center ${isSidebarOpen ? 'justify-start' : 'justify-center'} space-x-3 px-4 py-2 rounded-lg transition-colors ${
                    router.pathname === item.href
                      ? "bg-[#EFBD3A] text-[#0A0B0B]"
                      : "text-[#FEFEFF] hover:bg-[#1a1a1a]"
                  }`}
                >
                  <div className="flex items-center">
                    <item.icon
                      className={`flex-shrink-0 h-6 w-6 ${
                        item.current ? "text-[#0A0B0B]" : "text-[#FEFEFF] group-hover:text-[#FEFEFF]"
                      }`}
                      aria-hidden="true"
                    />
                    {isSidebarOpen && <span className="ml-3">{item.name}</span>}
                  </div>
                </button>
              ))}
            </nav>
            <div className="p-4 border-t border-[#1a1a1a]">
              <button
                onClick={handleLogout}
                className={`w-full flex items-center ${isSidebarOpen ? 'justify-start' : 'justify-center'} space-x-3 px-4 py-2 text-[#FEFEFF] hover:bg-[#1a1a1a] rounded-lg transition-colors`}
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  />
                </svg>
                {isSidebarOpen && <span>Sign Out</span>}
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 min-h-screen">
          {/* Mobile Header */}
          <div className="lg:hidden bg-[#0A0B0B] border-b border-[#1a1a1a]">
            <div className="flex items-center justify-between h-16 px-4">
              <Link href="/" className="flex items-center space-x-2">
                {/* <Image
                  src="/logo.png"
                  alt="Oryn Logo"
                  width={40}
                  height={40}
                  quality={100}
                  priority
                /> */}
                {/* <span className="text-xl font-bold text-[#FEFEFF]">OrynTrade</span> */}
              </Link>
              <button
                onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                className="relative flex items-center space-x-2 px-3 py-1.5 rounded-lg hover:bg-[#1a1a1a] transition-all duration-200 group"
              >
                <div className="relative w-8 h-8 rounded-full bg-gradient-to-br from-[#EFBD3A]/20 to-[#EFBD3A]/10 flex items-center justify-center">
                  <svg
                    className="w-5 h-5 text-[#FEFEFF] group-hover:text-[#EFBD3A] transition-colors duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                </div>
                <span className="text-[#FEFEFF] text-sm font-medium group-hover:text-[#EFBD3A] transition-colors duration-200">
                  {user?.email?.split('@')[0]}
                </span>
              </button>
            </div>
          </div>

          {/* Desktop Header */}
          <div className="hidden lg:flex items-center justify-end h-16 px-8 border-b border-[#1a1a1a]">
            <button
              onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              className="relative flex items-center space-x-2 px-3 py-1.5 rounded-lg hover:bg-[#1a1a1a] transition-all duration-200 group"
            >
              <div className="relative w-8 h-8 rounded-full bg-gradient-to-br from-[#EFBD3A]/20 to-[#EFBD3A]/10 flex items-center justify-center">
                <svg
                  className="w-5 h-5 text-[#FEFEFF] group-hover:text-[#EFBD3A] transition-colors duration-200"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
              </div>
              <span className="text-[#FEFEFF] text-sm font-medium group-hover:text-[#EFBD3A] transition-colors duration-200">
                {user?.email?.split('@')[0]}
              </span>
            </button>
          </div>

          {/* Profile Menu Dropdown */}
          {isProfileMenuOpen && (
            <div className="absolute right-4 top-16 lg:right-8 lg:top-16 z-50">
              <div className="bg-[#0A0B0B] border border-[#1a1a1a] rounded-xl shadow-lg py-2 w-64 overflow-hidden backdrop-blur-sm bg-opacity-95">
                <div className="px-4 py-4 border-b border-[#1a1a1a]">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#EFBD3A]/20 to-[#EFBD3A]/10 flex items-center justify-center">
                      <svg
                        className="w-6 h-6 text-[#FEFEFF]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="text-[#FEFEFF] text-sm font-medium">{user?.email?.split('@')[0]}</p>
                      <p className="text-[#FEFEFF]/60 text-xs">{user?.email}</p>
                    </div>
                  </div>
                </div>
                <div className="py-1">
                  <button
                    onClick={handleLogout}
                    className="w-full flex items-center px-4 py-2.5 text-[#FEFEFF] hover:bg-[#1a1a1a] transition-colors duration-200 group"
                  >
                    <svg
                      className="w-5 h-5 mr-3 text-[#FEFEFF]/60 group-hover:text-[#EFBD3A] transition-colors duration-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                      />
                    </svg>
                    <span className="group-hover:text-[#EFBD3A] transition-colors duration-200">Sign Out</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Page Content */}
          <main className="p-4 lg:p-8">
            {React.isValidElement(children) ? React.cloneElement(children, { isSidebarOpen }) : children}
          </main>
        </div>
      </div>
    </div>
  );
}
