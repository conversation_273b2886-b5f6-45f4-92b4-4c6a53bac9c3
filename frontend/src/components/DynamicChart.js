import { useEffect, useState } from "react";
import { Line } from "react-chartjs-2";
import "chart.js/auto";

export default function DynamicChart() {
  const [chartData, setChartData] = useState({
    labels: [],
    datasets: [
      {
        label: "Price",
        data: [],
        borderColor: "blue",
        backgroundColor: "rgba(0,0,255,0.2)",
      },
    ],
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setChartData((prevData) => {
        const newPrice = Math.random() * 10 + 100;
        return {
          labels: [...prevData.labels, new Date().toLocaleTimeString()],
          datasets: [
            {
              ...prevData.datasets[0],
              data: [...prevData.datasets[0].data, newPrice],
            },
          ],
        };
      });
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  if (!chartData.labels || chartData.labels.length === 0) {
    return <p>Loading trade data...</p>;
  }

  return <Line data={chartData} />;
}
