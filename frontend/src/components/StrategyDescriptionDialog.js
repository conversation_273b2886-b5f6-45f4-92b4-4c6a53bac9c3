import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import { USE_FIREBASE_EMULATOR } from '../config';

const AI_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/ai_strategy"
  : "https://ai-strategy-ihjc6tjxia-uc.a.run.app";

export default function StrategyDescriptionDialog({ isOpen, onClose, onStrategyGenerated }) {
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!description.trim()) {
      setError('Please enter a strategy description');
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log("Sending strategy description to AI:", description);
      const response = await axios.post(
        AI_STRATEGY_URL,
        { message: description },
        { headers: { "Content-Type": "application/json" } }
      );

      console.log("Received AI response:", response.data);

      if (response.data) {
        // The response is already in the correct format, no need for transformation
        const strategy = response.data;

        // Ensure all required fields are present
        if (!strategy.name || !strategy.indicators || !strategy.entryRules || !strategy.exitRules) {
          console.error("Missing required fields in strategy:", strategy);
          throw new Error('Invalid strategy format: missing required fields');
        }

        // Ensure indicators have the required fields
        strategy.indicators = strategy.indicators.map(ind => ({
          id: ind.id,
          type: ind.indicator_class || ind.type,
          indicator_class: ind.indicator_class || ind.type,
          parameters: ind.parameters || {},
          source: ind.source || "close"
        }));

        // Ensure entry rules have the required fields
        strategy.entryRules = strategy.entryRules.map(rule => {
          // Check if this is a MACD rule with incorrect format
          const isMacdRule = strategy.indicators.some(ind =>
            (ind.type === "MACD" || ind.indicator_class === "MACD") &&
            rule.indicator1 === ind.id
          );

          // Fix MACD rules that use barRef instead of macdComponent
          if (isMacdRule && (rule.barRef === "macd_line" || rule.barRef === "signal_line")) {
            console.log("Fixing incorrectly formatted MACD rule:", rule);

            // Convert barRef to proper macdComponent format
            const macdComponent = rule.barRef === "macd_line" ? "macd" : "signal";

            // If comparing to a value, we only need macdComponent
            if (rule.compareType === "value") {
              return {
                ...rule,
                id: rule.id,
                tradeType: rule.tradeType,
                indicator1: rule.indicator1,
                operator: rule.operator,
                compareType: rule.compareType,
                indicator2: rule.indicator2 || "",
                value: rule.value || "",
                logicalOperator: rule.logicalOperator || "AND",
                barRef: "close", // Set to close instead of macd_line/signal_line
                macdComponent: macdComponent
              };
            }
            // If comparing to another indicator (likely the same MACD for line crossovers)
            else if (rule.compareType === "indicator" && rule.indicator2 === rule.indicator1) {
              return {
                ...rule,
                id: rule.id,
                tradeType: rule.tradeType,
                indicator1: rule.indicator1,
                operator: rule.operator,
                compareType: rule.compareType,
                indicator2: rule.indicator2,
                value: rule.value || "",
                logicalOperator: rule.logicalOperator || "AND",
                barRef: "close", // Set to close instead of macd_line/signal_line
                macdComponent: "macd", // Default to comparing MACD line...
                macdComponent2: "signal" // ...to signal line
              };
            }
          }

          // Regular rule or already correctly formatted MACD rule
          return {
            ...rule,
            id: rule.id,
            tradeType: rule.tradeType,
            indicator1: rule.indicator1,
            operator: rule.operator,
            compareType: rule.compareType,
            indicator2: rule.indicator2 || "",
            value: rule.value || "",
            logicalOperator: rule.logicalOperator || "AND",
            barRef: rule.barRef || "close",
            // Preserve existing MACD components if present
            ...(rule.macdComponent && { macdComponent: rule.macdComponent }),
            ...(rule.macdComponent2 && { macdComponent2: rule.macdComponent2 })
          };
        });

        // Ensure exit rules have the required fields
        strategy.exitRules = strategy.exitRules.map(rule => {
          // Check if this is a MACD rule with incorrect format
          const isMacdRule = strategy.indicators.some(ind =>
            (ind.type === "MACD" || ind.indicator_class === "MACD") &&
            rule.indicator1 === ind.id
          );

          // Fix MACD rules that use barRef instead of macdComponent
          if (isMacdRule && (rule.barRef === "macd_line" || rule.barRef === "signal_line")) {
            console.log("Fixing incorrectly formatted MACD rule:", rule);

            // Convert barRef to proper macdComponent format
            const macdComponent = rule.barRef === "macd_line" ? "macd" : "signal";

            // If comparing to a value, we only need macdComponent
            if (rule.compareType === "value") {
              return {
                ...rule,
                id: rule.id,
                tradeType: rule.tradeType,
                indicator1: rule.indicator1,
                operator: rule.operator,
                compareType: rule.compareType,
                indicator2: rule.indicator2 || "",
                value: rule.value || "",
                logicalOperator: rule.logicalOperator || "AND",
                barRef: "close", // Set to close instead of macd_line/signal_line
                macdComponent: macdComponent
              };
            }
            // If comparing to another indicator (likely the same MACD for line crossovers)
            else if (rule.compareType === "indicator" && rule.indicator2 === rule.indicator1) {
              return {
                ...rule,
                id: rule.id,
                tradeType: rule.tradeType,
                indicator1: rule.indicator1,
                operator: rule.operator,
                compareType: rule.compareType,
                indicator2: rule.indicator2,
                value: rule.value || "",
                logicalOperator: rule.logicalOperator || "AND",
                barRef: "close", // Set to close instead of macd_line/signal_line
                macdComponent: "macd", // Default to comparing MACD line...
                macdComponent2: "signal" // ...to signal line
              };
            }
          }

          // Regular rule or already correctly formatted MACD rule
          return {
            ...rule,
            id: rule.id,
            tradeType: rule.tradeType,
            indicator1: rule.indicator1,
            operator: rule.operator,
            compareType: rule.compareType,
            indicator2: rule.indicator2 || "",
            value: rule.value || "",
            logicalOperator: rule.logicalOperator || "AND",
            barRef: rule.barRef || "close",
            // Preserve existing MACD components if present
            ...(rule.macdComponent && { macdComponent: rule.macdComponent }),
            ...(rule.macdComponent2 && { macdComponent2: rule.macdComponent2 })
          };
        });

        // Ensure risk management has the required fields
        if (!strategy.riskManagement) {
          strategy.riskManagement = {
            riskPercentage: "1",
            riskRewardRatio: "2",
            stopLossMethod: "fixed",
            fixedPips: "50"
          };
        }

        // If using indicator-based stop loss, ensure the indicator is in the indicators array
        if (strategy.riskManagement.stopLossMethod === "indicator" &&
            strategy.riskManagement.indicatorBasedSL &&
            strategy.riskManagement.indicatorBasedSL.indicator) {

          const slIndicator = strategy.riskManagement.indicatorBasedSL.indicator;
          const slParams = strategy.riskManagement.indicatorBasedSL.parameters || {};

          // Check if the indicator is already in the indicators array
          const indicatorExists = strategy.indicators.some(ind =>
            (ind.type === slIndicator || ind.indicator_class === slIndicator) &&
            JSON.stringify(ind.parameters) === JSON.stringify(slParams)
          );

          // If not, add it
          if (!indicatorExists) {
            const timestamp = Date.now();
            const randomChars = Math.random().toString(36).substring(2, 10);
            const newId = `${timestamp}${randomChars}`;

            let newIndicator = {
              id: newId,
              type: slIndicator,
              indicator_class: slIndicator,
              parameters: slParams,
              source: "close"
            };

            // Normalize indicator name based on what's in the risk management
            if (slIndicator === "atr") {
              newIndicator.type = "ATR";
              newIndicator.indicator_class = "ATR";
            } else if (slIndicator === "bollinger") {
              newIndicator.type = "BollingerBands";
              newIndicator.indicator_class = "BollingerBands";
            } else if (slIndicator === "support_resistance") {
              newIndicator.type = "SupportResistance";
              newIndicator.indicator_class = "SupportResistance";
            }

            strategy.indicators.push(newIndicator);
            console.log(`Added missing indicator for stop loss: ${slIndicator}`, newIndicator);
          }
        }

        // Ensure tradingSession is preserved and not overridden
        if (!strategy.tradingSession || !Array.isArray(strategy.tradingSession) || strategy.tradingSession.length === 0) {
          strategy.tradingSession = ["All"];
        }

        console.log("Processed strategy:", strategy);
        onStrategyGenerated(strategy);
        onClose();
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error generating strategy:', err);

      // User-friendly error messages
      let userMessage = 'We encountered an issue while generating your strategy.';

      if (err.response?.status === 503 || err.response?.status === 500) {
        userMessage = 'Our AI service is temporarily unavailable. Please try again in a few moments.';
      } else if (err.response?.status === 429) {
        userMessage = 'You\'ve made too many requests. Please wait a moment before trying again.';
      } else if (err.response?.status === 400) {
        userMessage = 'Please check your strategy description and try again.';
      } else if (err.message === 'Invalid response format') {
        userMessage = 'There was an issue processing the AI response. Our team has been notified.';
      }

      setError(
        <div className="space-y-2">
          <p>{userMessage}</p>
          <div className="flex gap-2 mt-2">
            <button
              onClick={() => handleSubmit(e)}
              className="text-sm px-3 py-1 bg-blue-500 hover:bg-blue-600 rounded-md transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={onClose}
              className="text-sm px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded-md transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50"
          />

          {/* Dialog Container */}
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-y-auto">
            {/* Dialog */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2 }}
              className="w-full max-w-2xl mx-auto my-8"
            >
              <div className="bg-gray-800 rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
                {/* Header */}
                <div className="px-6 py-4 border-b border-gray-700">
                  <div className="flex items-center justify-between">
                    <h2 className="text-2xl font-bold text-white flex items-center gap-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      Generate a strategy with OrynAI
                    </h2>
                    <button
                      onClick={onClose}
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <p className="text-gray-300 mb-6">
                    Describe your strategy, Adding details will help Oryn generate a better strategy:
                  </p>
                  <ul className="list-disc list-inside text-gray-300 mb-6 space-y-2">
                    <li>Preferred trading pairs</li>
                    <li>Timeframe preferences</li>
                    <li>Risk management approach</li>
                    <li>Technical indicators you prefer</li>
                    <li>Entry and exit conditions</li>
                  </ul>

                  {/* Disclaimer Message */}
                  <div className="bg-yellow-900/20 border border-yellow-500/20 rounded-lg p-4 mb-6">
                    <div className="flex items-start gap-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <div>
                        <h4 className="text-yellow-500 font-medium mb-1">Important Disclaimer</h4>
                        <p className="text-yellow-300 text-sm">
                          OrynAI generates trading strategies based on your requirements, but we cannot guarantee their profitability. All trading involves risk, and past performance does not guarantee future results. We recommend thoroughly testing any generated strategy in a demo account before using it with real money. Oryn takes no responsibility for any trading losses incurred from using our AI-generated strategies.
                        </p>
                      </div>
                    </div>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
                        Strategy Description
                      </label>
                      <textarea
                        id="description"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        rows={6}
                        className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        placeholder="Example: I want a strategy that trades EUR/USD on the 1-hour timeframe. I prefer using RSI and MACD indicators. I want to enter when RSI is oversold and MACD shows a bullish crossover. I'd like to use a 2% stop loss and 4% take profit..."
                      />
                    </div>

                    {error && (
                      <div className="mx-6 mb-4">
                        <div className="text-red-400 bg-red-900/20 border border-red-500/20 rounded-lg p-4">
                          {error}
                        </div>
                      </div>
                    )}

                    <div className="flex justify-end gap-4 mt-6">
                      <button
                        type="button"
                        onClick={onClose}
                        className="px-6 py-2.5 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={loading}
                        className={`px-6 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center gap-2 ${
                          loading ? "opacity-75 cursor-not-allowed" : ""
                        }`}
                      >
                        {loading ? (
                          <>
                            <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating...
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                            </svg>
                            Generate Strategy
                          </>
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </motion.div>
          </div>
        </>
      )}
    </AnimatePresence>
  );
}