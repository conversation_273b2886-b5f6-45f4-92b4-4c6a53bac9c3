import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const StatusNotification = ({ status, previousStatus, onClose }) => {
  const [visible, setVisible] = useState(true);
  const [autoCloseTimer, setAutoCloseTimer] = useState(null);

  // Define status messages and styles
  const statusConfig = {
    initializing: {
      title: 'Bot Initializing',
      message: 'The trading bot is starting up and preparing to trade.',
      icon: (
        <svg className="w-6 h-6 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      ),
      bgColor: 'bg-amber-900/20',
      borderColor: 'border-amber-500/30',
      textColor: 'text-amber-400',
      autoClose: false
    },
    initialized: {
      title: 'Bot Initialized',
      message: 'The trading bot has been initialized and is ready to start trading.',
      icon: (
        <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      ),
      bgColor: 'bg-blue-900/20',
      borderColor: 'border-blue-500/30',
      textColor: 'text-blue-400',
      autoClose: true
    },
    running: {
      title: 'Bot Running',
      message: 'The trading bot is actively monitoring the market and executing trades based on your strategy.',
      icon: (
        <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-green-900/20',
      borderColor: 'border-green-500/30',
      textColor: 'text-green-400',
      autoClose: true
    },
    pausing: {
      title: 'Bot Pausing',
      message: 'The trading bot is in the process of pausing. Open positions will be closed.',
      icon: (
        <svg className="w-6 h-6 text-yellow-500 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-yellow-900/20',
      borderColor: 'border-yellow-500/30',
      textColor: 'text-yellow-400',
      autoClose: false
    },
    paused: {
      title: 'Bot Paused',
      message: 'The trading bot has been paused. No new trades will be executed until resumed.',
      icon: (
        <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-yellow-900/20',
      borderColor: 'border-yellow-500/30',
      textColor: 'text-yellow-400',
      autoClose: true
    },
    resuming: {
      title: 'Bot Resuming',
      message: 'The trading bot is in the process of resuming operations.',
      icon: (
        <svg className="w-6 h-6 text-green-500 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-green-900/20',
      borderColor: 'border-green-500/30',
      textColor: 'text-green-400',
      autoClose: false
    },
    stopping: {
      title: 'Bot Stopping',
      message: 'The trading bot is in the process of stopping. All open positions will be closed.',
      icon: (
        <svg className="w-6 h-6 text-red-500 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
        </svg>
      ),
      bgColor: 'bg-red-900/20',
      borderColor: 'border-red-500/30',
      textColor: 'text-red-400',
      autoClose: false
    },
    stopped: {
      title: 'Bot Stopped',
      message: 'The trading bot has been stopped. All positions have been closed.',
      icon: (
        <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
        </svg>
      ),
      bgColor: 'bg-gray-900/20',
      borderColor: 'border-gray-500/30',
      textColor: 'text-gray-400',
      autoClose: true
    },
    error: {
      title: 'Bot Error',
      message: 'The trading bot has encountered an error. Please check the logs for details.',
      icon: (
        <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-red-900/20',
      borderColor: 'border-red-500/30',
      textColor: 'text-red-400',
      autoClose: false
    },
    market_closed: {
      title: 'Market Closed',
      message: 'The market is currently closed. The bot will resume trading when the market opens.',
      icon: (
        <svg className="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-purple-900/20',
      borderColor: 'border-purple-500/30',
      textColor: 'text-purple-400',
      autoClose: true
    }
  };

  // Get config for current status
  const config = statusConfig[status] || {
    title: 'Status Update',
    message: `Bot status changed to ${status}`,
    icon: (
      <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    bgColor: 'bg-blue-900/20',
    borderColor: 'border-blue-500/30',
    textColor: 'text-blue-400',
    autoClose: true
  };

  // Generate transition message based on status change
  const getTransitionMessage = () => {
    if (!previousStatus || previousStatus === status) return config.message;
    
    const transitions = {
      'running_to_paused': 'The bot has been paused. All open positions have been closed.',
      'paused_to_running': 'The bot has been resumed and is now actively trading.',
      'running_to_stopped': 'The bot has been stopped. All open positions have been closed.',
      'paused_to_stopped': 'The bot has been stopped from its paused state.',
      'running_to_error': 'The bot encountered an error while running. Check the logs for details.',
      'running_to_market_closed': 'The market has closed. The bot will resume when the market opens.',
      'market_closed_to_running': 'The market has opened. The bot has resumed trading.',
    };
    
    const transitionKey = `${previousStatus}_to_${status}`;
    return transitions[transitionKey] || config.message;
  };

  useEffect(() => {
    // Auto-close notification after 5 seconds for certain statuses
    if (config.autoClose) {
      const timer = setTimeout(() => {
        setVisible(false);
      }, 5000);
      
      setAutoCloseTimer(timer);
      
      return () => {
        if (timer) clearTimeout(timer);
      };
    }
  }, [status, config.autoClose]);

  const handleClose = () => {
    setVisible(false);
    if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
    }
    if (onClose) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className={`${config.bgColor} border ${config.borderColor} rounded-lg p-4 mb-4 relative`}
        >
          <button
            onClick={handleClose}
            className="absolute top-2 right-2 text-gray-400 hover:text-white"
            aria-label="Close notification"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <div className="flex items-start">
            <div className="flex-shrink-0">{config.icon}</div>
            <div className="ml-3 flex-1">
              <h3 className={`text-lg font-medium ${config.textColor}`}>{config.title}</h3>
              <div className="mt-1 text-sm text-gray-300">{getTransitionMessage()}</div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default StatusNotification;
