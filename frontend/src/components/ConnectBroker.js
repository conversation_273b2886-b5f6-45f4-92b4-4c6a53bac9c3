import { useState } from "react";
import axios from "axios";

export default function ConnectOANDA() {
  const [status, setStatus] = useState("");

  const handleConnect = async () => {
    try {
      const response = await axios.get("http://localhost:8000/connect-oanda");
      setStatus(response.data.message);
    } catch (error) {
      setStatus("Failed to connect to OANDA." + error.message);
    }
  };

  return (
    <div className="bg-white shadow-lg rounded-2xl p-6 text-center max-w-sm w-full text-gray-800">
      <button onClick={handleConnect} className="w-full p-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition">
        Connect to OANDA
      </button>
      {status && <p className="text-green-600 mt-4">{status}</p>}
    </div>
  );
}