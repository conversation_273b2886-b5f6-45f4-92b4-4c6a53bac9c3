import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function RiskManagementModal({ isOpen, onClose, onConfirm, strategy }) {
  // Default values
  const defaultMaxDailyLoss = 5; // 5% of account balance
  const defaultMaxPositionSize = 10; // 10% of account balance
  const defaultRuntime = 7; // 7 days
  const defaultTotalProfitTarget = 20; // 20% of account balance
  const defaultTotalLossLimit = 10; // 10% of account balance

  // State for form fields - only additional parameters
  const [maxDailyLoss, setMaxDailyLoss] = useState(defaultMaxDailyLoss);
  const [maxPositionSize, setMaxPositionSize] = useState(defaultMaxPositionSize);
  const [runtime, setRuntime] = useState(defaultRuntime);
  const [totalProfitTarget, setTotalProfitTarget] = useState(defaultTotalProfitTarget);
  const [totalLossLimit, setTotalLossLimit] = useState(defaultTotalLossLimit);
  const [avoidHighSpread, setAvoidHighSpread] = useState(true); // Default to true for safety

  const [errors, setErrors] = useState({});

  // Extract existing values from strategy
  useEffect(() => {
    if (strategy && strategy.strategy_json) {
      let strategyData;

      // Parse strategy JSON if it's a string
      if (typeof strategy.strategy_json === 'string') {
        try {
          strategyData = JSON.parse(strategy.strategy_json);
        } catch (e) {
          console.error('Error parsing strategy JSON:', e);
          strategyData = {};
        }
      } else {
        strategyData = strategy.strategy_json;
      }

      console.log('Parsed strategy data:', strategyData);

      // Check for risk management parameters
      if (strategyData.riskManagement) {
        const rm = strategyData.riskManagement;

        // Set additional parameters
        if (rm.maxDailyLoss) {
          const maxDailyLossValue = parseFloat(rm.maxDailyLoss.replace('%', ''));
          if (!isNaN(maxDailyLossValue)) {
            setMaxDailyLoss(maxDailyLossValue);
          }
        }

        if (rm.maxPositionSize) {
          const maxPositionSizeValue = parseFloat(rm.maxPositionSize.replace('%', ''));
          if (!isNaN(maxPositionSizeValue)) {
            setMaxPositionSize(maxPositionSizeValue);
          }
        }

        if (rm.runtime) {
          setRuntime(parseInt(rm.runtime));
        }

        if (rm.totalProfitTarget) {
          const totalProfitTargetValue = parseFloat(rm.totalProfitTarget.replace('%', ''));
          if (!isNaN(totalProfitTargetValue)) {
            setTotalProfitTarget(totalProfitTargetValue);
          }
        }

        if (rm.totalLossLimit) {
          const totalLossLimitValue = parseFloat(rm.totalLossLimit.replace('%', ''));
          if (!isNaN(totalLossLimitValue)) {
            setTotalLossLimit(totalLossLimitValue);
          }
        }

        // Extract avoidHighSpread setting
        if (rm.avoidHighSpread !== undefined) {
          setAvoidHighSpread(rm.avoidHighSpread);
        }
      }
    }
  }, [strategy]);

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Validate additional parameters
    if (maxDailyLoss <= 0 || maxDailyLoss > 20) {
      newErrors.maxDailyLoss = 'Max daily loss must be between 0.1% and 20%';
    }

    if (maxPositionSize <= 0 || maxPositionSize > 50) {
      newErrors.maxPositionSize = 'Max position size must be between 0.1% and 50%';
    }

    if (runtime <= 0 || runtime > 90) {
      newErrors.runtime = 'Runtime must be between 1 and 90 days';
    }

    if (totalProfitTarget <= 0 || totalProfitTarget > 100) {
      newErrors.totalProfitTarget = 'Total profit target must be between 0.1% and 100%';
    }

    if (totalLossLimit <= 0 || totalLossLimit > 50) {
      newErrors.totalLossLimit = 'Total loss limit must be between 0.1% and 50%';
    }

    // Check for risky parameter combinations that might cause insufficient margin
    if (strategy && strategy.strategy_json) {
      let strategyData;

      // Parse strategy JSON if it's a string
      if (typeof strategy.strategy_json === 'string') {
        try {
          strategyData = JSON.parse(strategy.strategy_json);
        } catch (e) {
          console.error('Error parsing strategy JSON:', e);
          strategyData = {};
        }
      } else {
        strategyData = strategy.strategy_json;
      }

      if (strategyData.riskManagement) {
        const rm = strategyData.riskManagement;

        // Check for high risk percentage combined with large fixed pips
        if (rm.stopLossMethod === 'fixed' && rm.fixedPips && rm.riskPercentage) {
          const riskPct = parseFloat(rm.riskPercentage);
          const fixedPips = parseFloat(rm.fixedPips);

          if (riskPct >= 10 && fixedPips >= 50) {
            newErrors.marginWarning = 'Warning: High risk percentage combined with large fixed pips may cause insufficient margin errors. Consider reducing risk percentage or fixed pips value.';
          }
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Create risk management config with only additional parameters
      const riskManagementConfig = {
        // Additional parameters
        maxDailyLoss: `${maxDailyLoss}%`,
        maxPositionSize: `${maxPositionSize}%`,
        runtime: runtime,
        totalProfitTarget: `${totalProfitTarget}%`,
        totalLossLimit: `${totalLossLimit}%`,
        avoidHighSpread: avoidHighSpread
      };

      console.log('Risk management config being sent:', riskManagementConfig);
      onConfirm(riskManagementConfig);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className="bg-gray-900 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto"
        >
          <div className="p-6 border-b border-gray-800">
            <h2 className="text-xl font-semibold text-white">Risk Management</h2>
            <p className="text-gray-400 mt-1">
              Configure risk parameters for your trading bot
            </p>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Trading Bot Parameters</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* Max Daily Loss */}
                <div>
                  <label className="block text-gray-300 mb-2">Max Daily Loss (%)</label>
                  <div className="relative">
                    <input
                      type="number"
                      value={maxDailyLoss}
                      onChange={(e) => setMaxDailyLoss(parseFloat(e.target.value))}
                      step="0.1"
                      min="0.1"
                      max="20"
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <span className="text-gray-400">%</span>
                    </div>
                  </div>
                  {errors.maxDailyLoss && (
                    <p className="text-red-500 text-sm mt-1">{errors.maxDailyLoss}</p>
                  )}
                  <p className="text-gray-500 text-sm mt-1">
                    Bot will stop trading if daily losses exceed this percentage
                  </p>
                </div>

                {/* Max Position Size */}
                <div>
                  <label className="block text-gray-300 mb-2">Max Position Size (%)</label>
                  <div className="relative">
                    <input
                      type="number"
                      value={maxPositionSize}
                      onChange={(e) => setMaxPositionSize(parseFloat(e.target.value))}
                      step="0.1"
                      min="0.1"
                      max="50"
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <span className="text-gray-400">%</span>
                    </div>
                  </div>
                  {errors.maxPositionSize && (
                    <p className="text-red-500 text-sm mt-1">{errors.maxPositionSize}</p>
                  )}
                  <p className="text-gray-500 text-sm mt-1">
                    Maximum percentage of account balance used for a single position
                  </p>
                </div>

                {/* Runtime */}
                <div>
                  <label className="block text-gray-300 mb-2">Bot Runtime (days)</label>
                  <input
                    type="number"
                    value={runtime}
                    onChange={(e) => setRuntime(parseInt(e.target.value))}
                    step="1"
                    min="1"
                    max="90"
                    className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.runtime && (
                    <p className="text-red-500 text-sm mt-1">{errors.runtime}</p>
                  )}
                  <p className="text-gray-500 text-sm mt-1">
                    Number of days the bot will run before stopping
                  </p>
                </div>

                {/* Total Profit Target */}
                <div>
                  <label className="block text-gray-300 mb-2">Total Profit Target (%)</label>
                  <div className="relative">
                    <input
                      type="number"
                      value={totalProfitTarget}
                      onChange={(e) => setTotalProfitTarget(parseFloat(e.target.value))}
                      step="0.1"
                      min="0.1"
                      max="100"
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <span className="text-gray-400">%</span>
                    </div>
                  </div>
                  {errors.totalProfitTarget && (
                    <p className="text-red-500 text-sm mt-1">{errors.totalProfitTarget}</p>
                  )}
                  <p className="text-gray-500 text-sm mt-1">
                    Bot will stop trading when this profit target is reached
                  </p>
                </div>

                {/* Total Loss Limit */}
                <div>
                  <label className="block text-gray-300 mb-2">Total Loss Limit (%)</label>
                  <div className="relative">
                    <input
                      type="number"
                      value={totalLossLimit}
                      onChange={(e) => setTotalLossLimit(parseFloat(e.target.value))}
                      step="0.1"
                      min="0.1"
                      max="50"
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <span className="text-gray-400">%</span>
                    </div>
                  </div>
                  {errors.totalLossLimit && (
                    <p className="text-red-500 text-sm mt-1">{errors.totalLossLimit}</p>
                  )}
                  <p className="text-gray-500 text-sm mt-1">
                    Bot will stop trading when this loss limit is reached
                  </p>
                </div>
              </div>

              {/* Avoid High Spread Toggle */}
              <div className="mt-6">
                <div className="flex items-center">
                  <input
                    id="avoidHighSpread"
                    type="checkbox"
                    checked={avoidHighSpread}
                    onChange={(e) => setAvoidHighSpread(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded"
                  />
                  <label htmlFor="avoidHighSpread" className="ml-2 block text-gray-300">
                    Avoid trading when spread is too high
                  </label>
                </div>
                <p className="text-gray-500 text-sm mt-1 ml-6">
                  When enabled, the bot will not enter trades when the spread between bid and ask prices is unusually high
                </p>
              </div>
            </div>

            {/* Display margin warning if present */}
            {errors.marginWarning && (
              <div className="mx-6 mb-4 p-4 bg-yellow-900/50 border border-yellow-600/50 rounded-lg">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-300">{errors.marginWarning}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="p-6 border-t border-gray-800 flex justify-end space-x-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-800 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors"
              >
                Confirm
              </button>
            </div>
          </form>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
