import { useState, useEffect } from "react";
import axios from "axios";

export default function AccountOverview() {
  const [accountData, setAccountData] = useState(null);

  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    axios.get(`http://localhost:8000/oanda/account?user_id=${userId}`)
      .then(response => setAccountData(response.data))
      .catch(error => console.error("Error fetching account data:", error));
  }, []);

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg">
      <h2 className="text-2xl font-semibold text-gray-800 mb-4">OANDA Account</h2>
      {accountData ? (
        <div>
          <p><strong>Account ID:</strong> {accountData.account.id}</p>
          <p><strong>Balance:</strong> ${accountData.account.balance}</p>
        </div>
      ) : (
        <p>Loading account details...</p>
      )}
    </div>
  );
}