// SignupForm.js
import React, { useState } from "react";
import { useRouter } from "next/router";
import { createUserWithEmailAndPassword } from "firebase/auth";
import { auth } from "../../firebaseConfig";
import axios from "axios";
import { USE_FIREBASE_EMULATOR } from "../config";
import LoadingSpinner from "./LoadingSpinner";
import Link from 'next/link';

export default function SignupForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  console.log(USE_FIREBASE_EMULATOR);
  const SIGNUP_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/signup"
    : "https://signup-ihjc6tjxia-uc.a.run.app";

  const handleSignup = async (e) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      setIsLoading(false);
      return;
    }
    // print the signup url
    console.log("Singup url", SIGNUP_URL);

    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;
      
      const response = await axios.post(
        SIGNUP_URL,
        JSON.stringify({
          email: user.email,
          firebase_uid: user.uid,
        }),
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 201 || response.status === 200) {
        // Wait for the user data to be created in the database
        const GET_USER_URL = USE_FIREBASE_EMULATOR
          ? "http://127.0.0.1:5001/oryntrade/us-central1/get_user"
          : "https://get-user-ihjc6tjxia-uc.a.run.app";
        
        // Poll for user data to be available
        let retries = 0;
        const maxRetries = 5;
        while (retries < maxRetries) {
          try {
            const userResponse = await axios.get(`${GET_USER_URL}?firebase_uid=${user.uid}`);
            if (userResponse.status === 200) {
              // User data is available, proceed with navigation
              router.push("/dashboard");
              return;
            }
          } catch (error) {
            console.log("Waiting for user data to be available...");
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
          retries++;
        }
        
        // If we get here, we couldn't verify the user data
        setError("Signup successful but there was an issue verifying your account. Please try logging in.");
      } else {
        setError("Signup failed. Try again.");
      }
    } catch (error) {
      setError(
        error.response?.data?.message || error.message || "Signup failed. Try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <form onSubmit={handleSignup} className="space-y-4">
        {error && <p className="text-red-500 text-sm">{error}</p>}
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Email"
          required
          className="w-full p-3 border border-gray-300 rounded-md"
        />
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Password"
          required
          className="w-full p-3 border border-gray-300 rounded-md"
        />
        <input
          type="password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          placeholder="Confirm Password"
          required
          className="w-full p-3 border border-gray-300 rounded-md"
        />
        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-blue-600 text-white p-3 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <LoadingSpinner size="small" text="Creating account..." />
          ) : (
            "Sign up"
          )}
        </button>
      </form>
      <div className="mt-4 text-center">
        <Link href="/login" className="text-blue-500 hover:text-blue-600">
          Back to Login
        </Link>
      </div>
      {isLoading && <LoadingSpinner fullScreen text="Setting up your account..." />}
    </>
  );
}