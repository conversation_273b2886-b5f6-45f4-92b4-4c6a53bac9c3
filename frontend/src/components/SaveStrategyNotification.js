import { motion, AnimatePresence } from 'framer-motion';

export default function SaveStrategyNotification({ isOpen, onClose, isUpdate = false }) {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="w-full max-w-md p-6 bg-white/5 border border-white/10 rounded-xl shadow-xl"
          >
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-green-500/10 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            
            <h3 className="text-xl font-semibold text-[#FEFEFF] text-center mb-2">
              {isUpdate ? 'Strategy Updated!' : 'Strategy Saved!'}
            </h3>
            
            <p className="text-[#FEFEFF]/80 text-center mb-6">
              {isUpdate 
                ? 'Your strategy has been successfully updated. You can now use it for backtesting or live trading.'
                : 'Your strategy has been successfully saved to your account. You can now use it for backtesting or live trading.'}
            </p>
            
            <div className="flex justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onClose}
                className="px-6 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200"
              >
                Continue
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
} 