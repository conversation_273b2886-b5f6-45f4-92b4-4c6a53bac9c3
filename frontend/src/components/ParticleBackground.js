import { useCallback } from "react";
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim"; //   Use loadSlim instead of loadFull

export default function ParticleBackground() {
  const particlesInit = useCallback(async (engine) => {
    console.log("Particles Engine Loaded:", engine);

    if (!engine) {
      console.error("Particles Engine is undefined!");
      return;
    }

    try {
      await loadSlim(engine); //   Ensures tsparticles is loaded properly
      console.log("Particles Engine Loaded Successfully");
    } catch (error) {
      console.error("Error loading tsparticles:", error);
    }
  }, []);

  return (
    <Particles
      init={particlesInit}
      options={{
        background: { color: { value: "#f5f5f7" } },
        particles: {
          number: { value: 50 },
          move: { speed: 0.5 },
          shape: { type: "circle" },
          opacity: { value: 0.3 },
        },
      }}
      style={{ position: "absolute", width: "100%", height: "100%", zIndex: -1 }}
    />
  );
}
