import React from "react";
import { format as formatDate } from "date-fns";

const TradeHistoryRow = ({ trade }) => {
  // Parse numeric values
  const units = parseFloat(trade.units);
  const price = parseFloat(trade.price);
  const initialMarginRequired = parseFloat(trade.initialMarginRequired);
  const halfSpreadCost = parseFloat(trade.halfSpreadCost || 0);
  const commission = parseFloat(trade.commission || 0);
  const unrealizedPL = parseFloat(trade.unrealizedPL || 0);
  const realizedPL = parseFloat(trade.realizedPL || 0);

  // Format time in local timezone
  const formatTime = (time) => {
    if (!time) return "N/A";
    try {
      let dateToFormat;

      // If it's a Firestore Timestamp
      if (time.seconds && time.nanoseconds) {
        dateToFormat = new Date(
          time.seconds * 1000 + time.nanoseconds / 1000000
        );
      }
      // If it's a Firestore Timestamp with toDate method
      else if (time.toDate) {
        dateToFormat = time.toDate();
      }
      // If it's already a Date object
      else if (time instanceof Date) {
        dateToFormat = time;
      }
      // If it's a string, try to parse it
      else if (typeof time === "string") {
        console.log("Parsing string date:", time);
        // Handle ISO string with or without Z
        try {
          // First try direct parsing
          dateToFormat = new Date(time);

          // If that fails, try with Z replacement
          if (isNaN(dateToFormat.getTime())) {
            dateToFormat = new Date(time.replace("Z", "+00:00"));
          }

          console.log("Parsed date:", dateToFormat);
        } catch (e) {
          console.error("Error parsing date string:", e);
        }
      }

      // Validate the date
      if (!dateToFormat || isNaN(dateToFormat.getTime())) {
        console.warn("Invalid date value:", time);
        return "Invalid Date";
      }

      return dateToFormat.toLocaleString(undefined, {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: true,
      });
    } catch (error) {
      console.error("Error formatting time:", error);
      return "Invalid Date";
    }
  };

  const formattedTime = formatTime(trade.openTime);

  return (
    <tr className="border-b border-gray-700 hover:bg-gray-800">
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        {formattedTime}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        {trade.instrument}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        <span
          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
            units > 0
              ? "bg-green-900 text-green-300"
              : "bg-red-900 text-red-300"
          }`}
        >
          {units > 0 ? "Buy" : "Sell"}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        {Math.abs(units).toLocaleString()}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        {price.toFixed(5)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        ${initialMarginRequired.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        ${halfSpreadCost.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        ${commission.toFixed(2)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        <span className={realizedPL >= 0 ? "text-green-400" : "text-red-400"}>
          ${realizedPL.toFixed(2)}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        <span className={unrealizedPL >= 0 ? "text-green-400" : "text-red-400"}>
          ${unrealizedPL.toFixed(2)}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        <span
          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
            trade.status === "OPEN"
              ? "bg-blue-900 text-blue-300"
              : "bg-gray-700 text-gray-300"
          }`}
        >
          {trade.status}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
        {trade.status === "CLOSED" ? (
          <>
            {console.log("closeTime:", trade.closeTime)}
            {formatTime(trade.closeTime)}
          </>
        ) : "N/A"}
      </td>
    </tr>
  );
};

export default TradeHistoryRow;
