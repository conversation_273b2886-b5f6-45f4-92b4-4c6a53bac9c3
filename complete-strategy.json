{"name": "my strat", "instruments": "EUR/USD", "timeframe": "1h", "indicators": [{"id": "1740973993639h80lpwlczf", "indicator_class": "RSI", "parameters": {"period": 14}}], "entryRules": [{"id": "1740973994922n319qxi6kek", "tradeType": "buy", "indicator1": "1740973993639h80lpwlczf", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30"}], "exitRules": [{"id": "1740974005992n217ky51vxl", "tradeType": "buy", "indicator1": "1740973993639h80lpwlczf", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70"}], "riskManagement": {"stopLoss": "1%", "takeProfit": "2%"}, "TimeZone": "UTC", "id": "test-strategy-001", "user_id": "test123", "status": "pending"}