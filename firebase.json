{"functions": {"source": "functions", "runtime": "python312", "ignore": ["venv", ".git", "firebase-debug.log", "firebase-debug.*.log"]}, "firestore": {"rules": "firebase/firestore.rules"}, "database": {"rules": "database.rules.json"}, "hosting": {"public": "frontend/out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "redirects": [{"source": "/dashboard", "destination": "/", "type": 301}, {"source": "/login", "destination": "/", "type": 301}, {"source": "/signup", "destination": "/", "type": 301}, {"source": "/settings", "destination": "/", "type": 301}, {"source": "/strategy-generation", "destination": "/", "type": 301}, {"source": "/trade-execution", "destination": "/", "type": 301}, {"source": "/connect", "destination": "/", "type": 301}], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8082}, "ui": {"enabled": true}, "singleProjectMode": true, "database": {"port": 9000}, "pubsub": {"port": 8108}, "eventarc": {"port": 9299}, "tasks": {"port": 9499}}}