import subprocess
import os
from dotenv import load_dotenv

# Load environment variables from .env file in functions directory
functions_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'functions')
env_path = os.path.join(functions_dir, '.env')
load_dotenv(env_path)

def get_env_vars():
    """Get all environment variables from .env file"""
    env_vars = []
    print("Checking environment variables...")
    for key, value in os.environ.items():
        if key in [
            'OPENAI_API_KEY',
            'OANDA_API_KEY',
            'OANDA_API_URL',
            'BUCKET_NAME',
            'CSV_FILENAME',
            'DEFAULT_SYMBOL',
            'DEFAULT_TIMEFRAME',
            'STRIPE_SECRET_KEY',
            'STRIPE_PUBLISHABLE_KEY',
            'USE_FIREBASE_EMULATOR',
            'LOCAL_FOREX_DATA_DIR'
        ]:
            print(f"Found {key}")
            env_vars.append(f"{key}={value}")

    result = ','.join(env_vars)
    print(f"Environment variables string: {result}")
    return result

def deploy_http_function(function_name, memory="256Mi", timeout="60s"):
    """
    Deploys an HTTP-triggered Firebase Cloud Function.
    """
    command = [
        "gcloud", "functions", "deploy",
        "--gen2",
        "--region=us-central1",
        "--project=oryntrade",
        "--runtime=python312",
        "--build-service-account=projects/oryntrade/serviceAccounts/<EMAIL>",
        f"--memory={memory}",
        f"--timeout={timeout}",
        "--trigger-http",
        "--no-allow-unauthenticated",
        "--source=functions/",
        f"--set-env-vars={get_env_vars()}",
        function_name,
        f"--entry-point={function_name}"
    ]

    try:
        print(f"Deploying HTTP function: {function_name}")
        subprocess.run(command, check=True)
        print(f"Function {function_name} deployed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error deploying function {function_name}: {e}")
    except FileNotFoundError:
        print("Error: gcloud command not found. Make sure Google Cloud SDK is installed.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

def deploy_firestore_function(function_name):
    """
    Deploys a Firestore-triggered Firebase Cloud Function.
    """
    command = [
        "gcloud", "functions", "deploy",
        "--gen2",
        "--region=us-central1",
        "--project=oryntrade",
        "--runtime=python312",
        "--build-service-account=projects/oryntrade/serviceAccounts/<EMAIL>",
        "--memory=1024Mi",
        "--trigger-event-filters=type=google.cloud.firestore.document.v1.created",
        "--trigger-event-filters=database=(default)",
        "--trigger-event-filters-path-pattern=document=users/{userId}/submittedStrategies/{strategyId}",
        "--trigger-location=nam5",
        "--source=functions/",
        "--no-allow-unauthenticated",
        f"--set-env-vars={get_env_vars()},FUNCTION_SIGNATURE_TYPE=cloudevent,FUNCTION_TARGET=publish_strategy",
        function_name,
        f"--entry-point={function_name}"
    ]

    try:
        print(f"Deploying Firestore function: {function_name}")
        subprocess.run(command, check=True)
        print(f"Function {function_name} deployed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error deploying function {function_name}: {e}")
    except FileNotFoundError:
        print("Error: gcloud command not found. Make sure Google Cloud SDK is installed.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

def deploy_pubsub_function(function_name, topic_name, memory="256Mi", timeout="60s"):
    """
    Deploys a Pub/Sub-triggered Firebase Cloud Function.
    """
    command = [
        "gcloud", "functions", "deploy",
        "--gen2",
        "--region=us-central1",
        "--project=oryntrade",
        "--runtime=python312",
        "--build-service-account=projects/oryntrade/serviceAccounts/<EMAIL>",
        f"--memory={memory}",
        f"--timeout={timeout}",
        f"--trigger-topic={topic_name}",
        "--source=functions/",
        "--no-allow-unauthenticated",
        f"--set-env-vars={get_env_vars()}",
        function_name,
        f"--entry-point={function_name}"
    ]

    try:
        print(f"Deploying Pub/Sub function: {function_name} (topic: {topic_name})")
        subprocess.run(command, check=True)
        print(f"Function {function_name} deployed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error deploying function {function_name}: {e}")
    except FileNotFoundError:
        print("Error: gcloud command not found. Make sure Google Cloud SDK is installed.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

# List of HTTP functions to deploy
http_functions = [
    "signup",
    "get_user",
    "register_api_key",
    "change_password",
    "delete_account",
    "get_oanda_account",
    "save_strategy",
    "update_strategy",
    "get_strategies"
]

# List of Firestore-triggered functions
firestore_functions = [
    "publish_strategy"
]

# List of Pub/Sub-triggered functions with their topics
pubsub_functions = {
    "fetch_new_candle_and_store": "update-candles-1h"
}

# List of functions that need more memory
high_memory_functions = {
    "run_backtest": "1024Mi",  # 1GB for complex calculations
    "ai_strategy": "512Mi",     # 512MB for OpenAI API calls
    "fetch_and_store_historical_data": "512Mi",  # 512MB for data processing and GCS uploads
    "fetch_new_candle_and_store": "512Mi",  # 512MB for gap detection, backfill, and LIFO operations
    "fetch_historical_candles_from_gcs": "1024Mi"  # 1GB for parallel processing of large historical datasets
}

# List of functions that need longer timeouts
long_timeout_functions = {
    "fetch_and_store_historical_data": "540s",  # 9 minutes for data fetching and processing
    "run_backtest": "300s",  # 5 minutes for backtesting
    "fetch_historical_candles_from_gcs": "300s"  # 5 minutes for processing large historical datasets
}

if __name__ == "__main__":
    # Ensure we're in the project root directory
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # Deploy HTTP functions
    for function_name in http_functions:
        deploy_http_function(function_name)

    # Deploy high-memory HTTP functions
    for function_name, memory in high_memory_functions.items():
        timeout = long_timeout_functions.get(function_name, "60s")
        deploy_http_function(function_name, memory, timeout)

    # Deploy Firestore-triggered functions
    for function_name in firestore_functions:
        deploy_firestore_function(function_name)

    # Deploy Pub/Sub-triggered functions
    for function_name, topic_name in pubsub_functions.items():
        memory = high_memory_functions.get(function_name, "256Mi")
        timeout = long_timeout_functions.get(function_name, "60s")
        deploy_pubsub_function(function_name, topic_name, memory, timeout)