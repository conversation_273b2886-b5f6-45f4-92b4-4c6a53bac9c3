# oryn

Platform for trading Forex by generating automated strategies based on preferences and execute them with our bot

# How to run:

Run builld.sh to install necessary dependencies and run the website

# When using firebase emulator for first time:

- Go to functions folder and run:
  - `python3.12 -m venv venv` Note: `python3.12` is specifically needed!
  - `. venv/bin/activate`
  - `python3.12 -m pip install -r requirements.txt`
- `firebase emulators:start --debug`. Having the `--debug` flag helps catch errors

# Deploy Firebase functions:

There are two ways to deploy the Firebase functions:

1. Using the deployment script (recommended):

```bash
python scripts/deploy_firebase_functions.py
```

This script will:

- Set up a virtual environment
- Install required dependencies
- Find all functions in `main.py`
- Deploy each function with the following configuration:
  - Python 3.12 runtime
  - 1GB memory
  - HTTP trigger
  - Authentication required
  - Gen2 functions

2. Deploy individual functions manually:

```bash
gcloud functions deploy <function_name> \
 --gen2 \
 --region=us-central1 \
 --project=oryntrade \
 --runtime=python312 \
 --entry-point=<function_name> \
 --build-service-account=projects/oryntrade/serviceAccounts/<EMAIL> \
 --memory=1024Mi \
 --trigger-http \
 --source=functions/
```

3. Deploy Firestore-triggered functions:

```bash
gcloud functions deploy publish_strategy \
  --gen2 \
  --region=us-central1 \
  --project=oryntrade \
  --runtime=python312 \
  --entry-point=publish_strategy \
  --build-service-account=projects/oryntrade/serviceAccounts/<EMAIL> \
  --memory=1024Mi \
  --trigger-event-filters="type=google.cloud.firestore.document.v1.created" \
  --trigger-event-filters="database=(default)" \
  --trigger-event-filters-path-pattern="document=users/{userId}/submittedStrategies/{strategyId}" \
  --trigger-location=nam5 \
  --source=functions/ \
  --no-allow-unauthenticated \
  --set-env-vars="FUNCTION_SIGNATURE_TYPE=cloudevent,FUNCTION_TARGET=publish_strategy"
```

The key components for Firestore triggers:

- `--trigger-event-filters="type=google.cloud.firestore.document.v1.created"`: Triggers when a new document is created
- `--trigger-event-filters="database=(default)"`: Specifies the Firestore database to monitor
- `--trigger-event-filters-path-pattern="document=users/{userId}/submittedStrategies/{strategyId}"`: Specifies which document paths to watch
- `--trigger-location=nam5`: **Must match** your Firestore database location
- `--set-env-vars="FUNCTION_SIGNATURE_TYPE=cloudevent,FUNCTION_TARGET=publish_strategy"`: **Required** for Firestore triggers

Note: Before deploying, make sure you're authenticated with Google Cloud:

```bash
gcloud auth application-default login
gcloud config set project oryntrade
```

# Emulator for only functions

firebase emulators:start --only functions --project oryntrade

- When using the emulator, you need to set the environment variables in the .env.local file in the functions folder. The emulator will automatically pick up the environment variables from the .env file.

# Oryn Trading Bot

A Python-based trading bot that uses Google Kubernetes Engine for strategy execution with Firebase authentication and Google Cloud integration.

## Prerequisites

- Python 3.12 or higher
- Google Cloud SDK installed
- Firebase CLI installed
- Docker installed
- Kubectl installed
- A Google Cloud project with the following APIs enabled:
  - Google Kubernetes Engine (GKE)
  - Cloud Pub/Sub
  - Cloud Firestore
  - Firebase Authentication
  - Artifact Registry

## Local Development

1. Start the Firebase emulators:

```bash
firebase emulators:start
```

2. Start the Pub/Sub emulator:

```bash
gcloud beta emulators pubsub start --project=oryntrade
```

3. Set the Pub/Sub emulator host:

```bash
export PUBSUB_EMULATOR_HOST=localhost:8451
```

## System Architecture

The Oryn trading platform uses a Kubernetes-based architecture with dedicated services for strategy management and execution. The system is designed for scalability, security, and reliability.

```
                                     ORYN TRADING SYSTEM ARCHITECTURE
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                     Google Kubernetes Engine                                     │
│  ┌─────────────────────────────────────────────────────┐    ┌─────────────────────────────────┐ │
│  │               Strategy Controller Pod                │    │      Trade Bot Pod (Single)     │ │
│  │  ┌───────────────────────────────────────────────┐  │    │  ┌─────────────────────────────┐ │ │
│  │  │                                               │  │    │  │                             │ │ │
│  │  │  ┌─────────────┐         ┌────────────────┐  │  │    │  │   ┌─────────────────────┐   │ │ │
│  │  │  │ FastAPI App │◄────────┤ Authentication │  │  │    │  │   │  Strategy Execution │   │ │ │
│  │  │  └─────┬───────┘         └────────────────┘  │  │    │  │   └──────────┬──────────┘   │ │ │
│  │  │        │                                      │  │    │  │              │              │ │ │
│  │  │        │                  ┌────────────────┐  │  │    │  │   ┌─────────▼──────────┐   │ │ │
│  │  │        ▼                  │ K8s Client API │  │  │    │  │   │ Technical Analysis │   │ │ │
│  │  │  ┌─────────────┐          └────────┬───────┘  │  │    │  │   └──────────┬──────────┘   │ │ │
│  │  │  │ REST API    │                   │          │  │    │  │              │              │ │ │
│  │  │  │ Endpoints   │◄──────────────────┘          │  │    │  │   ┌─────────▼──────────┐   │ │ │
│  │  │  └─────┬───────┘                              │  │    │  │   │   Trading Logic    │   │ │ │
│  │  │        │                  ┌────────────────┐  │  │    │  │   └─────────────────────┘   │ │ │
│  │  │        ▼                  │ Enhanced       │  │  │    │  │                             │ │ │
│  │  │  ┌─────────────┐          │ Structured     │  │  │    │  │   ┌─────────────────────┐   │ │ │
│  │  │  │ Request     │◄─────────┤ Logging        │  │  │    │  │   │   REST API Health   │   │ │ │
│  │  │  │ Middleware  │          └────────────────┘  │  │    │  │   └─────────────────────┘   │ │ │
│  │  │  └─────────────┘                              │  │    │  │                             │ │ │
│  │  │                                               │  │    │  └─────────────────────────────┘ │ │
│  │  └───────────────────────────────────────────────┘  │    │                                 │ │
│  └─────────────────────────────────────────────────────┘    └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
                           │                                                     │
                           │                                                     │
┌──────────────────────────▼────────────────────────────────────────────────────▼─────────────────┐
│                                         Google Cloud                                             │
│  ┌──────────────────────┐    ┌───────────────────┐    ┌───────────────────┐   ┌───────────────┐ │
│  │    Pub/Sub Topic     │    │    Firestore DB   │    │   Firebase Auth   │   │ IAM & Workload│ │
│  │   strategy-execution │    │                   │    │                   │   │   Identity    │ │
│  └──────────┬───────────┘    └─────────┬─────────┘    └──────┬────────────┘   └───────────────┘ │
│             │                          │                      │                                  │
└─────────────┼──────────────────────────┼──────────────────────┼──────────────────────────────────┘
              │                          │                      │
              ▼                          ▼                      ▼
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                     External Services                                            │
│                                                                                                  │
│  ┌──────────────────────┐    ┌───────────────────┐    ┌───────────────────┐   ┌───────────────┐ │
│  │   Client Applications │    │   Market Data     │    │  (Future) Broker  │   │ (Future) Data │ │
│  │                      │    │   Providers       │    │  Integration      │   │  Analytics    │ │
│  └──────────────────────┘    └───────────────────┘    └───────────────────┘   └───────────────┘ │
│                                                                                                  │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Data Flow for Executing a Strategy

1. **Strategy Creation**

   - A strategy (like your RSI strategy) is created by a user
   - Strategy parameters include indicators, entry/exit rules, and risk management
   - The strategy is stored in Firestore under the user's account

2. **Strategy Deployment**

   - User authenticates with Firebase Authentication
   - The strategy controller verifies the user's identity and permissions
   - User triggers deployment via the strategy controller API
   - The strategy controller creates a Kubernetes pod for the strategy (or uses the single-strategy pod)

3. **Strategy Execution**
   - The strategy is published to the Pub/Sub topic
   - The trade bot service subscribes to the Pub/Sub messages
   - The trade bot loads and executes the strategy:
     - Fetches market data
     - Calculates indicators (RSI)
     - Evaluates entry/exit rules
     - Manages risk parameters
   - All activities are logged with structured logging

### Components and Roles

- **Strategy Controller**

  - Authenticates users via Firebase
  - Manages strategy lifecycles
  - Creates and monitors strategy pods
  - Provides status information
  - Enhanced logging and monitoring

- **Trade Bot**

  - Executes trading strategies
  - Performs technical analysis
  - Evaluates trading rules
  - Can run in single-strategy or multi-strategy mode
  - Reports status and performance

- **Google Cloud Services**
  - Firebase Auth: Handles authentication and authorization
  - Firestore: Stores strategies and user data
  - Pub/Sub: Handles asynchronous messaging between services
  - GKE: Runs containerized services with proper permissions

## Deployment

### GKE Setup

1. Set up GKE cluster (if not already created):

```bash
gcloud container clusters create-auto oryn-trading-cluster \
    --location=us-central1 \
    --project=oryntrade
```

2. Configure kubectl to use the GKE cluster:

```bash
gcloud container clusters get-credentials oryn-trading-cluster \
    --location=us-central1 \
    --project=oryntrade
```

### Data Storage

The system uses Firestore as the primary data store with the following structure:

```
users/
  {userId}/
    submittedStrategies/
      {strategyId}/
        - name: string
        - status: string
        - last_heartbeat: timestamp
        - accountBalance: {
            balance: number,
            lastUpdated: timestamp
          }
        - marketStatus: {
            is_open: boolean,
            server_time: timestamp,
            last_updated: timestamp
          }
        - summary: {
            totalTrades: number,
            totalRealizedPL: number,
            totalUnrealizedPL: number,
            winRate: number
          }
        - chartData: {
            candles: array,
            indicators: object
          }
        tradeHistory/
          {tradeId}/
            - status: string
            - instrument: string
            - entryPrice: number
            - currentPrice: number
            - realizedPL: number
            - unrealizedPL: number
            - timestamp: timestamp
```

### Frontend-Backend Communication

The frontend communicates directly with Firestore to:

1. Read strategy status and data
2. Monitor account balance
3. Track open trades
4. View trade history
5. Access chart data

The backend services (Strategy Controller and Trade Bot) update the Firestore documents, which the frontend then reads to display the current state.

## Development Guidelines

1. **Frontend Development**

   - Use Firestore SDK for all data operations
   - Implement real-time listeners for live updates
   - Handle offline capabilities gracefully
   - Follow Firebase security rules

2. **Backend Development**

   - Update Firestore documents atomically
   - Use transactions for critical operations
   - Implement proper error handling
   - Follow structured logging practices

3. **Security**
   - Implement proper Firebase security rules
   - Validate all user inputs
   - Use service accounts for backend services
   - Follow least privilege principle
