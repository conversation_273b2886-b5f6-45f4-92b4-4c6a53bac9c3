{"name": "testStrat with RSI and SMA", "description": "", "instruments": "EUR/USD", "timeframe": "1m", "tradingSession": ["All"], "indicators": [{"id": "174612488387203ciapc536vx", "indicator_class": "RSI", "type": "RSI", "parameters": {"period": 14}, "source": "price"}, {"id": "17463095231729iwecbspi98", "indicator_class": "SMA", "type": "SMA", "parameters": {"period": 50}, "source": "price"}], "entryRules": [{"id": "1746124897193y54p1q6lo0o", "tradeType": "long", "indicator1": "174612488387203ciapc536vx", "operator": ">", "compareType": "value", "indicator2": "", "value": "1", "barRef": "close"}, {"id": "1746309541257yiwiv2isesr", "tradeType": "long", "indicator1": "17463095231729iwecbspi98", "operator": ">", "compareType": "value", "indicator2": "", "value": "1", "barRef": "close"}], "exitRules": [{"id": "17461249097601glkjy7s1idj", "tradeType": "long", "indicator1": "174612488387203ciapc536vx", "operator": ">", "compareType": "value", "indicator2": "", "value": "90", "barRef": "close"}], "riskManagement": {"stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage", "riskPerTrade": "1%", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%"}, "entryLongGroupOperator": "AND", "user_id": "hUli47EgkKnHdyryQhzTyvn7ehxp", "id": "QWat9liB348k0oumMRcN"}