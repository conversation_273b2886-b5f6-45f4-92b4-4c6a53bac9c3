# Trading Bot Service

A professional-grade trading bot service that executes trading strategies using the OANDA API for trade execution and Polygon.io for market data.

## Features

- Modular architecture with clear separation of concerns
- Support for multiple trading strategies (currently RSI-based)
- Real-time market data processing
- Risk management and position sizing
- Comprehensive logging and monitoring
- Configuration validation
- Graceful shutdown handling

## Project Structure

```
trade-bot-service/
├── data/               # Market data handling
│   └── market_data.py  # Polygon.io data provider
├── execution/          # Trade execution
│   └── oanda_client.py # OANDA API client
├── strategies/         # Trading strategies
│   ├── base_strategy.py
│   └── rsi_strategy.py
├── core/              # Core components
│   ├── trading_engine.py
│   ├── config_manager.py
│   └── strategy_loader.py
├── utils/             # Utilities
│   └── logger.py
├── config/            # Configuration files
│   ├── engine_config.json
│   └── strategy_config.json
├── logs/              # Log files
├── main.py           # Entry point
└── requirements.txt  # Dependencies
```

## Prerequisites

- Python 3.8 or higher
- OANDA API key and account ID
- Polygon.io API key
- Virtual environment (recommended)

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd trade-bot-service
```

2. Create and activate a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:

```bash
pip install -r requirements.txt
```

4. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your API keys and configuration
```

## Configuration

### Engine Configuration (engine_config.json)

```json
{
  "update_interval": 60,
  "max_trades_per_day": 5,
  "max_daily_loss": 0.02,
  "close_positions_on_shutdown": true
}
```

### Strategy Configuration (strategy_config.json)

```json
{
  "name": "RSI Strategy",
  "type": "rsi",
  "instruments": ["EUR_USD"],
  "timeframe": "1h",
  "multiplier": 1,
  "indicators": [
    {
      "type": "rsi",
      "parameters": {
        "period": 14
      }
    }
  ],
  "entryRules": [
    {
      "type": "rsi",
      "value": 30
    }
  ],
  "exitRules": [
    {
      "type": "rsi",
      "value": 70
    }
  ],
  "risk_management": {
    "position_size": {
      "max_position_size": 100000,
      "risk_per_trade": 0.01
    },
    "stop_loss": 0.02,
    "take_profit": 0.04
  }
}
```

## Usage

1. Start the trading bot:

```bash
python main.py
```

2. Monitor the logs:

```bash
tail -f logs/trading_bot.log
```

3. Stop the bot:

```bash
# Press Ctrl+C or send SIGTERM signal
```

## Development

### Code Style

The project uses:

- Black for code formatting
- Flake8 for linting
- MyPy for type checking

Run the formatters:

```bash
black .
flake8
mypy .
```

### Testing

Run tests with pytest:

```bash
pytest
```

## Error Handling

The bot includes comprehensive error handling:

- Market data fetch failures
- API connection issues
- Invalid configurations
- Trade execution errors
- Graceful shutdown on errors

All errors are logged with context for debugging.

## Logging

Logs are written to both:

- Console output
- Rotating log files in the `logs/` directory

Log files are automatically rotated when they reach 10MB and keep 5 backup files.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
