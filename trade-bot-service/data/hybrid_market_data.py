import os
from typing import Dict, List, Optional
from utils.logger import Logger
from data.market_data import MarketDataProvider
from data.websocket_market_data import WebSocketMarketDataProvider

class HybridMarketDataWrapper:
    """
    Hybrid market data wrapper that intelligently chooses between basic and enhanced providers.
    
    This wrapper:
    1. Tri<PERSON> to use EnhancedMarketDataProvider (GCS + Polygon) first
    2. Falls back to basic MarketDataProvider (Polygon only) if GCS fails
    3. Provides seamless switching based on availability and performance
    """

    def __init__(self, api_key: str):
        """
        Initialize the hybrid market data wrapper.

        Args:
            api_key (str): Polygon.io API key
        """
        self.api_key = api_key
        self.logger = Logger("HybridMarketDataWrapper")
        
        # Initialize providers
        self.enhanced_provider = None
        self.basic_provider = None
        self.current_provider = None
        
        # Performance tracking
        self.provider_performance = {
            'enhanced': {'success_count': 0, 'error_count': 0, 'avg_time': 0},
            'basic': {'success_count': 0, 'error_count': 0, 'avg_time': 0}
        }
        
        # Configuration
        self.use_enhanced = os.getenv('USE_ENHANCED_MARKET_DATA', 'true').lower() == 'true'
        self.fallback_threshold = 3  # Number of consecutive errors before switching providers
        self.consecutive_errors = {'enhanced': 0, 'basic': 0}
        
        self._initialize_providers()
        
        self.logger.log_info("Hybrid market data wrapper initialized")

    def _initialize_providers(self):
        """Initialize market data providers."""
        try:
            # Always initialize basic provider as fallback
            self.basic_provider = MarketDataProvider(self.api_key)
            self.logger.log_info("Basic market data provider initialized")
            
            # Try to initialize enhanced provider if enabled
            if self.use_enhanced:
                try:
                    self.enhanced_provider = WebSocketMarketDataProvider(self.api_key)
                    self.current_provider = self.enhanced_provider
                    self.logger.log_info("WebSocket market data provider initialized and set as primary")
                except Exception as e:
                    self.logger.log_warning(f"Failed to initialize enhanced provider: {str(e)}")
                    self.current_provider = self.basic_provider
                    self.logger.log_info("Using basic provider as primary")
            else:
                self.current_provider = self.basic_provider
                self.logger.log_info("Enhanced provider disabled, using basic provider")
                
        except Exception as e:
            self.logger.log_error(f"Failed to initialize providers: {str(e)}")
            raise

    def get_candles(self, symbol: str, timespan: str = "minute",
                   multiplier: int = 1, count: int = 1000) -> Dict:
        """
        Fetch candle data using the best available provider.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")
            timespan (str): Time interval
            multiplier (int): Number of timespans to multiply by
            count (int): Number of candles to fetch

        Returns:
            Dict: Candle data response
        """
        import time
        start_time = time.time()
        
        # Determine which provider to use
        provider_to_use = self._select_provider()
        provider_name = self._get_provider_name(provider_to_use)
        
        try:
            # Attempt to fetch data
            result = provider_to_use.get_candles(symbol, timespan, multiplier, count)
            
            # Track success
            execution_time = time.time() - start_time
            self._track_success(provider_name, execution_time)
            
            # Add source information to result
            if result.get("status") == "success":
                source = result.get("source", "unknown")
                if provider_name == "enhanced" and source in ["gcs", "cache"]:
                    result["source"] = "gcs_websocket_hybrid"
                else:
                    result["source"] = source
                    
                self.logger.log_info(f"Candles fetched from: {result.get('source', 'unknown')}")
            
            return result
            
        except Exception as e:
            # Track error and try fallback
            self._track_error(provider_name)
            self.logger.log_error(f"Error with {provider_name} provider: {str(e)}")
            
            # Try fallback provider if available
            fallback_provider = self._get_fallback_provider(provider_to_use)
            if fallback_provider and fallback_provider != provider_to_use:
                fallback_name = self._get_provider_name(fallback_provider)
                self.logger.log_info(f"Trying fallback provider: {fallback_name}")
                
                try:
                    result = fallback_provider.get_candles(symbol, timespan, multiplier, count)
                    execution_time = time.time() - start_time
                    self._track_success(fallback_name, execution_time)
                    
                    # Add source information
                    if result.get("status") == "success":
                        result["source"] = f"{fallback_name}_fallback"
                        self.logger.log_info(f"Candles fetched from: {result.get('source', 'unknown')}")
                    
                    return result
                    
                except Exception as fallback_error:
                    self._track_error(fallback_name)
                    self.logger.log_error(f"Fallback provider also failed: {str(fallback_error)}")
            
            # If all providers fail, return error
            return {
                "status": "error",
                "message": f"All market data providers failed: {str(e)}"
            }

    def _select_provider(self):
        """Select the best provider based on performance and availability."""
        # If enhanced provider is disabled or not available, use basic
        if not self.enhanced_provider or not self.use_enhanced:
            return self.basic_provider
        
        # Check if enhanced provider has too many consecutive errors
        if self.consecutive_errors['enhanced'] >= self.fallback_threshold:
            self.logger.log_warning("Enhanced provider has too many errors, using basic provider")
            return self.basic_provider
        
        # Use enhanced provider by default
        return self.enhanced_provider

    def _get_fallback_provider(self, current_provider):
        """Get fallback provider for the given provider."""
        if current_provider == self.enhanced_provider:
            return self.basic_provider
        elif current_provider == self.basic_provider:
            return self.enhanced_provider
        return None

    def _get_provider_name(self, provider) -> str:
        """Get the name of the provider."""
        if provider == self.enhanced_provider:
            return "enhanced"
        elif provider == self.basic_provider:
            return "basic"
        return "unknown"

    def _track_success(self, provider_name: str, execution_time: float):
        """Track successful operation for a provider."""
        if provider_name in self.provider_performance:
            stats = self.provider_performance[provider_name]
            stats['success_count'] += 1
            
            # Update average execution time
            total_operations = stats['success_count'] + stats['error_count']
            if total_operations > 1:
                stats['avg_time'] = (stats['avg_time'] * (total_operations - 1) + execution_time) / total_operations
            else:
                stats['avg_time'] = execution_time
            
            # Reset consecutive errors on success
            self.consecutive_errors[provider_name] = 0

    def _track_error(self, provider_name: str):
        """Track error for a provider."""
        if provider_name in self.provider_performance:
            self.provider_performance[provider_name]['error_count'] += 1
            self.consecutive_errors[provider_name] += 1

    def get_quote(self, symbol: str) -> Dict:
        """Get quote using current provider."""
        return self.current_provider.get_quote(symbol)

    def check_market_status(self) -> Dict:
        """Check market status using current provider."""
        return self.current_provider.check_market_status()

    def check_market_conditions(self, symbol: str, trading_sessions: List[str] = None, 
                               avoid_high_spread: bool = True) -> Dict:
        """Check market conditions using current provider."""
        return self.current_provider.check_market_conditions(symbol, trading_sessions, avoid_high_spread)

    def get_performance_metrics(self) -> Dict:
        """Get performance metrics for all providers."""
        metrics = {
            'hybrid_wrapper': self.provider_performance.copy(),
            'current_provider': self._get_provider_name(self.current_provider),
            'consecutive_errors': self.consecutive_errors.copy()
        }
        
        # Add individual provider metrics
        if self.enhanced_provider:
            try:
                metrics['enhanced_provider'] = self.enhanced_provider.get_performance_metrics()
            except:
                pass
                
        if self.basic_provider:
            try:
                metrics['basic_provider'] = self.basic_provider.get_performance_metrics()
            except:
                pass
        
        return metrics

    def cleanup(self):
        """Clean up all providers."""
        try:
            if self.enhanced_provider:
                self.enhanced_provider.cleanup()
            if self.basic_provider and hasattr(self.basic_provider, 'cleanup'):
                self.basic_provider.cleanup()
            self.logger.log_info("Hybrid market data wrapper cleaned up")
        except Exception as e:
            self.logger.log_error(f"Error during cleanup: {str(e)}")
