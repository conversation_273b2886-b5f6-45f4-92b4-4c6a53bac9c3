import os
import json
import asyncio
import websockets
import threading
from typing import Dict, List, Optional, Callable
from datetime import datetime, timezone
from collections import deque
from utils.logger import Logger
from data.market_data import MarketDataProvider
from google.cloud import storage
import dateutil.parser
from utils.candle_builder import CandleBuilder

class WebSocketMarketDataProvider(MarketDataProvider):
    """
    Market data provider that uses GCS for historical data and WebSocket for real-time updates.
    Maintains a 1000-candle FIFO window just like the frontend.
    """

    def __init__(self, api_key: str):
        """Initialize the WebSocket market data provider."""
        super().__init__(api_key)
        
        # Override logger name
        self.logger = Logger("WebSocketMarketDataProvider")
        
        # GCS configuration
        self.bucket_name = os.getenv('HISTORICAL_DATA_BUCKET', 'oryntrade-forex-candles-data')
        self.gcs_client = None
        self.bucket = None
        self._init_gcs_client()

        # WebSocket configuration
        self.websocket_url = os.getenv('WEBSOCKET_SERVICE_URL', 'ws://localhost:8081/ws')
        self.ws = None
        self.ws_thread = None
        self.is_connected = False
        self.should_reconnect = True

        # Real-time data management
        self.candle_buffer = {}  # symbol_timeframe -> deque of candles (FIFO)
        self.buffer_size = 1000  # Maintain 1000 candles
        self.subscriptions = set()  # Track active subscriptions
        self.update_callbacks = {}  # symbol_timeframe -> callback function

        # Candle building for higher timeframes
        self.candle_builders = {}  # symbol_timeframe -> CandleBuilder
        self.pending_candles = {}  # symbol_timeframe -> pending candle to add to buffer

        # Threading
        self.loop = None
        self.loop_thread = None
        
        self.logger.log_info("WebSocket Market Data Provider initialized")

    def get_candles(self, symbol: str, timespan: str = "minute",
                   multiplier: int = 1, count: int = 1000) -> Dict:
        """
        Get candles using GCS + WebSocket hybrid approach.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")
            timespan (str): Time interval
            multiplier (int): Multiplier (not used in current implementation)
            count (int): Number of candles to fetch

        Returns:
            Dict: Candle data response
        """
        try:
            self.logger.log_info(f"🔍 get_candles called with: symbol={symbol}, timespan={timespan}, count={count}")

            # Convert symbol format for consistency
            normalized_symbol = symbol.replace('/', '').replace('_', '')
            subscription_key = f"{normalized_symbol}_{timespan}"

            self.logger.log_info(f"🔑 Subscription key: {subscription_key}")

            # Check if we have real-time data in buffer
            if subscription_key in self.candle_buffer:
                buffer_candles = list(self.candle_buffer[subscription_key])
                if len(buffer_candles) >= count:
                    self.logger.log_info(f"Returning {len(buffer_candles)} candles from WebSocket buffer")
                    return {
                        "status": "success",
                        "candles": buffer_candles[-count:],
                        "source": "websocket_buffer"
                    }

            # First time or insufficient buffer data - get from GCS
            self.logger.log_info(f"Fetching initial data from GCS for {symbol} {timespan}")
            gcs_result = self._fetch_from_gcs(symbol, timespan, count)

            if gcs_result["status"] == "success":
                # Initialize buffer with GCS data
                candles = gcs_result["candles"]
                self.candle_buffer[subscription_key] = deque(candles, maxlen=self.buffer_size)

                # Add any pending candles that were built while buffer was being created
                if subscription_key in self.pending_candles:
                    pending_candle = self.pending_candles.pop(subscription_key)
                    self.candle_buffer[subscription_key].append(pending_candle)
                    self.logger.log_info(f"📦 Added pending candle to new buffer: {subscription_key}")

                # Handle timeframe-specific logic
                subscription_key = f"{normalized_symbol}_{timespan}"

                if timespan == "1m":
                    # For 1m timeframe, start WebSocket immediately (no building needed)
                    self.logger.log_info(f"✅ 1m timeframe - subscribing to WebSocket immediately")
                    self._start_websocket_subscription(normalized_symbol)
                else:
                    # For higher timeframes (5m, 15m, etc.), check if we need to build candles
                    if subscription_key not in self.candle_builders:
                        # Create GCS fetch callback for current candle fetching
                        gcs_callback = lambda symbol, tf, period_start: self._fetch_current_candle_from_gcs(symbol, tf, period_start)
                        self.candle_builders[subscription_key] = CandleBuilder(normalized_symbol, timespan, gcs_callback)
                        self.logger.log_info(f"🔧 Initialized CandleBuilder for {subscription_key}")

                    # Check if we're mid-period and handle accordingly
                    if self._is_mid_period(timespan):
                        self.logger.log_info(f"⚠️ Mid-period detected for {timespan} - will wait for completion before subscribing to WebSocket")
                        self._schedule_period_completion_handler(subscription_key, normalized_symbol, timespan)
                    else:
                        self.logger.log_info(f"✅ Starting at {timespan} period boundary - subscribing to WebSocket immediately")

                        # Pre-start the candle period so the first candle gets counted
                        builder = self.candle_builders[subscription_key]
                        builder.waiting_for_period_start = False  # Ready to accept candles
                        builder.period_pre_started = True  # Mark as pre-started
                        self.logger.log_info(f"🚀 Pre-started {timespan} period - ready to accept first candle")

                        self._start_websocket_subscription(normalized_symbol)

                self.logger.log_info(f"Initialized buffer with {len(candles)} candles from GCS")
                return {
                    "status": "success",
                    "candles": candles[-count:],
                    "source": "gcs_websocket_hybrid"
                }

            # If GCS fails, return the error (no Polygon fallback)
            return gcs_result

        except Exception as e:
            self.logger.log_error(f"Error in WebSocket get_candles: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }

    def _init_gcs_client(self):
        """Initialize GCS client and bucket."""
        try:
            self.gcs_client = storage.Client()
            self.bucket = self.gcs_client.bucket(self.bucket_name)
            self.logger.log_info(f"GCS client initialized for bucket: {self.bucket_name}")
        except Exception as e:
            self.logger.log_error(f"Failed to initialize GCS client: {str(e)}")
            self.gcs_client = None
            self.bucket = None

    def _fetch_from_gcs(self, symbol: str, timespan: str, count: int) -> Dict:
        """Fetch candle data from GCS."""
        try:
            if not self.bucket:
                return {
                    "status": "error",
                    "message": "GCS client not initialized"
                }

            # Convert symbol format for GCS path
            gcs_symbol = symbol.replace('/', '').replace('_', '')

            # Map timespan to GCS folder structure
            timespan_map = {
                "1m": "1m", "5m": "5m", "15m": "15m", "30m": "30m",
                "1h": "1h", "4h": "4h", "1d": "1d"
            }

            gcs_timespan = timespan_map.get(timespan, timespan)
            prefix = f"{gcs_symbol}/{gcs_timespan}/"

            self.logger.log_info(f"🔍 Searching for files in GCS: {prefix}")

            # Get all blobs with the prefix
            blobs = list(self.bucket.list_blobs(prefix=prefix))

            if not blobs:
                return {
                    "status": "error",
                    "message": f"No data files found in GCS for {symbol} {timespan}"
                }

            # Sort blobs by name (which includes date) to get chronological order
            blobs.sort(key=lambda x: x.name)

            # Take the most recent files to get enough candles
            relevant_files = blobs[-7:]  # Last 7 files should be enough for 1000+ candles

            self.logger.log_info(f"📄 Found {len(relevant_files)} relevant files")

            all_candles = []

            # Process files in chronological order
            for blob in relevant_files:
                try:
                    content = blob.download_as_text()

                    # Parse JSONL content
                    for line_num, line in enumerate(content.strip().split('\n'), 1):
                        if line.strip():
                            try:
                                candle_data = json.loads(line)

                                # Handle different data formats
                                if "timestamp" in candle_data:
                                    timestamp = int(candle_data["timestamp"])
                                elif "datetime" in candle_data:
                                    candle_datetime = dateutil.parser.parse(candle_data["datetime"])
                                    timestamp = int(candle_datetime.timestamp())
                                else:
                                    continue

                                # Convert to expected format
                                formatted_candle = {
                                    "time": timestamp,
                                    "open": float(candle_data["open"]),
                                    "high": float(candle_data["high"]),
                                    "low": float(candle_data["low"]),
                                    "close": float(candle_data["close"]),
                                    "volume": int(candle_data.get("volume", 0))
                                }
                                all_candles.append(formatted_candle)

                            except (json.JSONDecodeError, KeyError, ValueError) as e:
                                continue

                except Exception as e:
                    self.logger.log_error(f"Error processing file {blob.name}: {str(e)}")
                    continue

            if not all_candles:
                return {
                    "status": "error",
                    "message": "No valid candle data found in GCS files"
                }

            # Sort by timestamp and take the most recent candles
            all_candles.sort(key=lambda x: x["time"])
            recent_candles = all_candles[-count:] if len(all_candles) > count else all_candles

            self.logger.log_info(f"✅ Successfully parsed {len(recent_candles)} candles from GCS")

            return {
                "status": "success",
                "candles": recent_candles,
                "source": "gcs"
            }

        except Exception as e:
            self.logger.log_error(f"Error fetching from GCS: {str(e)}")
            return {
                "status": "error",
                "message": f"GCS fetch error: {str(e)}"
            }

    def _ensure_websocket_connection(self):
        """Ensure WebSocket connection is established."""
        self.logger.log_info(f"🔌 Checking WebSocket connection - Connected: {self.is_connected}, "
                           f"Should reconnect: {self.should_reconnect}, "
                           f"Thread alive: {self.loop_thread.is_alive() if self.loop_thread else False}")

        if not self.is_connected and self.should_reconnect:
            if not self.loop_thread or not self.loop_thread.is_alive():
                self.logger.log_info("🚀 Starting WebSocket connection...")
                self._start_websocket_thread()
            else:
                self.logger.log_info("⏳ WebSocket thread already running, waiting for connection...")
        elif self.is_connected:
            self.logger.log_info("✅ WebSocket already connected")
        else:
            self.logger.log_info("❌ WebSocket connection disabled (should_reconnect=False)")

    def _start_websocket_thread(self):
        """Start the WebSocket connection in a separate thread."""
        def run_websocket():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_until_complete(self._websocket_handler())
        
        self.loop_thread = threading.Thread(target=run_websocket, daemon=True)
        self.loop_thread.start()
        self.logger.log_info("Started WebSocket thread")

    async def _websocket_handler(self):
        """Handle WebSocket connection and messages."""
        while self.should_reconnect:
            try:
                self.logger.log_info(f"Connecting to WebSocket: {self.websocket_url}")
                
                async with websockets.connect(self.websocket_url) as websocket:
                    self.ws = websocket
                    self.is_connected = True
                    self.logger.log_info("✅ WebSocket connected")
                    
                    # Re-subscribe to all active subscriptions
                    for subscription in self.subscriptions.copy():
                        await self._send_subscription(subscription)
                    
                    # Listen for messages
                    async for message in websocket:
                        await self._handle_websocket_message(message)
                        
            except Exception as e:
                self.logger.log_error(f"WebSocket connection error: {str(e)}")
                self.is_connected = False
                self.ws = None
                
                if self.should_reconnect:
                    self.logger.log_info("Reconnecting in 5 seconds...")
                    await asyncio.sleep(5)

    async def _handle_websocket_message(self, message: str):
        """Handle incoming WebSocket messages."""
        try:
            self.logger.log_info(f"📥 Raw WebSocket message: {message}")
            data = json.loads(message)
            message_type = data.get('type')

            self.logger.log_info(f"📨 Parsed message type: {message_type}")

            if message_type == 'connection':
                self.logger.log_info(f"✅ WebSocket connection confirmed: {data.get('message')}")

            elif message_type == 'subscription_confirmed':
                forex_pair = data.get('forex_pair')
                timeframe = data.get('timeframe')
                self.logger.log_info(f"✅ Subscription confirmed: {forex_pair} {timeframe}")

            elif message_type == 'tick':
                # Trade-bot doesn't need tick data - log but ignore
                self.logger.log_info(f"📊 Received tick data (ignoring): {data.get('data', {}).get('symbol')} - {data.get('data', {}).get('price')}")

            elif message_type == 'candle_update':
                self.logger.log_info(f"🕯️ Received candle_update message")
                await self._handle_candle_update(data)

            else:
                self.logger.log_info(f"❓ Unknown message type '{message_type}': {data}")

        except Exception as e:
            self.logger.log_error(f"Error handling WebSocket message: {str(e)}")

    async def _handle_candle_update(self, candle_data: Dict):
        """Handle 1m candle updates and build higher timeframes."""
        try:
            self.logger.log_info(f"🔄 Received candle update: {candle_data}")

            data = candle_data.get('data', {})
            symbol = data.get('symbol')
            timeframe = data.get('timeframe')

            if not symbol or not timeframe:
                self.logger.log_warning(f"Missing symbol or timeframe in candle data: {candle_data}")
                return

            # This should always be a 1m candle from WebSocket service
            if timeframe != "1m":
                self.logger.log_warning(f"Expected 1m candle but received {timeframe}")
                return

            # Create 1m candle object
            candle_1m = {
                'time': data.get('time'),
                'open': data.get('open'),
                'high': data.get('high'),
                'low': data.get('low'),
                'close': data.get('close'),
                'volume': data.get('volume', 0)
            }

            self.logger.log_info(f"📈 Processing 1m candle: {symbol} OHLC={candle_1m['open']}/{candle_1m['high']}/{candle_1m['low']}/{candle_1m['close']}")

            # Check if this is for a 1m timeframe subscription (direct buffer addition)
            subscription_key_1m = f"{symbol}_1m"
            if subscription_key_1m in self.candle_buffer:
                # Direct addition for 1m timeframes - no building needed
                old_buffer_size = len(self.candle_buffer[subscription_key_1m])
                self.candle_buffer[subscription_key_1m].append(candle_1m)
                new_buffer_size = len(self.candle_buffer[subscription_key_1m])

                self.logger.log_info(f"📊 Added 1m candle directly to buffer: {symbol}, "
                                   f"buffer size: {old_buffer_size} -> {new_buffer_size}, "
                                   f"candle close: {candle_1m['close']}")

                if old_buffer_size >= self.buffer_size:
                    self.logger.log_info(f"🔄 FIFO: Removed oldest 1m candle, maintaining {self.buffer_size} candles")
            else:
                # Store as pending candle for 1m timeframe if buffer doesn't exist yet
                if subscription_key_1m not in self.pending_candles:
                    self.pending_candles[subscription_key_1m] = candle_1m
                    self.logger.log_info(f"📦 Stored pending 1m candle for {subscription_key_1m}")

            # Process through all CandleBuilders for this symbol (for higher timeframes)
            for subscription_key, builder in self.candle_builders.items():
                if subscription_key.startswith(symbol + "_"):
                    completed_candle = builder.add_1m_candle(candle_1m)

                    if completed_candle:
                        # A higher timeframe candle is complete
                        target_timeframe = subscription_key.split('_', 1)[1]
                        self.logger.log_info(f"✅ Completed {target_timeframe} candle for {symbol}")

                        # Add to buffer if we have one for this timeframe
                        if subscription_key in self.candle_buffer:
                            old_buffer_size = len(self.candle_buffer[subscription_key])
                            self.candle_buffer[subscription_key].append(completed_candle)
                            new_buffer_size = len(self.candle_buffer[subscription_key])

                            self.logger.log_info(f"📊 Added new candle to buffer: {symbol} {target_timeframe}, "
                                               f"buffer size: {old_buffer_size} -> {new_buffer_size}, "
                                               f"candle close: {completed_candle['close']}")

                            if old_buffer_size >= self.buffer_size:
                                self.logger.log_info(f"🔄 FIFO: Removed oldest candle, maintaining {self.buffer_size} candles")
                        else:
                            # Store pending candle to add when buffer is created
                            self.pending_candles[subscription_key] = completed_candle
                            self.logger.log_info(f"📦 Stored pending candle for {subscription_key}")

        except Exception as e:
            self.logger.log_error(f"Error handling candle update: {str(e)}")

    def _fetch_current_candle_from_gcs(self, symbol: str, timeframe: str, period_start) -> Dict:
        """Fetch the current incomplete candle from GCS for the given period."""
        try:
            from datetime import datetime, timezone

            self.logger.log_info(f"🔄 Fetching current {timeframe} candle from GCS for {symbol} at {period_start}")

            # For now, return None - this would need to be implemented based on your GCS structure
            # The implementation would:
            # 1. Calculate the expected filename for the current period
            # 2. Fetch the latest data from GCS for that timeframe
            # 3. Build the current candle from available 1m data
            # 4. Return the candle in the expected format

            self.logger.log_warning(f"⚠️ GCS current candle fetch not implemented yet")
            return None

        except Exception as e:
            self.logger.log_error(f"Error fetching current candle from GCS: {e}")
            return None

    def _fetch_and_add_current_candle_to_buffer(self, subscription_key: str, symbol: str, timeframe: str):
        """Fetch current candle from GCS and add to buffer to fill the gap."""
        try:
            from datetime import datetime, timezone

            if subscription_key not in self.candle_buffer:
                self.logger.log_warning(f"No buffer found for {subscription_key}")
                return

            builder = self.candle_builders.get(subscription_key)
            if not builder:
                self.logger.log_warning(f"No CandleBuilder found for {subscription_key}")
                return

            # Calculate current period start based on timeframe
            now = datetime.now(timezone.utc)

            # Get period start for current time
            if timeframe == "5m":
                minute = (now.minute // 5) * 5
                period_start = now.replace(minute=minute, second=0, microsecond=0)
            elif timeframe == "15m":
                minute = (now.minute // 15) * 15
                period_start = now.replace(minute=minute, second=0, microsecond=0)
            elif timeframe == "30m":
                minute = (now.minute // 30) * 30
                period_start = now.replace(minute=minute, second=0, microsecond=0)
            elif timeframe == "1h":
                period_start = now.replace(minute=0, second=0, microsecond=0)
            elif timeframe == "4h":
                hour = (now.hour // 4) * 4
                period_start = now.replace(hour=hour, minute=0, second=0, microsecond=0)
            elif timeframe == "1d":
                period_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                self.logger.log_warning(f"Unknown timeframe: {timeframe}")
                return

            # Fetch current candle from GCS
            current_candle = builder.get_current_gcs_candle_for_buffer(period_start)

            if current_candle:
                # Add to buffer
                old_buffer_size = len(self.candle_buffer[subscription_key])
                self.candle_buffer[subscription_key].append(current_candle)
                new_buffer_size = len(self.candle_buffer[subscription_key])

                self.logger.log_info(f"📦 Added current GCS candle to buffer: {symbol} {timeframe}, "
                                   f"buffer size: {old_buffer_size} -> {new_buffer_size}")
            else:
                self.logger.log_info(f"⚠️ No current candle available from GCS for {symbol} {timeframe}")

        except Exception as e:
            self.logger.log_error(f"Error fetching current candle for buffer: {e}")

    def _is_mid_period(self, timeframe: str) -> bool:
        """Check if we're currently mid-period for the given timeframe."""
        try:
            from datetime import datetime, timezone

            now = datetime.now(timezone.utc)

            if timeframe == "5m":
                return now.minute % 5 != 0 or now.second != 0
            elif timeframe == "15m":
                return now.minute % 15 != 0 or now.second != 0
            elif timeframe == "30m":
                return now.minute % 30 != 0 or now.second != 0
            elif timeframe == "1h":
                return now.minute != 0 or now.second != 0
            elif timeframe == "4h":
                return now.hour % 4 != 0 or now.minute != 0 or now.second != 0
            elif timeframe == "1d":
                return now.hour != 0 or now.minute != 0 or now.second != 0
            else:
                return False  # Unknown timeframe, assume not mid-period

        except Exception as e:
            self.logger.log_error(f"Error checking mid-period: {e}")
            return False

    def _schedule_period_completion_handler(self, subscription_key: str, symbol: str, timeframe: str):
        """Schedule a handler to wait for period completion and then start WebSocket."""
        try:
            import threading
            from datetime import datetime, timezone, timedelta

            def wait_and_start():
                try:
                    # Calculate when the current period will end
                    now = datetime.now(timezone.utc)

                    if timeframe == "5m":
                        next_period = now.replace(second=0, microsecond=0)
                        next_period = next_period.replace(minute=(next_period.minute // 5 + 1) * 5)
                    elif timeframe == "15m":
                        next_period = now.replace(second=0, microsecond=0)
                        next_period = next_period.replace(minute=(next_period.minute // 15 + 1) * 15)
                    elif timeframe == "30m":
                        next_period = now.replace(second=0, microsecond=0)
                        next_period = next_period.replace(minute=(next_period.minute // 30 + 1) * 30)
                    elif timeframe == "1h":
                        next_period = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                    elif timeframe == "4h":
                        next_period = now.replace(minute=0, second=0, microsecond=0)
                        next_period = next_period.replace(hour=(next_period.hour // 4 + 1) * 4)
                    elif timeframe == "1d":
                        next_period = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                    else:
                        self.logger.log_error(f"Unknown timeframe: {timeframe}")
                        return

                    # Add 10 second buffer for GCS data availability
                    wait_until = next_period + timedelta(seconds=10)
                    wait_seconds = (wait_until - now).total_seconds()

                    self.logger.log_info(f"⏳ Waiting {wait_seconds:.1f} seconds for {timeframe} period to complete + 10s buffer")
                    self.logger.log_info(f"⏳ Current period ends at: {next_period}, will start WebSocket at: {wait_until}")

                    if wait_seconds > 0:
                        import time
                        time.sleep(wait_seconds)

                    # Fetch the completed candle from GCS
                    self.logger.log_info(f"🔄 Fetching completed {timeframe} candle from GCS")
                    completed_candle = self._fetch_current_candle_from_gcs(symbol, timeframe, next_period - timedelta(minutes=self._get_timeframe_minutes(timeframe)))

                    if completed_candle and subscription_key in self.candle_buffer:
                        # Add completed candle to buffer
                        old_size = len(self.candle_buffer[subscription_key])
                        self.candle_buffer[subscription_key].append(completed_candle)
                        new_size = len(self.candle_buffer[subscription_key])
                        self.logger.log_info(f"📦 Added completed {timeframe} candle to buffer: {symbol}, size: {old_size} -> {new_size}")

                    # Now start WebSocket subscription
                    self.logger.log_info(f"📡 Starting WebSocket subscription for {symbol} after period completion")

                    # Pre-start the candle period so the first candle gets counted
                    if subscription_key in self.candle_builders:
                        builder = self.candle_builders[subscription_key]
                        builder.waiting_for_period_start = False  # Ready to accept candles
                        builder.period_pre_started = True  # Mark as pre-started
                        self.logger.log_info(f"🚀 Pre-started {timeframe} period - ready to accept first candle")

                    self._start_websocket_subscription(symbol)

                except Exception as e:
                    self.logger.log_error(f"Error in period completion handler: {e}")

            # Start the waiting thread
            wait_thread = threading.Thread(target=wait_and_start, daemon=True)
            wait_thread.start()

        except Exception as e:
            self.logger.log_error(f"Error scheduling period completion handler: {e}")

    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe to minutes."""
        timeframe_map = {
            "1m": 1, "5m": 5, "15m": 15, "30m": 30,
            "1h": 60, "4h": 240, "1d": 1440
        }
        return timeframe_map.get(timeframe, 1)

    def _start_websocket_subscription(self, symbol: str):
        """Start WebSocket connection and subscribe to 1m candles."""
        self._ensure_websocket_connection()
        self._subscribe_to_updates(symbol, "1m")

    def _subscribe_to_updates(self, symbol: str, timeframe: str):
        """Subscribe to real-time updates for a symbol/timeframe."""
        subscription_key = f"{symbol}_{timeframe}"

        self.logger.log_info(f"🔔 Attempting to subscribe to: {subscription_key}")

        if subscription_key not in self.subscriptions:
            self.subscriptions.add(subscription_key)
            self.logger.log_info(f"✅ Added subscription: {subscription_key}")

            # Send subscription if connected
            if self.is_connected and self.loop:
                self.logger.log_info(f"📡 Sending subscription message for: {subscription_key}")
                asyncio.run_coroutine_threadsafe(
                    self._send_subscription(subscription_key),
                    self.loop
                )
            else:
                self.logger.log_warning(f"⚠️ Cannot send subscription - WebSocket not connected. "
                                      f"Connected: {self.is_connected}, Loop: {self.loop is not None}")
        else:
            self.logger.log_info(f"📋 Already subscribed to: {subscription_key}")

    async def _send_subscription(self, subscription_key: str):
        """Send subscription message to WebSocket."""
        try:
            if self.ws:
                symbol, timeframe = subscription_key.split('_', 1)
                subscription_message = {
                    'type': 'subscribe',
                    'forex_pair': symbol,
                    'timeframe': timeframe,
                    'client_type': 'trade_bot'  # Identify as trade-bot client
                }

                self.logger.log_info(f"📤 Sending subscription message: {json.dumps(subscription_message)}")
                await self.ws.send(json.dumps(subscription_message))
                self.logger.log_info(f"📡 Subscription sent for: {symbol} {timeframe}")

        except Exception as e:
            self.logger.log_error(f"Error sending subscription: {str(e)}")

    def get_quote(self, symbol: str) -> Dict:
        """
        Override to use candle close price instead of Polygon quotes API.
        Trade-bot uses candle close prices for trading decisions.
        """
        from datetime import datetime, timezone

        try:
            # Get the latest candle close price from our buffer
            normalized_symbol = symbol.replace('/', '').replace('_', '')

            # Look for any timeframe data for this symbol
            latest_close = None
            latest_timestamp = None

            for subscription_key, candle_buffer in self.candle_buffer.items():
                if subscription_key.startswith(normalized_symbol) and candle_buffer:
                    latest_candle = candle_buffer[-1]
                    latest_close = latest_candle.get('close')
                    latest_timestamp = latest_candle.get('time')
                    break

            if latest_close is not None:
                # Use close price as both bid and ask (no spread for trade-bot)
                return {
                    "symbol": symbol,
                    "bid": latest_close,
                    "ask": latest_close,
                    "spread": 0.0,
                    "timestamp": datetime.fromtimestamp(latest_timestamp, tz=timezone.utc).isoformat() if latest_timestamp else datetime.now(timezone.utc).isoformat(),
                    "source": "candle_close_price",
                    "note": "Using latest candle close price"
                }
            else:
                # Fallback if no candle data available
                return {
                    "symbol": symbol,
                    "bid": 1.0,
                    "ask": 1.0,
                    "spread": 0.0,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "source": "fallback_default",
                    "note": "No candle data available, using default values"
                }

        except Exception as e:
            self.logger.log_error(f"Error getting quote from candle data: {str(e)}")
            # Return fallback quote
            return {
                "symbol": symbol,
                "bid": 1.0,
                "ask": 1.0,
                "spread": 0.0,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "error_fallback",
                "error": str(e)
            }

    def cleanup(self):
        """Clean up WebSocket connections."""
        self.should_reconnect = False
        self.is_connected = False

        if self.ws:
            asyncio.run_coroutine_threadsafe(self.ws.close(), self.loop)

        self.logger.log_info("WebSocket Market Data Provider cleaned up")
