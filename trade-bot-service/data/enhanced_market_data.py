import os
import logging
import requests
from typing import Dict, List, Optional
from datetime import datetime, timezone, timedelta
from google.cloud import storage
import json
import dateutil.parser
from utils.logger import Logger
from utils.retry_utils import with_retry, RetryableError
from utils.error_messages import get_user_friendly_error_message
from utils.market_conditions import MarketConditions
from data.market_data import MarketDataProvider
import numpy as np

MIN_CANDLE_COUNT = 1000

class EnhancedMarketDataProvider(MarketDataProvider):
    """Enhanced market data provider that uses GCS for historical data and Polygon API for real-time updates."""

    def __init__(self, api_key: str):
        """
        Initialize the enhanced market data provider.

        Args:
            api_key (str): Polygon.io API key
        """
        # Initialize parent class
        super().__init__(api_key)
        
        # Override logger name
        self.logger = Logger("EnhancedMarketDataProvider")
        
        # GCS configuration
        self.bucket_name = os.getenv('HISTORICAL_DATA_BUCKET', 'oryntrade-forex-candles-data')
        self.gcs_client = None
        self.bucket = None
        
        # Data cache
        self.candle_cache = {}  # symbol_timeframe -> candles
        self.cache_expiry = {}  # symbol_timeframe -> expiry_time
        self.cache_ttl = 300  # 5 minutes cache for candle data
        
        # Initialize GCS client
        self._init_gcs_client()
        
        self.logger.log_info("Enhanced Market Data Provider initialized")

    def _init_gcs_client(self):
        """Initialize Google Cloud Storage client."""
        try:
            self.gcs_client = storage.Client()
            self.bucket = self.gcs_client.bucket(self.bucket_name)
            self.logger.log_info("✅ GCS client initialized successfully")
        except Exception as e:
            self.logger.log_error(f"Failed to initialize GCS client: {str(e)}")
            self.gcs_client = None
            self.bucket = None

    def get_candles(self, symbol: str, timespan: str = "minute",
                   multiplier: int = 1, count: int = MIN_CANDLE_COUNT) -> Dict:
        """
        Fetch candle data using GCS + Polygon hybrid approach.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")
            timespan (str): Time interval (e.g., "1m", "5m", "15m", "30m", "1h", "4h", "1d")
            multiplier (int): Number of timespans to multiply by
            count (int): Number of candles to fetch

        Returns:
            Dict: Contains either:
                - status: "success" and candles: List[Dict] if successful
                - status: "error" and message: str if there's an error
        """
        import time
        start_time = time.time()

        try:
            self.logger.log_info(f"Fetching {count} candles for {symbol} {timespan}")
            
            # Create cache key
            cache_key = f"{symbol}_{timespan}"
            
            # Check cache first
            if self._is_cache_valid(cache_key):
                self.logger.log_info(f"Using cached data for {symbol} {timespan}")
                cached_candles = self.candle_cache[cache_key]
                
                # Return the requested number of candles (most recent)
                if len(cached_candles) >= count:
                    return {
                        "status": "success",
                        "candles": cached_candles[-count:],
                        "source": "cache"
                    }
            
            # Try to load from GCS first
            gcs_candles = self._fetch_from_gcs(symbol, timespan, count)
            
            if gcs_candles and len(gcs_candles) >= count:
                self.logger.log_info(f"Fetched {len(gcs_candles)} candles from GCS")
                
                # Cache the data
                self._cache_candles(cache_key, gcs_candles)
                
                # Track execution time
                self._track_execution_time('get_candles_gcs', start_time)
                
                return {
                    "status": "success",
                    "candles": gcs_candles[-count:],
                    "source": "gcs"
                }
            
            # Fallback to Polygon API
            self.logger.log_warning("GCS data not available, falling back to Polygon API")
            polygon_result = super().get_candles(symbol, timespan, multiplier, count)
            
            if polygon_result["status"] == "success":
                # Cache the Polygon data
                self._cache_candles(cache_key, polygon_result["candles"])
                polygon_result["source"] = "polygon_fallback"
            
            # Track execution time
            self._track_execution_time('get_candles_polygon_fallback', start_time)
            
            return polygon_result

        except Exception as e:
            self.logger.log_error(f"Error in enhanced get_candles: {str(e)}")
            
            # Fallback to parent implementation
            return super().get_candles(symbol, timespan, multiplier, count)

    def _fetch_from_gcs(self, symbol: str, timespan: str, count: int) -> Optional[List[Dict]]:
        """
        Fetch candle data from Google Cloud Storage.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")
            timespan (str): Time interval
            count (int): Number of candles needed

        Returns:
            Optional[List[Dict]]: List of candles or None if not available
        """
        if not self.gcs_client or not self.bucket:
            self.logger.log_warning("GCS client not available")
            return None

        try:
            self.logger.log_info(f"Loading historical data from GCS for {symbol} {timespan}")
            
            # Convert symbol format for GCS (EUR/USD -> EURUSD)
            gcs_symbol = symbol.replace('/', '').replace('_', '')
            
            # Calculate date range (get extra days to ensure we have enough candles)
            end_date = datetime.now(timezone.utc).date()
            
            # Estimate days needed based on timeframe
            timeframe_to_days = {
                "1m": 3,    # 3 days for 1-minute data
                "5m": 7,    # 1 week for 5-minute data  
                "15m": 14,  # 2 weeks for 15-minute data
                "30m": 21,  # 3 weeks for 30-minute data
                "1h": 45,   # 1.5 months for hourly data
                "4h": 180,  # 6 months for 4-hour data
                "1d": 365   # 1 year for daily data
            }
            
            days_needed = timeframe_to_days.get(timespan, 30)
            start_date = end_date - timedelta(days=days_needed)
            
            self.logger.log_info(f"Fetching GCS data from {start_date} to {end_date}")
            
            # Fetch candles from GCS
            candles = self._fetch_candles_from_gcs_storage(
                gcs_symbol, timespan, start_date, end_date
            )
            
            self.logger.log_info(f"Fetched {len(candles)} candles from GCS")
            
            if len(candles) >= count:
                return candles
            else:
                self.logger.log_warning(f"Not enough candles from GCS: {len(candles)} < {count}")
                return None

        except Exception as e:
            self.logger.log_error(f"Error fetching candles from GCS storage: {str(e)}")
            return None

    def _fetch_candles_from_gcs_storage(self, forex_pair: str, timeframe: str, 
                                       start_date, end_date) -> List[Dict]:
        """
        Fetch candles from GCS storage for the specified date range.
        
        Returns candles in format compatible with trading engine:
        [
            {
                "time": unix_timestamp,
                "open": float,
                "high": float,
                "low": float,
                "close": float,
                "volume": int
            }
        ]
        """
        try:
            self.logger.log_info(f"🔍 Searching for files in GCS: {forex_pair}/{timeframe}/")
            
            # List all files in the forex pair/timeframe folder
            folder_prefix = f"{forex_pair}/{timeframe}/"
            blobs = list(self.bucket.list_blobs(prefix=folder_prefix))
            
            if not blobs:
                self.logger.log_warning(f"⚠️ No files found in {folder_prefix}")
                return []
            
            # Filter files by date range
            relevant_files = []
            for blob in blobs:
                try:
                    # Extract date from filename: EURUSD_5m_2024-01-15.jsonl
                    filename = blob.name.split('/')[-1]
                    if not filename.endswith('.jsonl'):
                        continue
                        
                    # Extract date from filename
                    date_str = filename.split('_')[-1].replace('.jsonl', '')
                    file_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                    
                    if start_date <= file_date <= end_date:
                        relevant_files.append((blob, file_date))
                        
                except Exception as e:
                    self.logger.log_warning(f"Error parsing filename {blob.name}: {str(e)}")
                    continue
            
            if not relevant_files:
                self.logger.log_warning(f"⚠️ No relevant files found for date range {start_date} to {end_date}")
                return []
            
            # Sort files by date
            relevant_files.sort(key=lambda x: x[1])
            self.logger.log_info(f"📄 Found {len(relevant_files)} relevant files")
            
            # Read and parse all files
            all_candles = []
            for blob, file_date in relevant_files:
                try:
                    content = blob.download_as_text()
                    
                    # Parse JSONL content
                    for line_num, line in enumerate(content.strip().split('\n'), 1):
                        if line.strip():
                            try:
                                candle_data = json.loads(line)

                                # Handle different data formats (same logic as Cloud Functions)
                                if "timestamp" in candle_data:
                                    # New format with timestamp field
                                    timestamp = int(candle_data["timestamp"])
                                elif "datetime" in candle_data:
                                    # Old format with datetime field - convert to timestamp
                                    # Parse the datetime string
                                    candle_datetime = dateutil.parser.parse(candle_data["datetime"])
                                    timestamp = int(candle_datetime.timestamp())

                                    # Debug: Log conversion for first few candles
                                    if line_num <= 3:
                                        self.logger.log_info(f"🔍 Debug candle {line_num}: datetime='{candle_data['datetime']}' -> parsed={candle_datetime.isoformat()} -> timestamp={timestamp}")
                                else:
                                    self.logger.log_warning(f"⚠️ No timestamp or datetime field in line {line_num} of {blob.name}")
                                    continue

                                # Convert to expected format
                                formatted_candle = {
                                    "time": timestamp,
                                    "open": float(candle_data["open"]),
                                    "high": float(candle_data["high"]),
                                    "low": float(candle_data["low"]),
                                    "close": float(candle_data["close"]),
                                    "volume": int(candle_data.get("volume", 0))
                                }
                                all_candles.append(formatted_candle)

                            except (json.JSONDecodeError, KeyError, ValueError) as e:
                                self.logger.log_warning(f"⚠️ Error parsing line {line_num} in {blob.name}: {str(e)}")
                                continue
                            
                except Exception as e:
                    self.logger.log_error(f"Error processing file {blob.name}: {str(e)}")
                    continue
            
            # Sort candles by timestamp
            all_candles.sort(key=lambda x: x["time"])
            
            # Remove duplicates (keep the latest one for each timestamp)
            unique_candles = {}
            for candle in all_candles:
                unique_candles[candle["time"]] = candle
            
            final_candles = list(unique_candles.values())
            final_candles.sort(key=lambda x: x["time"])
            
            self.logger.log_info(f"✅ Successfully parsed {len(final_candles)} candles from GCS")
            return final_candles
            
        except Exception as e:
            self.logger.log_error(f"Error fetching candles from GCS: {str(e)}")
            return []

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self.candle_cache:
            return False
            
        if cache_key not in self.cache_expiry:
            return False
            
        return datetime.now(timezone.utc) < self.cache_expiry[cache_key]

    def _cache_candles(self, cache_key: str, candles: List[Dict]):
        """Cache candle data with expiry."""
        self.candle_cache[cache_key] = candles
        self.cache_expiry[cache_key] = datetime.now(timezone.utc) + timedelta(seconds=self.cache_ttl)

    def cleanup(self):
        """Clean up resources."""
        try:
            self.logger.log_info("Enhanced Market Data Provider cleaned up")
        except Exception as e:
            self.logger.log_error(f"Error during cleanup: {str(e)}")
