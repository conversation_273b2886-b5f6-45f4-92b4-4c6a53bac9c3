import os
import logging
import requests
from typing import Dict, List, Optional
from datetime import datetime, timezone, timedelta
from utils.logger import Logger
from utils.retry_utils import with_retry, RetryableError
from utils.error_messages import get_user_friendly_error_message
from utils.market_conditions import MarketConditions
import numpy as np

MIN_CANDLE_COUNT = 1000

class MarketDataProvider:
    """Handles fetching and processing market data from Polygon.io."""

    def __init__(self, api_key: str):
        """
        Initialize the market data provider.

        Args:
            api_key (str): Polygon.io API key
        """
        if not api_key:
            raise ValueError("POLYGON_API_KEY environment variable is not set")

        self.api_key = api_key
        self.base_url = "https://api.polygon.io/v2"
        self.logger = Logger("MarketDataProvider")

        # Initialize market conditions handler
        self.market_conditions = MarketConditions(api_key)

        # Cache for instrument quotes
        self.quotes_cache = {}
        self.quotes_cache_expiry = {}
        self.quotes_cache_ttl = 60  # seconds

        # Performance metrics
        self.metrics = {
            'api_calls': 0,
            'execution_times': {}
        }

        self.logger.log_info("MarketDataProvider initialized with API key")

    def _track_execution_time(self, operation: str, start_time: float) -> None:
        """
        Track execution time for an operation.

        Args:
            operation (str): Operation name
            start_time (float): Start time from time.time()
        """
        import time
        execution_time = time.time() - start_time

        if operation not in self.metrics['execution_times']:
            self.metrics['execution_times'][operation] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0
            }

        metrics = self.metrics['execution_times'][operation]
        metrics['count'] += 1
        metrics['total_time'] += execution_time
        metrics['min_time'] = min(metrics['min_time'], execution_time)
        metrics['max_time'] = max(metrics['max_time'], execution_time)

    def get_performance_metrics(self) -> Dict:
        """
        Get performance metrics for the market data provider.

        Returns:
            Dict: Performance metrics
        """
        metrics = self.metrics.copy()

        # Calculate average execution times
        for operation, data in metrics['execution_times'].items():
            if data['count'] > 0:
                data['avg_time'] = data['total_time'] / data['count']

        return metrics

    def _normalize_timespan(self, timespan: str) -> str:
        """
        Convert common timeframe notations to Polygon's expected format.

        Args:
            timespan (str): Input timeframe notation (e.g., "1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w", "1mo", "1q", "1y")

        Returns:
            str: Normalized timespan for Polygon API
        """
        # Remove any numbers from the start
        timespan = ''.join([i for i in timespan if not i.isdigit()]).lower()

        # Map common notations to Polygon format
        timespan_map = {
            "m": "minute",
            "h": "hour",
            "d": "day",
            "w": "week",
            "mo": "month",
            "q": "quarter",
            "y": "year"
        }

        normalized = timespan_map.get(timespan, timespan)
        if normalized not in ["minute", "hour", "day", "week", "month", "quarter", "year"]:
            self.logger.log_error(f"Invalid timespan: {timespan}")
            raise ValueError(f"Invalid timespan: {timespan}")

        return normalized

    def _get_timespan_seconds(self, timespan: str, multiplier: int = 1) -> int:
        """
        Convert timespan and multiplier to seconds.

        Args:
            timespan (str): Time interval (e.g., "minute", "hour", "day")
            multiplier (int): Number of timespans

        Returns:
            int: Total seconds
        """
        timespan_seconds = {
            "minute": 60,
            "hour": 3600,
            "day": 86400,
            "week": 604800,
            "month": 2592000,  # 30 days
            "quarter": 7776000,  # 90 days
            "year": 31536000  # 365 days
        }

        base_seconds = timespan_seconds.get(timespan, 60)  # Default to 60 seconds if unknown
        return base_seconds * multiplier

    def get_candles(self, symbol: str, timespan: str = "minute",
                   multiplier: int = 1, count: int = MIN_CANDLE_COUNT) -> Dict:
        """
        Fetch candle data from Polygon.io.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")
            timespan (str): Time interval (e.g., "1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w", "1mo", "1q", "1y")
            multiplier (int): Number of timespans to multiply by (will be overridden if specified in timespan)
            count (int): Number of candles to fetch

        Returns:
            Dict: Contains either:
                - status: "success" and candles: List[Dict] if successful
                - status: "error" and message: str if there's an error
                - status: "market_data_error" and message: str if there's a market data provider error
        """
        import time
        perf_start_time = time.time()  # Renamed to avoid conflict

        try:
            # Always fetch fresh candle data
            self.metrics['api_calls'] += 1
            self.logger.log_info(f"Fetching candles for {symbol} with timespan {timespan}, multiplier {multiplier}, count {count}")

            # Normalize timespan to Polygon format
            normalized_timespan = self._normalize_timespan(timespan)

            # Extract multiplier from timespan if present (e.g., "4h" -> multiplier=4)
            if any(c.isdigit() for c in timespan):
                try:
                    extracted_multiplier = int(''.join(filter(str.isdigit, timespan)))
                    multiplier = extracted_multiplier
                    self.logger.log_info(f"Extracted multiplier {multiplier} from timespan {timespan}")
                except ValueError:
                    self.logger.log_warning(f"Could not extract multiplier from timespan {timespan}, using provided value {multiplier}")

            # Calculate time range
            end_time = datetime.now(timezone.utc)
            # Check if we should use historical data (for weekend development)
            if os.getenv("BYPASS_MARKET_IS_CLOSED", "false").lower() == "true":
                self.logger.log_info("LOCAL DEVELOPMENT DETECTED: Using historical candle data for development")
                end_time = datetime.now(timezone.utc) - timedelta(days=3)
            total_seconds = count * self._get_timespan_seconds(normalized_timespan, multiplier)
            start_time = end_time - timedelta(seconds=total_seconds)

            # Convert to milliseconds for Polygon API
            from_timestamp = int(start_time.timestamp() * 1000)
            to_timestamp = int(end_time.timestamp() * 1000)

            # Prepare symbol for Polygon (add C: prefix for forex and remove all non-alphanumeric chars)
            polygon_symbol = f"C:{symbol.replace('/', '').replace('_', '')}"
            self.logger.log_info(f"Formatted symbol: {polygon_symbol}")

            url = f"{self.base_url}/aggs/ticker/{polygon_symbol}/range/{multiplier}/{normalized_timespan}/{from_timestamp}/{to_timestamp}"
            params = {"apiKey": self.api_key}

            # Log the full URL
            self.logger.log_info(f"Request URL: {url}")

            # Use the retry mechanism to make the API call
            response = self._make_polygon_request(url, params)

            data = response.json()
            self.logger.log_info(f"Response data status: {data.get('status')}")

            if data.get("status") != "OK":
                technical_error = f"Polygon API error: {data.get('error')}"
                self.logger.log_error(technical_error)
                self.logger.log_error(f"Full error response: {data}")

                # Create user-friendly error message
                user_message = get_user_friendly_error_message(technical_error)

                return {
                    "status": "market_data_error",
                    "message": user_message,
                    "technical_details": technical_error
                }

            candles = data.get("results", [])
            self.logger.log_info(f"Fetched {len(candles)} candles from Polygon for {symbol}")

            if not candles:
                technical_error = f"No candles returned for {symbol} in the specified time range"
                self.logger.log_warning(technical_error)
                self.logger.log_warning(f"Request parameters: timespan={timespan}, multiplier={multiplier}, count={count}")

                # Create user-friendly error message
                user_message = get_user_friendly_error_message(technical_error)

                return {
                    "status": "market_data_error",
                    "message": user_message,
                    "technical_details": technical_error
                }

            # Format candles for internal use
            formatted_candles = [{
                "time": int(candle["t"] / 1000),  # Convert to seconds
                "open": float(candle["o"]),
                "high": float(candle["h"]),
                "low": float(candle["l"]),
                "close": float(candle["c"]),
                "volume": int(candle["v"])
            } for candle in candles]

            self.logger.log_info(f"Formatted {len(formatted_candles)} candles")

            # Create response
            response = {
                "status": "success",
                "candles": formatted_candles
            }

            # Track execution time
            self._track_execution_time('get_candles', perf_start_time)

            return response

        except Exception as e:
            technical_error = f"Error fetching data from Polygon: {e}"
            self.logger.log_error(technical_error)
            self.logger.log_error(f"Symbol: {symbol}, Timespan: {timespan}, Multiplier: {multiplier}")
            self.logger.log_error(f"Error type: {type(e)}")
            self.logger.log_error(f"Error details: {e.__dict__ if hasattr(e, '__dict__') else 'No details available'}")

            # Create user-friendly error message
            user_message = get_user_friendly_error_message(str(e))

            return {
                "status": "market_data_error",
                "message": user_message,
                "technical_details": technical_error
            }

    @with_retry(max_retries=3, base_delay=1.0, max_delay=10.0, use_circuit_breaker=True)
    def _make_polygon_request(self, url: str, params: Dict) -> requests.Response:
        """
        Make a request to the Polygon API with retry logic and circuit breaker pattern.

        Args:
            url (str): The URL to request
            params (Dict): Query parameters

        Returns:
            requests.Response: The HTTP response

        Raises:
            RetryableError: If the request should be retried
            requests.exceptions.RequestException: For other request errors
        """
        self.logger.log_info("Making request to Polygon API...")

        try:
            # Use a session for better connection pooling and reliability
            session = requests.Session()

            # Set longer timeouts for better handling of slow connections
            # connect_timeout: time to establish the connection
            # read_timeout: time to receive the first byte after connection is established
            connect_timeout = 10.0  # seconds
            read_timeout = 30.0  # seconds

            response = session.get(
                url,
                params=params,
                timeout=(connect_timeout, read_timeout),
                # Add headers to help with debugging
                headers={
                    'User-Agent': 'TradingBot/1.0',
                    'Accept': 'application/json'
                }
            )

            self.logger.log_info(f"Response status code: {response.status_code}")

            # Handle rate limiting (429) or server errors (5xx)
            if response.status_code == 429 or 500 <= response.status_code < 600:
                self.logger.log_warning(f"Retryable error: Polygon API returned status {response.status_code}")
                self.logger.log_warning(f"Response headers: {response.headers}")

                # If we have a Retry-After header, log it
                retry_after = response.headers.get('Retry-After')
                if retry_after:
                    self.logger.log_info(f"Retry-After header: {retry_after}")

                # Raise a RetryableError to trigger the retry mechanism
                raise RetryableError(f"Polygon API returned status {response.status_code}")

            # For other error status codes, just raise the standard exception
            if not response.ok:
                self.logger.log_error(f"Polygon API error: Status {response.status_code}")
                self.logger.log_error(f"Response headers: {response.headers}")
                self.logger.log_error(f"Response text: {response.text}")
                try:
                    error_data = response.json()
                    self.logger.log_error(f"Error details: {error_data}")
                except:
                    self.logger.log_error("Could not parse error response as JSON")
                response.raise_for_status()  # This will raise an HTTPError

            return response

        except requests.exceptions.ConnectTimeout as e:
            self.logger.log_error(f"Connection timeout while connecting to Polygon API: {str(e)}")
            raise RetryableError(f"Connection timeout: {str(e)}") from e

        except requests.exceptions.ReadTimeout as e:
            self.logger.log_error(f"Read timeout while waiting for Polygon API response: {str(e)}")
            raise RetryableError(f"Read timeout: {str(e)}") from e

        except requests.exceptions.ConnectionError as e:
            self.logger.log_error(f"Connection error with Polygon API: {str(e)}")
            raise RetryableError(f"Connection error: {str(e)}") from e

        except requests.exceptions.RequestException as e:
            self.logger.log_error(f"Request exception with Polygon API: {str(e)}")
            raise

    def check_market_status(self) -> Dict:
        """
        Check if the forex market is open using our enhanced market conditions handler.
        Provides detailed information about market hours, active trading centers, and market activity.

        Returns:
            Dict: Market status information
        """
        try:
            # Use our enhanced market conditions handler which already handles the bypass setting
            market_status = self.market_conditions.is_market_open()

            self.logger.log_info(f"Forex Market is open: {market_status['is_open']}")
            if not market_status['is_open']:
                self.logger.log_info(f"Reason: {market_status['reason']}")
            else:
                self.logger.log_info(f"Active trading centers: {', '.join(market_status['active_centers'])}")
                self.logger.log_info(f"Market activity: {market_status['market_activity']}")

            return market_status

        except Exception as e:
            self.logger.log_error(f"Error checking market status: {str(e)}")
            self.logger.log_error(f"Error type: {type(e)}")
            self.logger.log_error(f"Error details: {e.__dict__ if hasattr(e, '__dict__') else 'No details available'}")
            self.logger.log_info("Assuming market is open due to error")

            # Return default values assuming market is open
            return {
                "is_open": True,
                "server_time": datetime.now(timezone.utc).isoformat(),
                "reason": "Error checking market status - assuming open",
                "active_centers": [],
                "market_activity": "unknown"
            }

    def get_quote(self, symbol: str) -> Dict:
        """
        Get the latest quote for a symbol using Polygon's v3 quotes API.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")

        Returns:
            Dict: Quote information including bid, ask, and spread
        """
        # Check cache first
        now = datetime.now(timezone.utc)
        if symbol in self.quotes_cache and now < self.quotes_cache_expiry.get(symbol, now):
            self.logger.log_info(f"Using cached quote for {symbol}")
            return self.quotes_cache[symbol]

        try:
            # Prepare symbol for Polygon (format as C:EUR-USD for forex)
            formatted_symbol = symbol.replace('/', '-').replace('_', '-')
            polygon_symbol = f"C:{formatted_symbol}"

            # Use v3 endpoint for quotes
            url = f"https://api.polygon.io/v3/quotes/{polygon_symbol}"
            params = {
                "apiKey": self.api_key,
                "limit": 1,  # Get just the latest quote
                "sort": "timestamp",
                "order": "desc"  # Most recent first
            }

            # Use the retry mechanism to make the API call
            response = self._make_polygon_request(url, params)
            data = response.json()

            results = data.get("results", [])
            if not results or len(results) == 0:
                # Fall back to v2 snapshot API if v3 doesn't return results
                return self._get_quote_fallback(symbol)

            # Get the most recent quote
            latest_quote = results[0]

            # Extract bid and ask prices
            bid = latest_quote.get("bid_price", 0.0)
            ask = latest_quote.get("ask_price", 0.0)

            # Calculate spread
            spread = ask - bid

            # Get timestamp (convert from nanoseconds to seconds)
            quote_timestamp = latest_quote.get("participant_timestamp", 0)
            if quote_timestamp > 0:
                quote_timestamp = datetime.fromtimestamp(quote_timestamp / 1_000_000_000, tz=timezone.utc).isoformat()
            else:
                quote_timestamp = now.isoformat()

            # Create quote object
            quote = {
                "symbol": symbol,
                "bid": bid,
                "ask": ask,
                "spread": spread,
                "timestamp": quote_timestamp,
                "source": "polygon_v3"
            }

            # Cache the quote
            self.quotes_cache[symbol] = quote
            self.quotes_cache_expiry[symbol] = now + timedelta(seconds=self.quotes_cache_ttl)

            self.logger.log_info(f"Got quote for {symbol}: bid={bid}, ask={ask}, spread={spread}")
            return quote

        except Exception as e:
            self.logger.log_error(f"Error getting quote from v3 API for {symbol}: {str(e)}")
            # Try fallback method
            return self._get_quote_fallback(symbol)

    def _get_quote_fallback(self, symbol: str) -> Dict:
        """
        Fallback method to get quotes using the v2 snapshot API.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")

        Returns:
            Dict: Quote information including bid, ask, and spread
        """
        now = datetime.now(timezone.utc)
        try:
            # Prepare symbol for Polygon (add C: prefix for forex and remove all non-alphanumeric chars)
            polygon_symbol = f"C:{symbol.replace('/', '').replace('_', '')}"

            # Use v2 endpoint for quotes
            url = f"{self.base_url}/snapshot/locale/global/markets/forex/tickers/{polygon_symbol}"
            params = {"apiKey": self.api_key}

            # Use the retry mechanism to make the API call
            response = self._make_polygon_request(url, params)
            data = response.json()

            ticker = data.get("ticker", {})
            if not ticker:
                raise ValueError(f"No quote data returned for {symbol}")

            # Extract bid and ask prices
            bid = ticker.get("bid", 0.0)
            ask = ticker.get("ask", 0.0)

            # Calculate spread
            spread = ask - bid

            # Create quote object
            quote = {
                "symbol": symbol,
                "bid": bid,
                "ask": ask,
                "spread": spread,
                "timestamp": now.isoformat(),
                "source": "polygon_v2_fallback"
            }

            # Cache the quote
            self.quotes_cache[symbol] = quote
            self.quotes_cache_expiry[symbol] = now + timedelta(seconds=self.quotes_cache_ttl)

            self.logger.log_info(f"Got quote for {symbol} using fallback: bid={bid}, ask={ask}, spread={spread}")
            return quote

        except Exception as e:
            self.logger.log_error(f"Error getting quote using fallback for {symbol}: {str(e)}")

            # Return a default quote with a warning
            return {
                "symbol": symbol,
                "bid": 0.0,
                "ask": 0.0,
                "spread": 0.0,
                "timestamp": now.isoformat(),
                "error": str(e),
                "warning": "Using default values due to error",
                "source": "default_fallback"
            }

    def check_market_conditions(self, symbol: str, trading_sessions: List[str] = None, avoid_high_spread: bool = True) -> Dict:
        """
        Check comprehensive market conditions for a symbol.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")
            trading_sessions (List[str], optional): List of trading sessions to check
            avoid_high_spread (bool, optional): Whether to avoid trading when spread is too high. Defaults to True.

        Returns:
            Dict: Market conditions information
        """
        try:
            # First check if market is open
            market_status = self.check_market_status()

            # Get current quote
            quote = self.get_quote(symbol)

            # Check comprehensive market conditions
            conditions = self.market_conditions.get_market_conditions(
                instrument=symbol.replace("/", "_"),
                bid=quote["bid"],
                ask=quote["ask"],
                trading_sessions=trading_sessions,
                avoid_high_spread=avoid_high_spread
            )

            self.logger.log_info(f"Market conditions for {symbol}: {conditions['is_safe_to_trade']}")
            if not conditions["is_safe_to_trade"]:
                self.logger.log_info(f"Reason: {conditions['reason']}")

            return conditions

        except Exception as e:
            self.logger.log_error(f"Error checking market conditions for {symbol}: {str(e)}")

            # Return a default response
            return {
                "is_safe_to_trade": False,
                "reason": f"Error checking market conditions: {str(e)}",
                "market_hours": {"is_open": True},
                "spread_info": None,
                "news_info": None,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e)
            }