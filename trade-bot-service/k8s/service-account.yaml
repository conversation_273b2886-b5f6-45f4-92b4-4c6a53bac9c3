apiVersion: v1
kind: ServiceAccount
metadata:
  name: trade-bot-sa
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: trade-bot-role
  namespace: default
rules:
  - apiGroups: [""]
    resources: ["pods", "pods/log"]
    verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: trade-bot-rolebinding
  namespace: default
subjects:
  - kind: ServiceAccount
    name: trade-bot-sa
    namespace: default
roleRef:
  kind: Role
  name: trade-bot-role
  apiGroup: rbac.authorization.k8s.io
