#!/bin/bash

# This script creates a Kubernetes secret for the Firebase key
# The Firebase key should be in a file named firebase-key.json
# This file should be in the same directory as this script

# Check if the Firebase key file exists
if [ ! -f "./firebase-key.json" ]; then
  echo "Error: firebase-key.json not found in the current directory."
  echo "Please place your Firebase key file in this directory and try again."
  exit 1
fi

# Create the secret
kubectl create secret generic firebase-key --from-file=firebase-key.json=./firebase-key.json

# Verify the secret was created
kubectl get secret firebase-key

echo "Firebase key secret created successfully." 