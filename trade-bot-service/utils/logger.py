import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional, Union, Dict, Any
from enum import IntEnum

class LogLevel(IntEnum):
    """Log levels for the application."""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL

# Global log level setting - can be overridden by environment variable
GLOBAL_LOG_LEVEL = LogLevel.INFO
if os.environ.get('LOG_LEVEL'):
    level_name = os.environ.get('LOG_LEVEL', 'INFO').upper()
    if hasattr(LogLevel, level_name):
        GLOBAL_LOG_LEVEL = getattr(LogLevel, level_name)

# Dictionary to track verbose loggers
VERBOSE_LOGGERS: Dict[str, bool] = {
    'oanda_client': False,  # Less verbose logging for OANDA client
    'MarketDataProvider': False,  # Less verbose logging for market data provider
    'FirebaseClient': False,  # Less verbose logging for Firebase client
}

class Logger:
    """Custom logger for the trading application."""

    def __init__(
        self,
        name: str,
        log_dir: str = "logs",
        level: Optional[int] = None,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5
    ):
        """
        Initialize the logger.

        Args:
            name (str): Logger name
            log_dir (str): Directory for log files
            level (int): Logging level
            max_file_size (int): Maximum size of each log file in bytes
            backup_count (int): Number of backup files to keep
        """
        self.name = name
        self.log_dir = Path(log_dir)

        # Determine the appropriate log level
        if level is None:
            # Use the global log level by default
            self.level = GLOBAL_LOG_LEVEL
            # Check if this logger should be less verbose
            if name in VERBOSE_LOGGERS and not VERBOSE_LOGGERS[name]:
                # Use a higher log level (less verbose) for this logger
                self.level = max(LogLevel.INFO, GLOBAL_LOG_LEVEL)
        else:
            self.level = level

        self.max_file_size = max_file_size
        self.backup_count = backup_count

        # Create log directory if it doesn't exist
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # Setup logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(self.level)

        # Prevent propagation to root logger to avoid double logging
        self.logger.propagate = False

        # Clear any existing handlers
        self.logger.handlers = []

        # Add handlers
        self._setup_handlers()

        # Log initialization (only at debug level)
        self.logger.debug(f"Logger initialized for {name} with level {logging.getLevelName(self.level)}")
        self.logger.debug(f"Log directory: {log_dir}")
        self.logger.debug(f"Max file size: {max_file_size} bytes")
        self.logger.debug(f"Backup count: {backup_count}")

    def _setup_handlers(self):
        """Setup file and console handlers for the logger."""
        # Create formatters
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - [%(name)s] %(message)s"
        )
        console_formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - [%(name)s] %(message)s"
        )

        # Setup file handler
        log_file = self.log_dir / f"{self.name}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count
        )
        file_handler.setLevel(self.level)
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)

        # Setup console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.level)
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

    def log_trade(self, trade_data: dict):
        """
        Log a trade execution.

        Args:
            trade_data (dict): Trade execution data
        """
        self.logger.info(
            f"Trade executed - Action: {trade_data.get('action')}, "
            f"Instrument: {trade_data.get('instrument')}, "
            f"Units: {trade_data.get('units')}, "
            f"Price: {trade_data.get('price')}, "
            f"Reason: {trade_data.get('reason')}"
        )

    def log_error(self, error: Union[Exception, str], context: Optional[str] = None):
        """
        Log an error with context.

        Args:
            error (Union[Exception, str]): The error that occurred or error message
            context (Optional[str]): Additional context about the error
        """
        if isinstance(error, Exception):
            message = f"Error: {str(error)}"
            if context:
                message = f"{context} - {message}"
            self.logger.error(message, exc_info=True)
            self.logger.error(f"Error type: {type(error)}")
            self.logger.error(f"Error details: {error.__dict__ if hasattr(error, '__dict__') else 'No details available'}")
        else:
            message = f"Error: {error}"
            if context:
                message = f"{context} - {message}"
            self.logger.error(message)

    def log_warning(self, message: str):
        """
        Log a warning message.

        Args:
            message (str): Message to log
        """
        self.logger.warning(message)

    def log_info(self, message: str, verbose: bool = False):
        """
        Log an info message.

        Args:
            message (str): Message to log
            verbose (bool, optional): Whether this is a verbose message. Defaults to False.
        """
        # Skip verbose messages for non-verbose loggers
        if verbose and self.name in VERBOSE_LOGGERS and not VERBOSE_LOGGERS[self.name]:
            # Log at debug level instead
            self.logger.debug(f"[VERBOSE] {message}")
        else:
            self.logger.info(message)

    def log_debug(self, message: str):
        """
        Log a debug message.

        Args:
            message (str): Message to log
        """
        self.logger.debug(message)

    def log_critical(self, message: str):
        """
        Log a critical message.

        Args:
            message (str): Message to log
        """
        self.logger.critical(message)

    def log_structured(self, level: LogLevel, event_type: str, data: Dict[str, Any]):
        """
        Log a structured message with additional data.

        Args:
            level (LogLevel): Log level
            event_type (str): Type of event (e.g., 'trade_executed', 'error')
            data (Dict[str, Any]): Additional data to log
        """
        import json
        structured_message = f"[{event_type}] {json.dumps(data)}"

        if level == LogLevel.DEBUG:
            self.logger.debug(structured_message)
        elif level == LogLevel.INFO:
            self.logger.info(structured_message)
        elif level == LogLevel.WARNING:
            self.logger.warning(structured_message)
        elif level == LogLevel.ERROR:
            self.logger.error(structured_message)
        elif level == LogLevel.CRITICAL:
            self.logger.critical(structured_message)

    def get_logger(self) -> logging.Logger:
        """
        Get the underlying logger instance.

        Returns:
            logging.Logger: The logger instance
        """
        return self.logger