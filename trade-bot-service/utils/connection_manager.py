"""
Connection manager for handling broker connections and reconnection logic.
"""

import time
import threading
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Callable, List

from utils.logger import Logger
from utils.retry_utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, RetryableError

class ConnectionState:
    """Connection state enum-like class."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"

class ConnectionManager:
    """
    Manages connections to external services with automatic reconnection.

    This class provides:
    - Connection state tracking
    - Automatic reconnection with exponential backoff
    - Health monitoring and reporting
    - Connection event callbacks
    """

    def __init__(self, name: str, health_client=None):
        """
        Initialize the connection manager.

        Args:
            name: Name of the connection (for logging)
            health_client: Optional health client for reporting connection status
        """
        self.name = name
        self.logger = Logger(f"ConnectionManager-{name}")
        self.health_client = health_client
        self.state = ConnectionState.DISCONNECTED
        self.last_connected_time = None
        self.last_disconnected_time = None
        self.connection_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_delay_base = 2.0  # Base delay in seconds
        self.reconnect_delay_max = 300.0  # Maximum delay in seconds (5 minutes)
        self.reconnect_thread = None
        self.is_reconnecting = False
        self.on_connect_callbacks: List[Callable] = []
        self.on_disconnect_callbacks: List[Callable] = []
        self.on_reconnect_callbacks: List[Callable] = []
        self.on_failure_callbacks: List[Callable] = []
        self.connection_lock = threading.Lock()

    def register_callback(self, event_type: str, callback: Callable):
        """
        Register a callback for a connection event.

        Args:
            event_type: Event type (connect, disconnect, reconnect, failure)
            callback: Callback function to register
        """
        if event_type == "connect":
            self.on_connect_callbacks.append(callback)
        elif event_type == "disconnect":
            self.on_disconnect_callbacks.append(callback)
        elif event_type == "reconnect":
            self.on_reconnect_callbacks.append(callback)
        elif event_type == "failure":
            self.on_failure_callbacks.append(callback)
        else:
            self.logger.log_warning(f"Unknown event type: {event_type}")

    def connect(self, connect_func: Optional[Callable] = None, *args, **kwargs) -> bool:
        """
        Connect to the service.

        Args:
            connect_func: Function to call to establish the connection (optional)
            *args: Arguments to pass to the connect function
            **kwargs: Keyword arguments to pass to the connect function

        Returns:
            bool: True if connection was successful, False otherwise
        """
        with self.connection_lock:
            if self.state == ConnectionState.CONNECTED:
                self.logger.log_info(f"Already connected to {self.name}")
                return True

            self.state = ConnectionState.CONNECTING
            self.connection_attempts = 1

            try:
                self.logger.log_info(f"Connecting to {self.name}...")

                # Update health status
                if self.health_client:
                    self.health_client.update_dependency(
                        self.name, "connecting", f"Connecting to {self.name}..."
                    )

                # Call the connect function if provided
                if connect_func:
                    result = connect_func(*args, **kwargs)
                else:
                    # If no connect function is provided, just assume success
                    # This is useful for initialization when we don't want to check yet
                    result = True

                # Update connection state
                self.state = ConnectionState.CONNECTED
                self.last_connected_time = datetime.now(timezone.utc)

                # Update health status
                if self.health_client:
                    self.health_client.update_dependency(
                        self.name, "healthy", f"Connected to {self.name}"
                    )

                self.logger.log_info(f"Successfully connected to {self.name}")

                # Call connect callbacks
                for callback in self.on_connect_callbacks:
                    try:
                        callback()
                    except Exception as e:
                        self.logger.log_error(f"Error in connect callback: {str(e)}")

                return True
            except Exception as e:
                self.state = ConnectionState.DISCONNECTED
                self.last_disconnected_time = datetime.now(timezone.utc)

                # Update health status
                if self.health_client:
                    self.health_client.update_dependency(
                        self.name, "unhealthy", f"Failed to connect to {self.name}: {str(e)}"
                    )

                self.logger.log_error(f"Failed to connect to {self.name}: {str(e)}")

                # Call disconnect callbacks
                for callback in self.on_disconnect_callbacks:
                    try:
                        callback()
                    except Exception as e:
                        self.logger.log_error(f"Error in disconnect callback: {str(e)}")

                return False

    def disconnect(self, disconnect_func: Optional[Callable] = None, *args, **kwargs) -> bool:
        """
        Disconnect from the service.

        Args:
            disconnect_func: Function to call to disconnect
            *args: Arguments to pass to the disconnect function
            **kwargs: Keyword arguments to pass to the disconnect function

        Returns:
            bool: True if disconnection was successful, False otherwise
        """
        with self.connection_lock:
            if self.state == ConnectionState.DISCONNECTED:
                self.logger.log_info(f"Already disconnected from {self.name}")
                return True

            try:
                self.logger.log_info(f"Disconnecting from {self.name}...")

                # Call the disconnect function if provided
                if disconnect_func:
                    disconnect_func(*args, **kwargs)

                # Update connection state
                self.state = ConnectionState.DISCONNECTED
                self.last_disconnected_time = datetime.now(timezone.utc)

                # Update health status
                if self.health_client:
                    self.health_client.update_dependency(
                        self.name, "disconnected", f"Disconnected from {self.name}"
                    )

                self.logger.log_info(f"Successfully disconnected from {self.name}")

                # Call disconnect callbacks
                for callback in self.on_disconnect_callbacks:
                    try:
                        callback()
                    except Exception as e:
                        self.logger.log_error(f"Error in disconnect callback: {str(e)}")

                return True
            except Exception as e:
                self.logger.log_error(f"Error disconnecting from {self.name}: {str(e)}")
                return False

    def reconnect(self, connect_func: Callable, disconnect_func: Optional[Callable] = None, timeout: float = 5.0, *args, **kwargs) -> bool:
        """
        Reconnect to the service.

        Args:
            connect_func: Function to call to establish the connection
            disconnect_func: Function to call to disconnect (optional)
            timeout: Timeout in seconds for the connection attempt
            *args: Arguments to pass to the connect function
            **kwargs: Keyword arguments to pass to the connect function

        Returns:
            bool: True if reconnection was successful, False otherwise
        """
        with self.connection_lock:
            if self.is_reconnecting:
                self.logger.log_info(f"Already attempting to reconnect to {self.name}")
                return False

            self.is_reconnecting = True
            self.state = ConnectionState.RECONNECTING

            # Update health status
            if self.health_client:
                self.health_client.update_dependency(
                    self.name, "reconnecting", f"Reconnecting to {self.name}..."
                )

            # Disconnect first if needed
            if disconnect_func and self.state != ConnectionState.DISCONNECTED:
                try:
                    self.logger.log_info(f"Disconnecting from {self.name} before reconnecting...")
                    disconnect_func()
                except Exception as e:
                    self.logger.log_warning(f"Error disconnecting from {self.name} before reconnecting: {str(e)}")

            # Start reconnection in a separate thread
            self.reconnect_thread = threading.Thread(
                target=self._reconnect_worker,
                args=(connect_func,) + args,
                kwargs=kwargs,
                daemon=True
            )
            self.reconnect_thread.start()

            return True

    def _reconnect_worker(self, connect_func: Callable, timeout: float = 5.0, *args, **kwargs):
        """
        Worker thread for reconnection attempts.

        Args:
            connect_func: Function to call to establish the connection
            timeout: Timeout in seconds for the connection attempt
            *args: Arguments to pass to the connect function
            **kwargs: Keyword arguments to pass to the connect function
        """
        retry_handler = RetryHandler(
            max_retries=self.max_reconnect_attempts,
            base_delay=self.reconnect_delay_base,
            max_delay=self.reconnect_delay_max,
            logger=self.logger
        )

        try:
            # Use a thread pool to execute with timeout
            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(retry_handler.execute_with_retry, connect_func, *args, **kwargs)
                try:
                    # Wait for the result with a timeout
                    result = future.result(timeout=timeout)
                except concurrent.futures.TimeoutError:
                    self.logger.log_warning(f"Reconnection attempt timed out after {timeout} seconds")
                    future.cancel()
                    raise TimeoutError(f"Reconnection timed out after {timeout} seconds")

            # Update connection state
            with self.connection_lock:
                self.state = ConnectionState.CONNECTED
                self.last_connected_time = datetime.now(timezone.utc)
                self.is_reconnecting = False

            # Update health status
            if self.health_client:
                self.health_client.update_dependency(
                    self.name, "healthy", f"Reconnected to {self.name}"
                )

            self.logger.log_info(f"Successfully reconnected to {self.name}")

            # Call reconnect callbacks
            for callback in self.on_reconnect_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.logger.log_error(f"Error in reconnect callback: {str(e)}")

        except Exception as e:
            # Update connection state
            with self.connection_lock:
                self.state = ConnectionState.FAILED
                self.is_reconnecting = False

            # Update health status
            if self.health_client:
                self.health_client.update_dependency(
                    self.name, "unhealthy", f"Failed to reconnect to {self.name}: {str(e)}"
                )

            self.logger.log_error(f"Failed to reconnect to {self.name} after {self.max_reconnect_attempts} attempts: {str(e)}")

            # Call failure callbacks
            for callback in self.on_failure_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.logger.log_error(f"Error in failure callback: {str(e)}")

    def check_connection(self, check_func: Callable, timeout: float = 5.0, *args, **kwargs) -> bool:
        """
        Check if the connection is still active.

        Args:
            check_func: Function to call to check the connection
            timeout: Timeout in seconds for the connection check
            *args: Arguments to pass to the check function
            **kwargs: Keyword arguments to pass to the check function

        Returns:
            bool: True if connection is active, False otherwise
        """
        if self.state != ConnectionState.CONNECTED and self.state != ConnectionState.CONNECTING:
            return False

        try:
            self.logger.log_debug(f"Checking connection to {self.name}...")

            # Add timeout handling
            import threading
            import concurrent.futures

            # Use a thread pool to execute the check function with a timeout
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(check_func, *args, **kwargs)
                try:
                    result = future.result(timeout=timeout)

                    # Update health status
                    if self.health_client:
                        self.health_client.update_dependency(
                            self.name, "healthy", f"Connected to {self.name}"
                        )

                    # Update connection state if it was CONNECTING
                    if self.state == ConnectionState.CONNECTING:
                        with self.connection_lock:
                            self.state = ConnectionState.CONNECTED
                            self.last_connected_time = datetime.now(timezone.utc)

                    return True
                except concurrent.futures.TimeoutError:
                    self.logger.log_warning(f"Connection check timed out for {self.name} after {timeout} seconds")
                    future.cancel()
                    raise TimeoutError(f"Connection check timed out after {timeout} seconds")
        except Exception as e:
            self.logger.log_warning(f"Connection check failed for {self.name}: {str(e)}")

            # Update connection state
            with self.connection_lock:
                self.state = ConnectionState.DISCONNECTED
                self.last_disconnected_time = datetime.now(timezone.utc)

            # Update health status
            if self.health_client:
                self.health_client.update_dependency(
                    self.name, "unhealthy", f"Connection check failed for {self.name}: {str(e)}"
                )

            # Call disconnect callbacks
            for callback in self.on_disconnect_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.logger.log_error(f"Error in disconnect callback: {str(e)}")

            return False

    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get the current connection status.

        Returns:
            Dict[str, Any]: Connection status information
        """
        return {
            "name": self.name,
            "state": self.state,
            "last_connected": self.last_connected_time.isoformat() if self.last_connected_time else None,
            "last_disconnected": self.last_disconnected_time.isoformat() if self.last_disconnected_time else None,
            "uptime": (datetime.now(timezone.utc) - self.last_connected_time).total_seconds() if self.last_connected_time else 0,
            "reconnect_attempts": self.connection_attempts
        }
