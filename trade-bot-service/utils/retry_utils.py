import time
import random
import requests
from typing import Callable, Any, Dict, Optional, Union, List, Tuple
from functools import wraps
from datetime import datetime, timedelta
from utils.logger import Logger

class RetryableError(Exception):
    """Exception class for errors that should trigger a retry."""
    pass

class CircuitBreaker:
    """Circuit breaker pattern implementation to prevent repeated failures."""

    # Class-level variables to track circuit state across all instances
    _circuit_state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    _failure_count = 0
    _last_failure_time = 0
    _failure_threshold = 5  # Number of failures before opening circuit
    _recovery_timeout = 60  # Seconds to wait before trying again (HALF_OPEN)

    @classmethod
    def record_failure(cls):
        """Record a failure and potentially open the circuit."""
        cls._failure_count += 1
        cls._last_failure_time = time.time()

        if cls._circuit_state == "CLOSED" and cls._failure_count >= cls._failure_threshold:
            cls._circuit_state = "OPEN"

    @classmethod
    def record_success(cls):
        """Record a success and potentially close the circuit."""
        if cls._circuit_state == "HALF_OPEN":
            cls._circuit_state = "CLOSED"
        cls._failure_count = 0

    @classmethod
    def can_execute(cls) -> bool:
        """Check if execution is allowed based on circuit state."""
        if cls._circuit_state == "CLOSED":
            return True

        if cls._circuit_state == "OPEN":
            # Check if recovery timeout has elapsed
            if time.time() - cls._last_failure_time > cls._recovery_timeout:
                cls._circuit_state = "HALF_OPEN"
                return True
            return False

        # In HALF_OPEN state, allow one request to test the waters
        return True


class RetryHandler:
    """Utility class for handling retries of API calls with exponential backoff."""

    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 30.0,
        backoff_factor: float = 2.0,
        jitter: bool = True,
        logger: Optional[Logger] = None,
        use_circuit_breaker: bool = True
    ):
        """
        Initialize the retry handler.

        Args:
            max_retries (int): Maximum number of retry attempts
            base_delay (float): Initial delay between retries in seconds
            max_delay (float): Maximum delay between retries in seconds
            backoff_factor (float): Factor by which the delay increases with each retry
            jitter (bool): Whether to add random jitter to the delay
            logger (Logger, optional): Logger instance
            use_circuit_breaker (bool): Whether to use the circuit breaker pattern
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        self.logger = logger or Logger("RetryHandler")
        self.use_circuit_breaker = use_circuit_breaker

    def _calculate_delay(self, attempt: int) -> float:
        """
        Calculate the delay for a retry attempt with exponential backoff.

        Args:
            attempt (int): The current retry attempt (0-based)

        Returns:
            float: Delay in seconds
        """
        delay = min(self.max_delay, self.base_delay * (self.backoff_factor ** attempt))

        if self.jitter:
            # Add random jitter between 0% and 25% of the delay
            jitter_amount = random.uniform(0, 0.25 * delay)
            delay += jitter_amount

        return delay

    def _should_retry(self, exception: Exception) -> bool:
        """
        Determine if a retry should be attempted based on the exception.

        Args:
            exception (Exception): The exception that occurred

        Returns:
            bool: True if retry should be attempted, False otherwise
        """
        # Log the exception type for debugging
        self.logger.log_info(f"Checking if exception should be retried: {type(exception).__name__}")

        # Retry on connection errors, timeouts, and specific HTTP status codes
        if isinstance(exception, requests.exceptions.RequestException):
            # Always retry on connection errors and timeouts
            if isinstance(exception, (
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout,
                requests.exceptions.ConnectTimeout,
                requests.exceptions.ReadTimeout,
                # Add more network-related exceptions
                requests.exceptions.ChunkedEncodingError,  # Connection broken while streaming response
                requests.exceptions.ContentDecodingError,  # Failed to decode response content
                requests.exceptions.SSLError,  # SSL certificate or handshake error
                requests.exceptions.ProxyError,  # Proxy connection failed
                requests.exceptions.TooManyRedirects  # Too many redirects
            )):
                self.logger.log_warning(f"Network error detected: {type(exception).__name__}. Will retry.")
                return True

            # Retry on DNS resolution failures (often a ConnectionError with specific error message)
            if isinstance(exception, requests.exceptions.ConnectionError) and "Name or service not known" in str(exception):
                self.logger.log_warning(f"DNS resolution failure detected. Will retry.")
                return True

            # Retry on specific HTTP status codes
            if isinstance(exception, requests.exceptions.HTTPError):
                status_code = exception.response.status_code
                # Retry on rate limiting (429) and server errors (5xx)
                if status_code == 429 or 500 <= status_code < 600:
                    self.logger.log_warning(f"HTTP error with status code {status_code}. Will retry.")
                    return True

        # Retry on custom RetryableError
        if isinstance(exception, RetryableError):
            self.logger.log_warning(f"RetryableError detected: {str(exception)}. Will retry.")
            return True

        # Don't retry on other exceptions
        self.logger.log_info(f"Exception {type(exception).__name__} is not retryable.")
        return False

    def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a function with retry logic and circuit breaker pattern.

        Args:
            func (Callable): Function to execute
            *args: Positional arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Any: Result of the function

        Raises:
            Exception: The last exception encountered if all retries fail
            CircuitBreakerOpenError: If the circuit breaker is open
        """
        # Check if circuit breaker allows execution
        if self.use_circuit_breaker and not CircuitBreaker.can_execute():
            circuit_error = RetryableError("Circuit breaker is open, preventing execution")
            self.logger.log_error(circuit_error, "Circuit breaker open")
            raise circuit_error

        last_exception = None
        network_error_count = 0
        max_network_errors = 2  # Allow at most 2 network errors before giving up

        for attempt in range(self.max_retries + 1):  # +1 for the initial attempt
            try:
                if attempt > 0:
                    delay = self._calculate_delay(attempt - 1)
                    self.logger.log_info(f"Retry attempt {attempt}/{self.max_retries} after {delay:.2f}s delay")
                    time.sleep(delay)

                result = func(*args, **kwargs)

                # Record success in circuit breaker
                if self.use_circuit_breaker:
                    CircuitBreaker.record_success()

                return result

            except Exception as e:
                last_exception = e

                # Check if it's a network error
                is_network_error = isinstance(e, requests.exceptions.RequestException) and isinstance(e, (
                    requests.exceptions.ConnectionError,
                    requests.exceptions.Timeout,
                    requests.exceptions.SSLError,
                    requests.exceptions.ProxyError
                ))

                if is_network_error:
                    network_error_count += 1
                    self.logger.log_warning(f"Network error detected: {type(e).__name__}. Count: {network_error_count}/{max_network_errors}")

                    # If we've hit the network error limit, record failure in circuit breaker
                    if network_error_count >= max_network_errors and self.use_circuit_breaker:
                        self.logger.log_error(e, f"Network error limit reached ({max_network_errors})")
                        CircuitBreaker.record_failure()

                if attempt < self.max_retries and self._should_retry(e) and network_error_count < max_network_errors:
                    self.logger.log_warning(
                        f"Attempt {attempt + 1}/{self.max_retries + 1} failed: {str(e)}. Retrying..."
                    )
                else:
                    # Record failure in circuit breaker if we're not retrying
                    if self.use_circuit_breaker:
                        CircuitBreaker.record_failure()

                    if attempt == self.max_retries:
                        self.logger.log_error(
                            f"All {self.max_retries + 1} attempts failed. Last error: {str(e)}"
                        )
                    elif network_error_count >= max_network_errors:
                        self.logger.log_error(
                            f"Network error limit reached ({max_network_errors}). Last error: {str(e)}"
                        )
                    else:
                        self.logger.log_error(
                            f"Non-retryable error on attempt {attempt + 1}: {str(e)}"
                        )
                    break

        # If we get here, all retries failed
        raise last_exception

def with_retry(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 30.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    use_circuit_breaker: bool = True
):
    """
    Decorator for functions that should be retried on failure.

    Args:
        max_retries (int): Maximum number of retry attempts
        base_delay (float): Initial delay between retries in seconds
        max_delay (float): Maximum delay between retries in seconds
        backoff_factor (float): Factor by which the delay increases with each retry
        jitter (bool): Whether to add random jitter to the delay
        use_circuit_breaker (bool): Whether to use the circuit breaker pattern

    Returns:
        Callable: Decorated function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get logger from the first argument if it's a class method with self.logger
            logger = None
            if args and hasattr(args[0], 'logger'):
                logger = args[0].logger

            retry_handler = RetryHandler(
                max_retries=max_retries,
                base_delay=base_delay,
                max_delay=max_delay,
                backoff_factor=backoff_factor,
                jitter=jitter,
                logger=logger,
                use_circuit_breaker=use_circuit_breaker
            )

            return retry_handler.execute_with_retry(func, *args, **kwargs)

        return wrapper

    return decorator


class OrderTracker:
    """
    Tracker for order status and partial fills.
    """

    def __init__(self, logger=None):
        """
        Initialize the order tracker.

        Args:
            logger: Logger instance
        """
        self.logger = logger or Logger("OrderTracker")
        self.orders = {}  # order_id -> order_info
        self.partial_fills = {}  # order_id -> list of partial fills
        self.rejected_orders = {}  # order_id -> rejection reason

    def track_order(self, order_id: str, order_info: Dict[str, Any]):
        """
        Start tracking an order.

        Args:
            order_id: Order ID
            order_info: Order information
        """
        self.orders[order_id] = order_info
        self.partial_fills[order_id] = []
        self.logger.log_info(f"Started tracking order {order_id}")

    def record_partial_fill(self, order_id: str, fill_info: Dict[str, Any]):
        """
        Record a partial fill for an order.

        Args:
            order_id: Order ID
            fill_info: Fill information
        """
        if order_id not in self.orders:
            self.logger.log_warning(f"Received partial fill for unknown order {order_id}")
            return

        self.partial_fills[order_id].append(fill_info)
        self.logger.log_info(f"Recorded partial fill for order {order_id}: {fill_info}")

    def record_rejection(self, order_id: str, reason: str):
        """
        Record a rejected order.

        Args:
            order_id: Order ID
            reason: Rejection reason
        """
        if order_id in self.orders:
            self.rejected_orders[order_id] = reason
            self.logger.log_warning(f"Order {order_id} was rejected: {reason}")
        else:
            self.logger.log_warning(f"Received rejection for unknown order {order_id}")

    def is_order_complete(self, order_id: str) -> bool:
        """
        Check if an order is complete (fully filled).

        Args:
            order_id: Order ID

        Returns:
            bool: True if order is complete, False otherwise
        """
        if order_id not in self.orders:
            return False

        # Check if order was rejected
        if order_id in self.rejected_orders:
            return True

        # Check if order has any partial fills
        if not self.partial_fills[order_id]:
            return False

        # Calculate total filled units
        total_filled = sum(fill.get("units", 0) for fill in self.partial_fills[order_id])

        # Compare with original order units
        original_units = self.orders[order_id].get("units", 0)

        return total_filled >= original_units

    def get_order_status(self, order_id: str) -> Dict[str, Any]:
        """
        Get the current status of an order.

        Args:
            order_id: Order ID

        Returns:
            Dict[str, Any]: Order status information
        """
        if order_id not in self.orders:
            return {"status": "unknown", "order_id": order_id}

        # Check if order was rejected
        if order_id in self.rejected_orders:
            return {
                "status": "rejected",
                "order_id": order_id,
                "reason": self.rejected_orders[order_id]
            }

        # Check if order has any partial fills
        if not self.partial_fills[order_id]:
            return {
                "status": "pending",
                "order_id": order_id,
                "order_info": self.orders[order_id]
            }

        # Calculate total filled units
        total_filled = sum(fill.get("units", 0) for fill in self.partial_fills[order_id])

        # Compare with original order units
        original_units = self.orders[order_id].get("units", 0)

        if total_filled >= original_units:
            return {
                "status": "filled",
                "order_id": order_id,
                "order_info": self.orders[order_id],
                "fills": self.partial_fills[order_id],
                "total_filled": total_filled
            }
        else:
            return {
                "status": "partial",
                "order_id": order_id,
                "order_info": self.orders[order_id],
                "fills": self.partial_fills[order_id],
                "total_filled": total_filled,
                "remaining": original_units - total_filled
            }

    def cleanup_old_orders(self, max_age: timedelta = timedelta(days=1)):
        """
        Clean up old orders from the tracker.

        Args:
            max_age: Maximum age of orders to keep
        """
        now = datetime.now()
        orders_to_remove = []

        for order_id, order_info in self.orders.items():
            order_time = order_info.get("time")
            if order_time:
                try:
                    order_datetime = datetime.fromisoformat(order_time.replace('Z', '+00:00'))
                    if now - order_datetime > max_age:
                        orders_to_remove.append(order_id)
                except (ValueError, TypeError):
                    # If we can't parse the time, keep the order
                    pass

        for order_id in orders_to_remove:
            self.orders.pop(order_id, None)
            self.partial_fills.pop(order_id, None)
            self.rejected_orders.pop(order_id, None)

        self.logger.log_info(f"Cleaned up {len(orders_to_remove)} old orders")
