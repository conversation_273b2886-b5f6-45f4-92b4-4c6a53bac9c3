"""
Market conditions utility module for handling market hours, weekend closures, news events, and spread monitoring.
"""
import os
import requests
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple
import pytz
from utils.logger import Logger
from utils.retry_utils import with_retry, RetryableError

class MarketConditions:
    """
    Handles market condition checks including market hours, weekend closures, and spread monitoring.
    """

    def __init__(self, api_key: str = None):
        """
        Initialize the market conditions handler.

        Args:
            api_key (str, optional): API key for Polygon.io
        """
        self.logger = Logger("MarketConditions")
        self.api_key = api_key

        # Check if we should bypass market closed checks (for local development)
        self.bypass_market_is_closed = os.getenv("BYPASS_MARKET_IS_CLOSED", "false").lower() == "true"

        # Polygon API endpoints
        self.polygon_base_url = "https://api.polygon.io"
        self.market_status_url = f"{self.polygon_base_url}/v1/marketstatus/now"
        self.market_holidays_url = f"{self.polygon_base_url}/v1/marketstatus/upcoming"

        # Define forex market hours (UTC) as fallback if API fails
        # Forex market is open 24/5, from Sunday 22:00 UTC to Friday 22:00 UTC
        self.market_open_hour_utc = 22  # 10 PM UTC on Sunday
        self.market_close_hour_utc = 22  # 10 PM UTC on Friday
        self.market_open_day = 6  # Sunday (0 = Monday, 6 = Sunday in Python's datetime)
        self.market_close_day = 4  # Friday

        # Define major forex centers and their trading hours (UTC)
        self.trading_centers = {
            "Sydney": {"open": 22, "close": 7},  # 22:00-07:00 UTC
            "Tokyo": {"open": 0, "close": 9},    # 00:00-09:00 UTC
            "London": {"open": 8, "close": 17},  # 08:00-17:00 UTC
            "New York": {"open": 13, "close": 22}  # 13:00-22:00 UTC
        }

        # Define spread thresholds for different currency pairs (in pips)
        self.spread_thresholds = {
            "EUR_USD": 1.0,  # 1 pip (0.0001)
            "GBP_USD": 1.5,  # 1.5 pips (0.00015)
            "USD_JPY": 1.5,  # 1.5 pips (0.015 for JPY pairs)
            "AUD_USD": 1.5,  # 1.5 pips (0.00015)
            "USD_CAD": 2.0,  # 2 pips (0.0002)
            "USD_CHF": 2.0,  # 2 pips (0.0002)
            "EUR_JPY": 2.0,  # 2 pips (0.02 for JPY pairs)
            "GBP_JPY": 2.5,  # 2.5 pips (0.025 for JPY pairs)
            "default": 3.0   # Default threshold for other pairs
        }

        # Cache for market status and holidays
        self.market_status_cache = None
        self.market_status_cache_expiry = datetime.now(timezone.utc)
        self.market_holidays_cache = None
        self.market_holidays_cache_expiry = datetime.now(timezone.utc)

        # Cache TTL in seconds
        self.status_cache_ttl = 300  # 5 minutes
        self.holidays_cache_ttl = 86400  # 24 hours

        self.logger.log_info("MarketConditions initialized")
        if self.bypass_market_is_closed:
            self.logger.log_info("BYPASS_MARKET_IS_CLOSED is enabled - market will always be reported as open")

    @with_retry(max_retries=3, base_delay=1.0, max_delay=10.0, use_circuit_breaker=True)
    def _make_polygon_request(self, url: str, params: Dict = None) -> Dict:
        """
        Make a request to the Polygon API with retry logic.

        Args:
            url (str): The URL to request
            params (Dict, optional): Query parameters

        Returns:
            Dict: The response data

        Raises:
            RetryableError: If the request fails with a retryable error
            Exception: For other errors
        """
        if params is None:
            params = {}

        if "apiKey" not in params and self.api_key:
            params["apiKey"] = self.api_key

        try:
            response = requests.get(url, params=params, timeout=10)

            # Check if the request was successful
            if response.status_code == 200:
                return response.json()
            elif response.status_code in [429, 502, 503, 504]:
                self.logger.log_warning(f"Retryable error from Polygon API: {response.status_code}")
                raise RetryableError(f"Polygon API returned status code {response.status_code}")
            else:
                self.logger.log_error(f"Error from Polygon API: {response.status_code} - {response.text}")
                raise Exception(f"Polygon API returned status code {response.status_code}: {response.text}")

        except requests.exceptions.RequestException as e:
            self.logger.log_error(f"Request exception when calling Polygon API: {str(e)}")
            raise RetryableError(f"Request exception: {str(e)}")

    def is_market_open(self) -> Dict[str, Any]:
        """
        Check if the forex market is currently open using Polygon's market status API.
        Falls back to local calculation if the API call fails.

        Returns:
            Dict[str, Any]: Market status information
        """
        # If bypass_market_is_closed is enabled, always return market as open
        if self.bypass_market_is_closed:
            self.logger.log_info("BYPASS_MARKET_IS_CLOSED is enabled - reporting market as open")
            return {
                "is_open": True,
                "reason": "Development mode - market always open",
                "current_time_utc": datetime.now(timezone.utc).isoformat(),
                "server_time": datetime.now(timezone.utc).isoformat(),
                "active_centers": ["Development"],
                "market_activity": "high",
                "source": "bypass_setting"
            }

        # Get current UTC time
        now = datetime.now(timezone.utc)

        # Check if we have a valid cached response
        if (self.market_status_cache is not None and
            now < self.market_status_cache_expiry):
            self.logger.log_info("Using cached market status")
            return self.market_status_cache

        # Try to get market status from Polygon API
        try:
            data = self._make_polygon_request(self.market_status_url)

            # Extract forex market status
            forex_status = data.get("currencies", {}).get("fx")
            is_open = forex_status == "open"
            server_time = data.get("serverTime")

            self.logger.log_info(f"Polygon API: Forex Market is {'open' if is_open else 'closed'}")

            # Check for upcoming holidays
            holidays = self._get_upcoming_holidays()
            holiday_reason = None

            # Check if today is a holiday
            today_str = now.strftime("%Y-%m-%d")
            for holiday in holidays:
                if holiday.get("date") == today_str and holiday.get("exchange") == "forex":
                    holiday_reason = f"Holiday: {holiday.get('name')}"
                    break

            # Get current time components for calculations
            current_day = now.weekday()  # 0 = Monday, 6 = Sunday
            current_hour = now.hour
            current_minute = now.minute

            # If API says market is closed but doesn't specify why, check if it's a weekend
            reason = holiday_reason
            if not is_open and not reason:
                # Check if it's a weekend closure using local calculation

                # Friday after market close (22:00 UTC) to Sunday before market open (22:00 UTC)
                if (current_day == self.market_close_day and
                    (current_hour > self.market_close_hour_utc or
                     (current_hour == self.market_close_hour_utc and current_minute > 0))) or \
                   (current_day == 5) or \
                   (current_day == 6 and
                    (current_hour < self.market_open_hour_utc or
                     (current_hour == self.market_open_hour_utc and current_minute == 0))):
                    reason = "Weekend market closure"

            # Check which trading centers are currently open
            active_centers = []
            for center, hours in self.trading_centers.items():
                # Handle centers that cross UTC day boundary
                if hours["open"] > hours["close"]:
                    if current_hour >= hours["open"] or current_hour < hours["close"]:
                        active_centers.append(center)
                else:
                    if hours["open"] <= current_hour < hours["close"]:
                        active_centers.append(center)

            # Determine market activity level based on active trading centers
            market_activity = "low"
            if len(active_centers) >= 2:
                market_activity = "high"
            elif len(active_centers) == 1:
                market_activity = "medium"

            # If no centers are active during regular market hours, market activity is very low
            if len(active_centers) == 0 and is_open:
                market_activity = "very low"

            # Create response
            response = {
                "is_open": is_open,
                "reason": reason,
                "current_time_utc": now.isoformat(),
                "server_time": server_time,
                "active_centers": active_centers,
                "market_activity": market_activity,
                "source": "polygon_api"
            }

            # Cache the response
            self.market_status_cache = response
            self.market_status_cache_expiry = now + timedelta(seconds=self.status_cache_ttl)

            return response

        except Exception as e:
            self.logger.log_error(f"Error getting market status from Polygon API: {str(e)}")
            self.logger.log_info("Falling back to local market status calculation")

            # Fall back to local calculation
            return self._calculate_market_status_locally()

    def _calculate_market_status_locally(self) -> Dict[str, Any]:
        """
        Calculate market status locally without using the API.
        Used as a fallback when the API call fails.

        Returns:
            Dict[str, Any]: Market status information
        """
        # Get current UTC time
        now = datetime.now(timezone.utc)
        current_day = now.weekday()  # 0 = Monday, 6 = Sunday
        current_hour = now.hour
        current_minute = now.minute

        # Check if it's weekend closure
        is_open = True
        reason = None

        # Friday after market close (22:00 UTC) to Sunday before market open (22:00 UTC)
        if (current_day == self.market_close_day and
            (current_hour > self.market_close_hour_utc or
             (current_hour == self.market_close_hour_utc and current_minute > 0))) or \
           (current_day == 5) or \
           (current_day == 6 and
            (current_hour < self.market_open_hour_utc or
             (current_hour == self.market_open_hour_utc and current_minute == 0))):
            is_open = False
            reason = "Weekend market closure"

        # Check which trading centers are currently open
        active_centers = []
        for center, hours in self.trading_centers.items():
            # Handle centers that cross UTC day boundary
            if hours["open"] > hours["close"]:
                if current_hour >= hours["open"] or current_hour < hours["close"]:
                    active_centers.append(center)
            else:
                if hours["open"] <= current_hour < hours["close"]:
                    active_centers.append(center)

        # Determine market activity level based on active trading centers
        market_activity = "low"
        if len(active_centers) >= 2:
            market_activity = "high"
        elif len(active_centers) == 1:
            market_activity = "medium"

        # If no centers are active during regular market hours, market activity is very low
        if len(active_centers) == 0 and is_open:
            market_activity = "very low"

        return {
            "is_open": is_open,
            "reason": reason,
            "current_time_utc": now.isoformat(),
            "active_centers": active_centers,
            "market_activity": market_activity,
            "source": "local_calculation"
        }

    def _get_upcoming_holidays(self) -> List[Dict[str, Any]]:
        """
        Get upcoming market holidays from Polygon API.

        Returns:
            List[Dict[str, Any]]: List of upcoming holidays
        """
        now = datetime.now(timezone.utc)

        # Check if we have a valid cached response
        if (self.market_holidays_cache is not None and
            now < self.market_holidays_cache_expiry):
            self.logger.log_info("Using cached market holidays")
            return self.market_holidays_cache

        try:
            data = self._make_polygon_request(self.market_holidays_url)

            # Extract holidays - response is a list, not a dict
            if isinstance(data, dict) and "response" in data:
                holidays = data.get("response", [])
            elif isinstance(data, list):
                holidays = data
            else:
                self.logger.log_warning(f"Unexpected response format from Polygon API: {type(data)}")
                holidays = []

            # Filter for forex holidays
            forex_holidays = []
            for h in holidays:
                if isinstance(h, dict) and h.get("exchange") == "forex":
                    forex_holidays.append(h)

            self.logger.log_info(f"Got {len(forex_holidays)} upcoming forex holidays from Polygon API")

            # Cache the response
            self.market_holidays_cache = forex_holidays
            self.market_holidays_cache_expiry = now + timedelta(seconds=self.holidays_cache_ttl)

            return forex_holidays

        except Exception as e:
            self.logger.log_error(f"Error getting market holidays from Polygon API: {str(e)}")
            return []  # Return empty list if API call fails

    def check_spread(self, instrument: str, bid: float, ask: float) -> Dict[str, Any]:
        """
        Check if the current spread is acceptable for trading.

        Args:
            instrument (str): Trading instrument (e.g., "EUR_USD")
            bid (float): Current bid price
            ask (float): Current ask price

        Returns:
            Dict[str, Any]: Spread information and whether it's acceptable
        """
        # Calculate spread in pips
        spread_in_pips = self._calculate_spread_in_pips(instrument, bid, ask)

        # Get threshold for this instrument
        threshold = self.spread_thresholds.get(instrument, self.spread_thresholds["default"])

        # Check if spread is acceptable
        is_acceptable = spread_in_pips <= threshold

        return {
            "instrument": instrument,
            "spread_in_pips": spread_in_pips,
            "threshold": threshold,
            "is_acceptable": is_acceptable,
            "bid": bid,
            "ask": ask
        }

    def _calculate_spread_in_pips(self, instrument: str, bid: float, ask: float) -> float:
        """
        Calculate spread in pips for a given instrument.

        Args:
            instrument (str): Trading instrument (e.g., "EUR_USD")
            bid (float): Current bid price
            ask (float): Current ask price

        Returns:
            float: Spread in pips
        """
        spread = ask - bid

        # Convert spread to pips based on instrument
        if "_JPY" in instrument:
            # JPY pairs have 2 decimal places, so 1 pip = 0.01
            spread_in_pips = spread * 100
        else:
            # Most other pairs have 4 decimal places, so 1 pip = 0.0001
            spread_in_pips = spread * 10000

        return spread_in_pips



    def is_in_trading_session(self, trading_sessions: List[str]) -> Dict[str, Any]:
        """
        Check if the current time is within the specified trading sessions.

        Args:
            trading_sessions (List[str]): List of trading sessions to check

        Returns:
            Dict[str, Any]: Trading session information
        """
        # If bypass_market_is_closed is enabled, always return in session
        if self.bypass_market_is_closed:
            self.logger.log_info("BYPASS_MARKET_IS_CLOSED is enabled - reporting in trading session")
            return {
                "in_session": True,
                "active_sessions": trading_sessions or list(self.trading_centers.keys()),
                "requested_sessions": trading_sessions,
                "current_time_utc": datetime.now(timezone.utc).isoformat()
            }

        # If no trading sessions specified, assume all sessions are valid
        if not trading_sessions:
            return {
                "in_session": True,
                "active_sessions": list(self.trading_centers.keys()),
                "current_time_utc": datetime.now(timezone.utc).isoformat()
            }

        # Get current UTC time
        now = datetime.now(timezone.utc)
        current_hour = now.hour

        # Check if current time is in any of the specified trading sessions
        active_sessions = []
        for session in trading_sessions:
            if session == "All":
                # If "All" is specified, consider all sessions valid
                return {
                    "in_session": True,
                    "active_sessions": ["All"],
                    "current_time_utc": now.isoformat()
                }

            # Check if the specified session is valid
            if session in self.trading_centers:
                center = self.trading_centers[session]
                # Handle centers that cross UTC day boundary
                if center["open"] > center["close"]:
                    if current_hour >= center["open"] or current_hour < center["close"]:
                        active_sessions.append(session)
                else:
                    if center["open"] <= current_hour < center["close"]:
                        active_sessions.append(session)

        # Determine if we're in any of the specified sessions
        in_session = len(active_sessions) > 0

        return {
            "in_session": in_session,
            "active_sessions": active_sessions,
            "requested_sessions": trading_sessions,
            "current_time_utc": now.isoformat()
        }

    def get_market_conditions(self, instrument: str, bid: float, ask: float, trading_sessions: List[str] = None, avoid_high_spread: bool = True) -> Dict[str, Any]:
        """
        Get comprehensive market conditions including market hours and spread.

        Args:
            instrument (str): Trading instrument (e.g., "EUR_USD")
            bid (float): Current bid price
            ask (float): Current ask price
            trading_sessions (List[str], optional): List of trading sessions to check
            avoid_high_spread (bool, optional): Whether to avoid trading when spread is too high. Defaults to True.

        Returns:
            Dict[str, Any]: Comprehensive market conditions
        """
        # If bypass_market_is_closed is enabled, always return safe to trade
        if self.bypass_market_is_closed:
            self.logger.log_info("BYPASS_MARKET_IS_CLOSED is enabled - reporting safe market conditions")
            # Still check spread for informational purposes
            spread_info = self.check_spread(instrument, bid, ask)

            # Create a default session info if trading sessions are specified
            session_info = None
            if trading_sessions:
                session_info = {
                    "in_session": True,
                    "active_sessions": trading_sessions,
                    "requested_sessions": trading_sessions,
                    "current_time_utc": datetime.now(timezone.utc).isoformat()
                }

            # Get market hours with bypass enabled
            market_hours = self.is_market_open()

            return {
                "instrument": instrument,
                "is_safe_to_trade": True,
                "reason": None,
                "market_hours": market_hours,
                "spread_info": spread_info,
                "session_info": session_info,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        # Normal processing when bypass is not enabled
        # Check market hours
        market_hours = self.is_market_open()

        # Check spread
        spread_info = self.check_spread(instrument, bid, ask)

        # Check trading session if specified
        session_info = None
        if trading_sessions:
            session_info = self.is_in_trading_session(trading_sessions)

        # Determine if it's safe to trade based on all conditions
        is_safe_to_trade = market_hours["is_open"]

        # Add spread check if avoid_high_spread is enabled
        if avoid_high_spread:
            is_safe_to_trade = is_safe_to_trade and spread_info["is_acceptable"]

        # Add trading session check if specified
        if session_info:
            is_safe_to_trade = is_safe_to_trade and session_info["in_session"]

        # Determine reason if not safe to trade
        reason = None
        if not is_safe_to_trade:
            if not market_hours["is_open"]:
                reason = market_hours["reason"]
            elif session_info and not session_info["in_session"]:
                reason = f"Outside trading session: {', '.join(session_info['requested_sessions'])}"
            elif avoid_high_spread and not spread_info["is_acceptable"]:
                reason = f"Spread too wide ({spread_info['spread_in_pips']} pips > {spread_info['threshold']} pips threshold)"

        return {
            "instrument": instrument,
            "is_safe_to_trade": is_safe_to_trade,
            "reason": reason,
            "market_hours": market_hours,
            "spread_info": spread_info,
            "session_info": session_info,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
