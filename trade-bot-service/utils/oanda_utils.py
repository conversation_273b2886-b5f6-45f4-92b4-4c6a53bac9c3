import os

def get_oanda_url() -> str:
    """
    Get the OANDA API URL based on environment.
    
    Returns:
        str: The OANDA API base URL for either practice or live environment
    """
    # Check if using practice environment
    is_practice = os.getenv("OANDA_PRACTICE", "true").lower() == "true"
    
    if is_practice:
        return "https://api-fxpractice.oanda.com/v3"
    else:
        return "https://api-fxtrade.oanda.com/v3" 