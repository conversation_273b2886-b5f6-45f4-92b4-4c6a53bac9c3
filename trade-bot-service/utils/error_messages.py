"""Utility module for generating user-friendly error messages."""

import re
from typing import Dict, Optional, Any

def get_user_friendly_error_message(technical_error: str) -> str:
    """
    Convert a technical error message to a user-friendly one.

    Args:
        technical_error (str): The technical error message

    Returns:
        str: A user-friendly error message
    """
    # Network connectivity errors
    if any(term in technical_error.lower() for term in [
        "connection error",
        "connecttimeout",
        "readtimeout",
        "timeout",
        "connection refused",
        "name resolution",
        "nodename nor servname provided",
        "network unreachable",
        "failed to resolve",
        "no route to host"
    ]):
        return "Unable to connect to our data provider."

    # API rate limiting
    if any(term in technical_error.lower() for term in ["rate limit", "429", "too many requests"]):
        return "Our data provider is currently busy. The bot will automatically retry in a moment."

    # Server errors
    if any(term in technical_error.lower() for term in ["500", "502", "503", "504", "server error"]):
        return "Our data provider is experiencing technical difficulties. The bot will automatically retry in a moment."

    # Authentication errors
    if any(term in technical_error.lower() for term in ["unauthorized", "authentication", "auth", "401", "403"]):
        return "There was an issue with your account authentication. Please check your API keys."

    # No data available
    if any(term in technical_error.lower() for term in ["no candles", "no data", "empty response"]):
        return "No market data is available for the selected instrument and timeframe."

    # Market closed
    if "market is closed" in technical_error.lower():
        return "The market is currently closed. The bot will automatically resume when the market opens."

    # Insufficient margin
    if "insufficient margin" in technical_error.lower():
        return "Insufficient margin in your account. Please edit your strategy to reduce the risk percentage or lot size, or add more funds to your account before restarting the bot."

    # Default message for unknown errors
    return "There was an issue retrieving market data. The bot will automatically retry."

def format_error_for_logs(error: Any, context: Optional[str] = None) -> Dict[str, Any]:
    """
    Format an error for logging purposes.

    Args:
        error (Any): The error object or message
        context (Optional[str]): Additional context about the error

    Returns:
        Dict[str, Any]: Formatted error information
    """
    error_info = {
        "user_message": get_user_friendly_error_message(str(error)),
        "technical_message": str(error),
        "context": context
    }

    if hasattr(error, "__dict__"):
        error_info["details"] = error.__dict__

    return error_info
