"""
Recovery manager for handling interrupted operations.
"""

import os
import json
import time
import threading
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timezone, timedelta

from utils.logger import Logger
from utils.retry_utils import with_retry, RetryableError

class RecoveryManager:
    """
    Manager for recovering from interrupted operations.
    
    This class provides:
    - Persistent state tracking for operations
    - Automatic recovery of interrupted operations
    - Checkpointing for long-running operations
    """
    
    def __init__(self, strategy_id: str, recovery_dir: str = "/tmp/trade_bot_recovery", logger=None):
        """
        Initialize the recovery manager.
        
        Args:
            strategy_id: Strategy ID
            recovery_dir: Directory for recovery files
            logger: Logger instance
        """
        self.strategy_id = strategy_id
        self.recovery_dir = recovery_dir
        self.logger = logger or Logger("RecoveryManager")
        self.recovery_file = os.path.join(recovery_dir, f"{strategy_id}_recovery.json")
        self.checkpoint_file = os.path.join(recovery_dir, f"{strategy_id}_checkpoint.json")
        self.lock = threading.Lock()
        
        # Create recovery directory if it doesn't exist
        os.makedirs(recovery_dir, exist_ok=True)
        
        # Load existing recovery data
        self.recovery_data = self._load_recovery_data()
        self.checkpoint_data = self._load_checkpoint_data()
    
    def _load_recovery_data(self) -> Dict[str, Any]:
        """
        Load recovery data from file.
        
        Returns:
            Dict[str, Any]: Recovery data
        """
        try:
            if os.path.exists(self.recovery_file):
                with open(self.recovery_file, "r") as f:
                    data = json.load(f)
                self.logger.log_info(f"Loaded recovery data from {self.recovery_file}")
                return data
        except Exception as e:
            self.logger.log_error(f"Error loading recovery data: {str(e)}")
        
        return {
            "strategy_id": self.strategy_id,
            "pending_operations": [],
            "completed_operations": [],
            "last_updated": datetime.now(timezone.utc).isoformat()
        }
    
    def _load_checkpoint_data(self) -> Dict[str, Any]:
        """
        Load checkpoint data from file.
        
        Returns:
            Dict[str, Any]: Checkpoint data
        """
        try:
            if os.path.exists(self.checkpoint_file):
                with open(self.checkpoint_file, "r") as f:
                    data = json.load(f)
                self.logger.log_info(f"Loaded checkpoint data from {self.checkpoint_file}")
                return data
        except Exception as e:
            self.logger.log_error(f"Error loading checkpoint data: {str(e)}")
        
        return {
            "strategy_id": self.strategy_id,
            "last_checkpoint": None,
            "state": {},
            "last_updated": datetime.now(timezone.utc).isoformat()
        }
    
    def _save_recovery_data(self):
        """Save recovery data to file."""
        try:
            with self.lock:
                # Update timestamp
                self.recovery_data["last_updated"] = datetime.now(timezone.utc).isoformat()
                
                with open(self.recovery_file, "w") as f:
                    json.dump(self.recovery_data, f, indent=2)
                self.logger.log_debug(f"Saved recovery data to {self.recovery_file}")
        except Exception as e:
            self.logger.log_error(f"Error saving recovery data: {str(e)}")
    
    def _save_checkpoint_data(self):
        """Save checkpoint data to file."""
        try:
            with self.lock:
                # Update timestamp
                self.checkpoint_data["last_updated"] = datetime.now(timezone.utc).isoformat()
                
                with open(self.checkpoint_file, "w") as f:
                    json.dump(self.checkpoint_data, f, indent=2)
                self.logger.log_debug(f"Saved checkpoint data to {self.checkpoint_file}")
        except Exception as e:
            self.logger.log_error(f"Error saving checkpoint data: {str(e)}")
    
    def register_operation(self, operation_id: str, operation_type: str, params: Dict[str, Any]) -> bool:
        """
        Register an operation for recovery.
        
        Args:
            operation_id: Unique operation ID
            operation_type: Type of operation
            params: Operation parameters
            
        Returns:
            bool: True if operation was registered, False if it already exists
        """
        with self.lock:
            # Check if operation already exists
            for op in self.recovery_data["pending_operations"]:
                if op["operation_id"] == operation_id:
                    self.logger.log_warning(f"Operation {operation_id} already registered")
                    return False
            
            # Add operation to pending operations
            operation = {
                "operation_id": operation_id,
                "operation_type": operation_type,
                "params": params,
                "status": "pending",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.recovery_data["pending_operations"].append(operation)
            self._save_recovery_data()
            
            self.logger.log_info(f"Registered operation {operation_id} of type {operation_type}")
            return True
    
    def complete_operation(self, operation_id: str, result: Optional[Dict[str, Any]] = None) -> bool:
        """
        Mark an operation as completed.
        
        Args:
            operation_id: Operation ID
            result: Operation result
            
        Returns:
            bool: True if operation was completed, False if it wasn't found
        """
        with self.lock:
            # Find operation in pending operations
            for i, op in enumerate(self.recovery_data["pending_operations"]):
                if op["operation_id"] == operation_id:
                    # Update operation status
                    op["status"] = "completed"
                    op["updated_at"] = datetime.now(timezone.utc).isoformat()
                    op["result"] = result
                    
                    # Move to completed operations
                    self.recovery_data["completed_operations"].append(op)
                    self.recovery_data["pending_operations"].pop(i)
                    
                    self._save_recovery_data()
                    
                    self.logger.log_info(f"Completed operation {operation_id}")
                    return True
            
            self.logger.log_warning(f"Operation {operation_id} not found in pending operations")
            return False
    
    def fail_operation(self, operation_id: str, error: str) -> bool:
        """
        Mark an operation as failed.
        
        Args:
            operation_id: Operation ID
            error: Error message
            
        Returns:
            bool: True if operation was marked as failed, False if it wasn't found
        """
        with self.lock:
            # Find operation in pending operations
            for i, op in enumerate(self.recovery_data["pending_operations"]):
                if op["operation_id"] == operation_id:
                    # Update operation status
                    op["status"] = "failed"
                    op["updated_at"] = datetime.now(timezone.utc).isoformat()
                    op["error"] = error
                    
                    # Move to completed operations
                    self.recovery_data["completed_operations"].append(op)
                    self.recovery_data["pending_operations"].pop(i)
                    
                    self._save_recovery_data()
                    
                    self.logger.log_warning(f"Failed operation {operation_id}: {error}")
                    return True
            
            self.logger.log_warning(f"Operation {operation_id} not found in pending operations")
            return False
    
    def get_pending_operations(self, operation_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get pending operations.
        
        Args:
            operation_type: Filter by operation type
            
        Returns:
            List[Dict[str, Any]]: List of pending operations
        """
        with self.lock:
            if operation_type:
                return [op for op in self.recovery_data["pending_operations"] if op["operation_type"] == operation_type]
            else:
                return self.recovery_data["pending_operations"].copy()
    
    def get_completed_operations(self, operation_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get completed operations.
        
        Args:
            operation_type: Filter by operation type
            limit: Maximum number of operations to return
            
        Returns:
            List[Dict[str, Any]]: List of completed operations
        """
        with self.lock:
            if operation_type:
                return [op for op in self.recovery_data["completed_operations"] if op["operation_type"] == operation_type][-limit:]
            else:
                return self.recovery_data["completed_operations"][-limit:]
    
    def checkpoint(self, state: Dict[str, Any], checkpoint_id: Optional[str] = None) -> str:
        """
        Create a checkpoint for the current state.
        
        Args:
            state: Current state
            checkpoint_id: Optional checkpoint ID
            
        Returns:
            str: Checkpoint ID
        """
        with self.lock:
            # Generate checkpoint ID if not provided
            if not checkpoint_id:
                checkpoint_id = f"checkpoint_{int(time.time())}"
            
            # Update checkpoint data
            self.checkpoint_data["last_checkpoint"] = checkpoint_id
            self.checkpoint_data["state"] = state
            
            self._save_checkpoint_data()
            
            self.logger.log_info(f"Created checkpoint {checkpoint_id}")
            return checkpoint_id
    
    def get_last_checkpoint(self) -> Optional[Dict[str, Any]]:
        """
        Get the last checkpoint.
        
        Returns:
            Optional[Dict[str, Any]]: Last checkpoint data or None if no checkpoint exists
        """
        with self.lock:
            if self.checkpoint_data["last_checkpoint"]:
                return {
                    "checkpoint_id": self.checkpoint_data["last_checkpoint"],
                    "state": self.checkpoint_data["state"],
                    "last_updated": self.checkpoint_data["last_updated"]
                }
            return None
    
    def cleanup_old_operations(self, max_age: timedelta = timedelta(days=7)):
        """
        Clean up old completed operations.
        
        Args:
            max_age: Maximum age of operations to keep
        """
        with self.lock:
            now = datetime.now(timezone.utc)
            operations_to_remove = []
            
            for i, op in enumerate(self.recovery_data["completed_operations"]):
                try:
                    updated_at = datetime.fromisoformat(op["updated_at"])
                    if now - updated_at > max_age:
                        operations_to_remove.append(i)
                except (ValueError, KeyError):
                    # If we can't parse the time, keep the operation
                    pass
            
            # Remove operations from newest to oldest to maintain correct indices
            for i in sorted(operations_to_remove, reverse=True):
                self.recovery_data["completed_operations"].pop(i)
            
            if operations_to_remove:
                self._save_recovery_data()
                self.logger.log_info(f"Cleaned up {len(operations_to_remove)} old operations")
    
    def recover_pending_operations(self, handlers: Dict[str, Callable[[Dict[str, Any]], Any]]) -> int:
        """
        Recover pending operations.
        
        Args:
            handlers: Dictionary mapping operation types to handler functions
            
        Returns:
            int: Number of operations recovered
        """
        pending_operations = self.get_pending_operations()
        recovered_count = 0
        
        for op in pending_operations:
            operation_id = op["operation_id"]
            operation_type = op["operation_type"]
            params = op["params"]
            
            if operation_type in handlers:
                try:
                    self.logger.log_info(f"Recovering operation {operation_id} of type {operation_type}")
                    result = handlers[operation_type](params)
                    self.complete_operation(operation_id, result)
                    recovered_count += 1
                except Exception as e:
                    self.logger.log_error(f"Error recovering operation {operation_id}: {str(e)}")
                    self.fail_operation(operation_id, str(e))
            else:
                self.logger.log_warning(f"No handler for operation type {operation_type}")
        
        return recovered_count
    
    @with_retry(max_retries=3, base_delay=1.0, max_delay=10.0)
    def execute_with_recovery(self, operation_id: str, operation_type: str, 
                             func: Callable, params: Dict[str, Any], *args, **kwargs) -> Any:
        """
        Execute a function with recovery.
        
        Args:
            operation_id: Unique operation ID
            operation_type: Type of operation
            func: Function to execute
            params: Operation parameters (for recovery)
            *args: Additional arguments to pass to the function
            **kwargs: Additional keyword arguments to pass to the function
            
        Returns:
            Any: Result of the function
            
        Raises:
            Exception: If the function fails and cannot be retried
        """
        # Register operation
        self.register_operation(operation_id, operation_type, params)
        
        try:
            # Execute function
            result = func(*args, **kwargs)
            
            # Mark operation as completed
            self.complete_operation(operation_id, result if isinstance(result, dict) else {"result": str(result)})
            
            return result
        except Exception as e:
            # Mark operation as failed
            self.fail_operation(operation_id, str(e))
            
            # Re-raise the exception
            raise
