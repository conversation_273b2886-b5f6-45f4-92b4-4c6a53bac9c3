{"name": "EMA, RSI and SMA", "description": "", "instruments": "EUR/USD", "timeframe": "1m", "tradingSession": ["All"], "indicators": [{"id": "17463155718150j4abebs2hs", "indicator_class": "RSI", "type": "RSI", "parameters": {"period": 14}, "source": "price"}, {"id": "17463155757414jhu5y21tt8", "indicator_class": "SMA", "type": "SMA", "parameters": {"period": 50}, "source": "17463155718150j4abebs2hs"}, {"id": "1746316025284u6ab7tvoq0e", "indicator_class": "EMA", "type": "EMA", "parameters": {"period": 20}, "source": "price"}], "entryRules": [{"id": "1746315587442hb6uml3mkve", "tradeType": "long", "indicator1": "17463155757414jhu5y21tt8", "operator": ">", "compareType": "value", "indicator2": "", "value": "1", "barRef": "close"}, {"id": "1746316033928vgxqqcpw98", "tradeType": "long", "indicator1": "1746316025284u6ab7tvoq0e", "operator": ">", "compareType": "value", "indicator2": "", "value": "50", "barRef": "close"}], "exitRules": [{"id": "1746315596604ittq3x9iui", "tradeType": "long", "indicator1": "17463155757414jhu5y21tt8", "operator": ">", "compareType": "value", "indicator2": "", "value": "90", "barRef": "close"}], "riskManagement": {"stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage", "riskPerTrade": "1%", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%"}, "entryLongGroupOperator": "AND", "user_id": "hUli47EgkKnHdyryQhzTyvn7ehxp", "id": "6So7BsiPSbNoR0bqz0Vq"}