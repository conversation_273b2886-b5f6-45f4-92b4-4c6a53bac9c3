#!/bin/bash

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Error: .env file not found. Please create one with your configuration."
    exit 1
fi

# Load environment variables from .env file
set -a
source .env
set +a

# Check if config directory exists
if [ ! -d "config" ]; then
    echo "Error: config directory not found. Please ensure it exists with engine_config.json."
    exit 1
fi

# Check if required config files exist
if [ ! -f "config/engine_config.json" ]; then
    echo "Error: Required configuration file missing."
    echo "Please ensure engine_config.json exists."
    exit 1
fi

# Check if Firebase emulator is running
if [ "$(lsof -i :8082)" == "" ]; then
    echo "Error: Firebase emulator not running. Please start it first with 'firebase emulators:start'"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
else
    source venv/bin/activate
fi

# Add current directory to PYTHONPATH
export PYTHONPATH=$PYTHONPATH:$(pwd)

# Enable health API
export ENABLE_HEALTH_API=true
export HEALTH_API_PORT=8001

# Run the trade bot
echo "Starting trade bot with health API on port 8001..."
python main.py