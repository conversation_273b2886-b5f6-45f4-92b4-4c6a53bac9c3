# Enhanced Market Data Provider

## Overview

The Enhanced Market Data Provider implements a **GCS + Polygon hybrid approach** for efficient candle data fetching in the trade-bot-service. This system significantly improves performance and reduces API costs by intelligently combining Google Cloud Storage (GCS) historical data with Polygon.io real-time updates.

## Architecture

### Components

1. **HybridMarketDataWrapper** - Main interface that intelligently selects providers
2. **EnhancedMarketDataProvider** - GCS + Polygon hybrid implementation  
3. **MarketDataProvider** - Basic Polygon-only fallback implementation

### Data Flow

```
Request for Candles
        ↓
HybridMarketDataWrapper
        ↓
┌─────────────────────┐    ┌──────────────────────┐
│ EnhancedProvider    │    │ BasicProvider        │
│ (GCS + Polygon)     │    │ (Polygon Only)       │
│                     │    │                      │
│ 1. Check Cache      │    │ 1. Direct API Call  │
│ 2. Try GCS          │    │ 2. Format Response   │
│ 3. Fallback Polygon │    │                      │
└─────────────────────┘    └──────────────────────┘
        ↓                           ↓
    Success/Error              Success/Error
        ↓                           ↓
    Performance Tracking & Auto-Fallback
```

## Benefits

### Performance Improvements
- **~90% reduction in Firestore writes** (removed redundant indicator storage)
- **Faster data loading** from GCS cache vs API calls
- **Intelligent caching** with 5-minute TTL for frequently accessed data
- **Automatic provider switching** based on performance metrics

### Cost Optimization
- **Reduced Polygon API calls** through GCS caching
- **Lower Firestore costs** by eliminating chart data storage
- **Efficient data transfer** using JSONL format in GCS

### Reliability
- **Automatic fallback** from GCS to Polygon API
- **Error tracking** and provider health monitoring
- **Graceful degradation** when GCS is unavailable

## Configuration

### Environment Variables

```bash
# Enable/disable enhanced provider
USE_ENHANCED_MARKET_DATA=true

# GCS configuration
GOOGLE_CLOUD_PROJECT=your-project-id
HISTORICAL_DATA_BUCKET=oryntrade-forex-candles-data

# Polygon API
POLYGON_API_KEY=your_polygon_api_key
```

### Provider Selection Logic

The system automatically selects the best provider based on:
- **Availability**: GCS client initialization status
- **Performance**: Success/error rates and execution times
- **Configuration**: `USE_ENHANCED_MARKET_DATA` environment variable
- **Error Threshold**: Switches to fallback after 3 consecutive errors

## Usage

### Basic Usage

```python
from data.hybrid_market_data import HybridMarketDataWrapper

# Initialize wrapper
wrapper = HybridMarketDataWrapper(api_key="your_polygon_key")

# Fetch candles (automatically selects best provider)
result = wrapper.get_candles(
    symbol="EUR/USD",
    timespan="5m", 
    count=1000
)

if result["status"] == "success":
    candles = result["candles"]
    source = result["source"]  # "gcs", "cache", "polygon_fallback"
    print(f"Got {len(candles)} candles from {source}")
```

### Performance Monitoring

```python
# Get performance metrics
metrics = wrapper.get_performance_metrics()
print(f"Enhanced provider success rate: {metrics['enhanced']['success_count']}")
print(f"Average execution time: {metrics['enhanced']['avg_time']:.2f}s")
```

## Data Sources

### GCS Historical Data
- **Format**: JSONL files organized by forex pair, timeframe, and date
- **Structure**: `{forex_pair}/{timeframe}/{forex_pair}_{timeframe}_{date}.jsonl`
- **Example**: `EURUSD/5m/EURUSD_5m_2024-01-15.jsonl`
- **Coverage**: Up to 1 year of historical data per timeframe

### Polygon API Real-time
- **Endpoint**: `/v2/aggs/ticker/{symbol}/range/{multiplier}/{timespan}/{from}/{to}`
- **Rate Limit**: 5 requests per minute (handled by retry logic)
- **Fallback**: Used when GCS data is insufficient or unavailable

## Testing

### Run Tests

```bash
# Set environment variables
export POLYGON_API_KEY="your_api_key"
export GOOGLE_CLOUD_PROJECT="your_project_id"

# Run test script
python test_enhanced_market_data.py
```

### Expected Output

```
Enhanced Market Data Provider Test
==================================================
✅ Loaded environment variables from .env file
🚀 Testing Enhanced Market Data Provider
Initializing hybrid market data wrapper...

📊 Test Case 1: {'symbol': 'EUR/USD', 'timespan': '5m', 'count': 100}
✅ Success: 100 candles from gcs_websocket_hybrid
⏱️ Execution time: 1.23s
📅 Date range: 2024-01-15 09:00:00+00:00 to 2024-01-15 17:15:00+00:00
💰 Price range: 1.08945 to 1.09123

🎉 All tests passed!
```

## Migration from Basic Provider

The migration is **seamless** and **backward compatible**:

1. **Automatic Detection**: System detects GCS availability and falls back gracefully
2. **Same Interface**: All existing code continues to work without changes  
3. **Gradual Rollout**: Can be enabled/disabled via environment variable
4. **Performance Monitoring**: Built-in metrics track improvement

## Troubleshooting

### Common Issues

1. **GCS Authentication Errors**
   ```bash
   # Fix: Authenticate with Google Cloud
   gcloud auth application-default login
   ```

2. **Missing Historical Data**
   - Check bucket name and permissions
   - Verify data exists for requested timeframe/date range
   - System will automatically fallback to Polygon API

3. **Performance Issues**
   - Monitor metrics via `get_performance_metrics()`
   - Check consecutive error counts
   - Verify network connectivity to GCS

### Logs

Key log messages to monitor:
- `"Enhanced Market Data Provider initialized"` - Successful startup
- `"Candles fetched from: gcs_websocket_hybrid"` - Using GCS data
- `"GCS data not available, falling back to Polygon API"` - Fallback triggered
- `"Enhanced provider has too many errors, using basic provider"` - Provider switch

## Future Enhancements

1. **WebSocket Integration**: Real-time updates for current candle
2. **Intelligent Prefetching**: Predict and cache likely-needed data
3. **Compression**: Reduce GCS storage costs with data compression
4. **Multi-Region**: Distribute GCS data across regions for lower latency
