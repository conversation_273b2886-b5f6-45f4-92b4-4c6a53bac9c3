# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set a working directory inside the container
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file first to leverage Docker cache
COPY requirements.txt .

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir fastapi uvicorn

# Copy the rest of your trade bot code into the container
COPY . .

# Set the Firebase credentials path
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/firebase-key/firebase-key.json

# Make the start script executable
RUN chmod +x /app/start.sh

# Expose ports for the main bot and the health API
EXPOSE 8001

# Run the trade bot with the API server
CMD ["/app/start.sh"]