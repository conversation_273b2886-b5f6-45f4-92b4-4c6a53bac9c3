# Trading Bot Service Environment Variables

# Required API Keys
POLYGON_API_KEY=your_polygon_api_key_here
USER_ID=your_user_id_here
STRATEGY_ID=your_strategy_id_here

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your_project_id
HISTORICAL_DATA_BUCKET=oryntrade-forex-candles-data

# Market Data Provider Configuration
USE_ENHANCED_MARKET_DATA=true  # Set to false to use basic Polygon-only provider
BYPASS_MARKET_IS_CLOSED=false  # Set to true for development/testing

# Firebase Configuration
USE_FIREBASE_EMULATOR=false
FIRESTORE_EMULATOR_HOST=127.0.0.1:8082

# OANDA Configuration
OANDA_PRACTICE_MODE=true  # Set to false for live trading

# Strategy Controller
STRATEGY_CONTROLLER_URL=http://strategy-controller:8080

# Logging
LOG_LEVEL=INFO
