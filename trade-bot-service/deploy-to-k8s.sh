#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Configuration
PROJECT_ID="oryntrade"
SERVICE_NAME="trade-bot"
REPOSITORY="oryn-containers"
IMAGE_NAME="us-central1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${SERVICE_NAME}:latest"

echo "🚀 Starting deployment process for ${SERVICE_NAME}"

# Build the Docker image for linux/amd64 platform
echo "🏗️ Building Docker image for linux/amd64 platform..."
docker buildx build --platform linux/amd64 \
    -t ${IMAGE_NAME} \
    --push \
    .

echo "✅ Trade Bot image has been built and pushed to the container registry"

# Delete existing trade bot pods to force them to use the new image
echo "🔄 Deleting existing trade bot pods to use new image..."
kubectl delete pods -l app=trade-bot

echo "✅ Deployment completed successfully!"
echo "✅ The new image is available at: ${IMAGE_NAME}"
echo "✅ Existing trade bot pods have been deleted"
echo "📝 Note: New pods will be created with the updated image when you submit a new strategy"
echo "🔍 To test the new image, you can submit a strategy through the Strategy Controller API" 