"""
Fast update loop for the trading bot.

This module contains the FastUpdateLoop class, which is responsible for updating
time-sensitive data at a higher frequency than the main strategy loop.
"""

import time
import threading
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from core.trading_engine import TradingEngine
from execution.oanda_client import OandaClient
from execution.firebase_client import FirebaseClient
from utils.logger import Logger


class FastUpdateLoop:
    """
    Fast update loop for the trading bot.

    This class is responsible for updating time-sensitive data at a higher frequency
    than the main strategy loop, including:
    - Unrealized P&L for open trades
    - Take profit and stop loss prices for new trades
    - Risk management thresholds
    """

    def __init__(
        self,
        trading_engine: TradingEngine,
        oanda_client: OandaClient,
        firebase_client: FirebaseClient,
        update_interval: int = 5,  # Default to 5 seconds
    ):
        """
        Initialize the fast update loop.

        Args:
            trading_engine: Trading engine instance
            oanda_client: OANDA client instance
            firebase_client: Firebase client instance
            update_interval: Update interval in seconds (default: 5)
        """
        self.trading_engine = trading_engine
        self.oanda_client = oanda_client
        self.firebase_client = firebase_client
        self.update_interval = update_interval
        self.logger = Logger("FastUpdateLoop")

        self.running = False
        self.thread = None
        self.last_update_time = None

        # Risk management thresholds
        self.daily_loss_limit = None
        self.total_profit_target = None
        self.total_loss_limit = None

        # Initialize risk management thresholds from trading engine
        self._initialize_risk_thresholds()

    def _initialize_risk_thresholds(self):
        """Initialize risk management thresholds from trading engine."""
        self.daily_loss_limit = self.trading_engine.daily_loss_limit
        self.total_profit_target = self.trading_engine.total_profit_target
        self.total_loss_limit = self.trading_engine.total_loss_limit

        self.logger.log_info(
            f"Initialized risk thresholds: "
            f"daily_loss_limit={self.daily_loss_limit}%, "
            f"total_profit_target={self.total_profit_target}%, "
            f"total_loss_limit={self.total_loss_limit}%"
        )

    def start(self):
        """Start the fast update loop."""
        if self.running:
            self.logger.log_warning("Fast update loop is already running")
            return

        self.running = True
        self.thread = threading.Thread(target=self._run_loop)
        self.thread.daemon = True
        self.thread.start()

        self.logger.log_info(f"Fast update loop started with interval {self.update_interval}s")

    def stop(self):
        """Stop the fast update loop."""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
            self.thread = None

        self.logger.log_info("Fast update loop stopped")

    def _run_loop(self):
        """Run the fast update loop."""
        while self.running:
            try:
                self._perform_fast_updates()
                self.last_update_time = datetime.now(timezone.utc)
            except Exception as e:
                self.logger.log_error(e, "Error in fast update loop")

            # Sleep for the update interval
            time.sleep(self.update_interval)

    def _perform_fast_updates(self):
        """Perform fast updates."""
        try:
            # Get open trades from OANDA - this will have the latest unrealized P&L values and TP/SL prices
            open_trades = self.oanda_client.get_open_trades()

            if open_trades:
                self.logger.log_info(f"Updating information for {len(open_trades)} open trades")

                # Update open trades in Firebase - this will update unrealized P&L, TP/SL prices, and other trade data
                self.firebase_client.update_open_trades(open_trades)

                # Log the updated unrealized P&L
                total_unrealized_pl = sum(float(trade.unrealizedPL) for trade in open_trades)
                self.logger.log_info(f"Total unrealized P&L: {total_unrealized_pl:.2f}")

                # Log individual trade details for debugging
                for trade in open_trades:
                    # Log basic trade information
                    trade_info = (
                        f"Trade {trade.tradeID}: {trade.instrument}, {trade.type.value}, "
                        f"Units: {trade.units}, Entry: {trade.price}, "
                        f"Unrealized P&L: {trade.unrealizedPL}"
                    )

                    # Log TP/SL information
                    if not trade.takeProfitPrice or not trade.stopLossPrice:
                        self.logger.log_info(f"{trade_info} - Missing TP/SL prices")
                    else:
                        self.logger.log_info(
                            f"{trade_info} - TP: {trade.takeProfitPrice}, SL: {trade.stopLossPrice}"
                        )
            else:
                self.logger.log_info("No open trades to update")

        except Exception as e:
            self.logger.log_error(e, "Error updating trade information")

        # Check risk management thresholds
        self._check_risk_thresholds()

    def _check_risk_thresholds(self):
        """
        Check risk management thresholds including unrealized P&L.

        This method checks if any risk thresholds have been exceeded,
        considering both realized and unrealized P&L.

        Returns:
            Dict[str, Any]: Risk check result with status and message
        """
        try:
            # Get account summary
            account_summary = self.oanda_client.get_account_summary()
            if not account_summary:
                self.logger.log_warning("Failed to get account summary, skipping risk check")
                return

            # Get account balance
            account_balance = float(account_summary.get('balance', 0))
            if account_balance <= 0:
                self.logger.log_warning("Invalid account balance, skipping risk check")
                return

            # Get open trades
            open_trades = self.oanda_client.get_open_trades()

            # Calculate total unrealized P&L
            total_unrealized_pl = sum(float(trade.unrealizedPL) for trade in open_trades)

            # Get total realized P&L from trading engine
            total_realized_pl = self.trading_engine.total_profit - self.trading_engine.total_loss

            # Calculate total P&L (realized + unrealized)
            total_pl = total_realized_pl + total_unrealized_pl

            # Calculate total P&L as percentage of account balance
            total_pl_pct = (total_pl / account_balance) * 100

            self.logger.log_info(
                f"Risk check: total_realized_pl={total_realized_pl:.2f}, "
                f"total_unrealized_pl={total_unrealized_pl:.2f}, "
                f"total_pl={total_pl:.2f} ({total_pl_pct:.2f}%)"
            )

            # Check total profit target
            if self.total_profit_target and total_pl_pct >= self.total_profit_target:
                self.logger.log_warning(
                    f"Total profit target reached: {total_pl_pct:.2f}% >= {self.total_profit_target}%"
                )
                self.firebase_client.append_user_log(
                    f"🎯 Total profit target of {self.total_profit_target}% reached! Bot will stop."
                )

                # Update bot status
                self.firebase_client.update_bot_status(
                    "STOPPING",
                    {"message": f"Total profit target of {self.total_profit_target}% reached"}
                )

                # Signal the main bot to stop
                self._signal_bot_to_stop()

                return {
                    "status": "total_profit_target_reached",
                    "message": f"Total profit target of {self.total_profit_target}% reached"
                }

            # Check total loss limit
            if self.total_loss_limit and total_pl_pct <= -self.total_loss_limit:
                self.logger.log_warning(
                    f"Total loss limit exceeded: {total_pl_pct:.2f}% <= -{self.total_loss_limit}%"
                )
                self.firebase_client.append_user_log(
                    f"⛔ Total loss limit of {self.total_loss_limit}% exceeded. Bot will stop."
                )

                # Update bot status
                self.firebase_client.update_bot_status(
                    "STOPPING",
                    {"message": f"Total loss limit of {self.total_loss_limit}% exceeded"}
                )

                # Signal the main bot to stop
                self._signal_bot_to_stop()

                return {
                    "status": "total_loss_limit_exceeded",
                    "message": f"Total loss limit of {self.total_loss_limit}% exceeded"
                }

            # Calculate daily P&L
            # For simplicity, we'll use the trading engine's daily loss tracker
            # and add the unrealized P&L from today's open trades
            daily_loss = self.trading_engine.daily_loss

            # Add unrealized P&L from trades opened today
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            for trade in open_trades:
                try:
                    # Check if the trade was opened today
                    # Handle different formats of openTime
                    if hasattr(trade, 'openTime'):
                        # If openTime is a string, parse it
                        if isinstance(trade.openTime, str):
                            try:
                                # Try ISO format parsing
                                trade_open_time = datetime.fromisoformat(trade.openTime.replace('Z', '+00:00'))
                            except ValueError:
                                # Try other common formats
                                try:
                                    trade_open_time = datetime.strptime(trade.openTime, '%Y-%m-%dT%H:%M:%S.%fZ')
                                except ValueError:
                                    try:
                                        trade_open_time = datetime.strptime(trade.openTime, '%Y-%m-%d %H:%M:%S')
                                    except ValueError:
                                        self.logger.log_warning(f"Could not parse trade open time: {trade.openTime}")
                                        continue
                        # If openTime is already a datetime object
                        elif isinstance(trade.openTime, datetime):
                            trade_open_time = trade.openTime
                        # If openTime is a timestamp (int or float)
                        elif isinstance(trade.openTime, (int, float)):
                            trade_open_time = datetime.fromtimestamp(trade.openTime, tz=timezone.utc)
                        else:
                            self.logger.log_warning(f"Unknown type for trade open time: {type(trade.openTime)}")
                            continue
                    else:
                        # If openTime is not available, skip this trade
                        self.logger.log_warning(f"Trade has no openTime attribute: {trade}")
                        continue

                    # Now check if the trade was opened today
                    if trade_open_time >= today_start:
                        # Add this trade's unrealized P&L to the daily loss calculation
                        if hasattr(trade, 'unrealizedPL'):
                            unrealized_pl = float(trade.unrealizedPL) if isinstance(trade.unrealizedPL, (str, int, float)) else 0
                            daily_loss += unrealized_pl if unrealized_pl < 0 else 0
                except Exception as e:
                    self.logger.log_error(e, f"Error processing trade for daily loss calculation: {trade}")

            # Calculate daily loss as percentage of account balance
            daily_loss_pct = (daily_loss / account_balance) * 100 if daily_loss > 0 else 0

            # Check daily loss limit
            if self.daily_loss_limit and daily_loss_pct >= self.daily_loss_limit:
                self.logger.log_warning(
                    f"Daily loss limit exceeded: {daily_loss_pct:.2f}% >= {self.daily_loss_limit}%"
                )
                self.firebase_client.append_user_log(
                    f"⚠️ Daily loss limit of {self.daily_loss_limit}% exceeded. Trading paused for today."
                )

                # Update bot status
                self.firebase_client.update_bot_status(
                    "PAUSED",
                    {"message": f"Daily loss limit of {self.daily_loss_limit}% exceeded"}
                )

                # Signal the main bot to pause
                self._signal_bot_to_pause()

                return {
                    "status": "daily_loss_limit_exceeded",
                    "message": f"Daily loss limit of {self.daily_loss_limit}% exceeded"
                }

            return {
                "status": "ok",
                "message": "Risk thresholds not exceeded"
            }
        except Exception as e:
            self.logger.log_error(e, "Error checking risk thresholds")
            return {
                "status": "error",
                "message": f"Error checking risk thresholds: {str(e)}"
            }

    def _signal_bot_to_stop(self):
        """
        Signal the main bot to stop by creating a stop flag file.
        """
        try:
            # Create a stop flag file to signal the main bot to stop
            stop_flag_file = f"/tmp/trade_bot_stopped_{self.trading_engine.strategy_id}"
            with open(stop_flag_file, 'w') as f:
                f.write(f"Stopped at {datetime.now(timezone.utc).isoformat()} due to risk threshold")
            self.logger.log_info(f"Created stop flag file: {stop_flag_file}")
        except Exception as e:
            self.logger.log_error(e, "Error creating stop flag file")

    def _signal_bot_to_pause(self):
        """
        Signal the main bot to pause by setting a pause flag in the trading engine.
        """
        try:
            # Set the is_paused flag in the trading engine
            self.trading_engine.is_paused = True
            self.logger.log_info("Set trading engine pause flag")

            # Close all open trades
            self.trading_engine.close_and_update_open_trades()
            self.logger.log_info("Closed all open trades due to daily loss limit")
        except Exception as e:
            self.logger.log_error(e, "Error pausing trading engine")
