import logging
import time
from typing import Dict, Optional, List, Any, Union
from datetime import datetime, timedelta, timezone

from typing import Union
from data.market_data import MarketDataProvider
from data.hybrid_market_data import HybridMarketDataWrapper
from execution.oanda_client import OandaClient
from strategies.base_strategy import BaseStrategy
from utils.logger import Logger
from utils.error_messages import get_user_friendly_error_message
from data.market_data import MIN_CANDLE_COUNT
from execution.firebase_client import FirebaseClient
from execution.oanda_types import OandaExecuteTradeResponseSuccess, Trade
from core.trading_engine_types import TradeType, TradeExecutionResponse, TradeHistoryRow, TradeStatus

# Optional import for health client
try:
    from utils.health_client import HealthClient
    HEALTH_CLIENT_AVAILABLE = True
except ImportError:
    HEALTH_CLIENT_AVAILABLE = False

class TradingEngine:
    """Main trading engine that coordinates strategy execution, market data, and trade execution."""

    def __init__(
        self,
        strategy: BaseStrategy,
        market_data_provider: Union[MarketDataProvider, HybridMarketDataWrapper],
        oanda_client: OandaClient,
        user_id: str,
        strategy_id: str,
        firebase_client: FirebaseClient,
        health_client: Optional[Any] = None
    ):
        """
        Initialize the trading engine.

        Args:
            strategy (BaseStrategy): Trading strategy instance
            market_data_provider (MarketDataProvider): Market data provider instance
            oanda_client (OandaClient): OANDA API client instance
            user_id (str): User ID for Firestore
            strategy_id (str): Strategy ID for Firestore
            firebase_client (FirebaseClient): Firebase client instance
            health_client (Optional[Any]): Health client for reporting metrics
        """
        self.strategy = strategy
        self.market_data_provider = market_data_provider
        self.oanda_client = oanda_client
        self.user_id = user_id
        self.strategy_id = strategy_id
        self.firebase_client = firebase_client
        self.health_client = health_client

        # Setup logging
        self.logger = Logger("TradingEngine")

        # Initialize state
        self.last_update_time = None
        self.current_position = None

        # Risk management state
        self.daily_loss = 0.0
        self.total_profit = 0.0
        self.total_loss = 0.0
        self.daily_loss_limit = None
        self.max_position_size_pct = None
        self.stop_loss_pct = None
        self.take_profit_pct = None
        self.risk_percentage_pct = None
        self.runtime_days = None
        self.total_profit_target = None
        self.total_loss_limit = None
        self.avoid_high_spread = True  # Default to True for safety
        self.start_time = datetime.now(timezone.utc)

        # Performance metrics
        self.metrics = {
            'update_cycles': 0,
            'trades_executed': 0,
            'trades_closed': 0,
            'execution_times': {}
        }

        # Initialize risk management parameters from strategy config
        self._initialize_risk_management()
        self.trade_history: List[Dict[str, Any]] = []
        self.current_positions = {}

    def _initialize_risk_management(self):
        """
        Initialize risk management parameters from strategy configuration.

        Priority order:
        1. Strategy-defined stop-loss and take-profit values
        2. Risk management configuration values
        3. Default values
        """
        try:
            # Get strategy configuration
            strategy_config = getattr(self.strategy, 'config', {})

            # Get risk management configuration
            risk_config = strategy_config.get('risk_management', {})
            if not risk_config:
                self.logger.log_info("No risk management configuration found, using defaults")

            # Check if strategy has stop-loss and take-profit defined
            has_strategy_stop_loss = hasattr(self.strategy, 'stop_loss') and self.strategy.stop_loss
            has_strategy_take_profit = hasattr(self.strategy, 'take_profit') and self.strategy.take_profit

            if has_strategy_stop_loss:
                self.logger.log_info(f"Using strategy-defined stop-loss: {self.strategy.stop_loss}")

            if has_strategy_take_profit:
                self.logger.log_info(f"Using strategy-defined take-profit: {self.strategy.take_profit}")

            # Parse risk management parameters with defaults
            self.logger.log_info("Using new risk management structure")

            # Extract risk percentage
            if hasattr(self.strategy, 'risk_percentage') and self.strategy.risk_percentage is not None:
                risk_percentage_str = self.strategy.risk_percentage
                self.risk_percentage_pct = self._parse_percentage(risk_percentage_str, 1.0)
                self.logger.log_info(f"Using risk percentage: {self.risk_percentage_pct}%")
            else:
                self.risk_percentage_pct = 1.0
                self.logger.log_info(f"Using default risk percentage: {self.risk_percentage_pct}%")

            # Extract risk reward ratio and calculate take profit
            if hasattr(self.strategy, 'risk_reward_ratio') and self.strategy.risk_reward_ratio is not None:
                risk_reward_ratio = float(self.strategy.risk_reward_ratio)
                self.logger.log_info(f"Using risk reward ratio: {risk_reward_ratio}")
            else:
                risk_reward_ratio = 2.0
                self.logger.log_info(f"Using default risk reward ratio: {risk_reward_ratio}")

            # Extract stop loss method
            if hasattr(self.strategy, 'stop_loss_method'):
                stop_loss_method = self.strategy.stop_loss_method
                self.logger.log_info(f"Using stop loss method: {stop_loss_method}")

                # Handle different stop loss methods
                if stop_loss_method == "fixed":
                    if hasattr(self.strategy, 'fixed_pips') and self.strategy.fixed_pips is not None:
                        fixed_pips = float(self.strategy.fixed_pips)
                        self.logger.log_info(f"Using fixed pips: {fixed_pips}")
                        # Set stop loss in pips
                        self.stop_loss_pct = fixed_pips / 100.0  # Convert pips to percentage
                        stop_loss_unit = "pips"
                    else:
                        self.stop_loss_pct = 2.0  # Default to 2%
                        stop_loss_unit = "percentage"
                elif stop_loss_method == "indicator":
                    # For indicator-based stop loss, use a default percentage for now
                    self.stop_loss_pct = 2.0
                    stop_loss_unit = "percentage"
                elif stop_loss_method == "risk":
                    # For risk-based stop loss, use a default percentage for now
                    self.stop_loss_pct = 2.0
                    stop_loss_unit = "percentage"
                else:
                    # Default to percentage-based stop loss
                    self.stop_loss_pct = 2.0
                    stop_loss_unit = "percentage"
            else:
                # Default to percentage-based stop loss
                self.stop_loss_pct = 2.0
                stop_loss_unit = "percentage"

            # Calculate take profit based on risk reward ratio
            self.take_profit_pct = self.stop_loss_pct * risk_reward_ratio
            take_profit_unit = stop_loss_unit

            self.logger.log_info(f"Calculated take profit: {self.take_profit_pct}% based on risk reward ratio: {risk_reward_ratio}")

            # Set default values for other risk management parameters
            self.daily_loss_limit = 5.0  # 5% of account balance
            self.max_position_size_pct = 10.0  # 10% of account balance
            self.runtime_days = self.strategy.runtime if hasattr(self.strategy, 'runtime') else 7
            self.total_profit_target = 20.0  # 20% of account balance
            self.total_loss_limit = 10.0  # 10% of account balance
            self.avoid_high_spread = self.strategy.avoid_high_spread if hasattr(self.strategy, 'avoid_high_spread') else True

            # Get current price for unit conversion if needed
            current_price = None
            if stop_loss_unit != "percentage" or take_profit_unit != "percentage":
                try:
                    # Get latest price for the instrument
                    market_data = self.market_data_provider.get_candles(
                        symbol=self.strategy.instrument,
                        timespan=self.strategy.timeframe,
                        count=1
                    )
                    if market_data["status"] == "success" and market_data["candles"]:
                        current_price = market_data["candles"][-1]["close"]
                except Exception as e:
                    self.logger.log_error(e, "Error getting current price for unit conversion")

            # The stop_loss_pct and take_profit_pct are already set above based on the stop loss method
            # No need to convert them again

            self.logger.log_info(f"Final stop-loss: {self.stop_loss_pct}%, take-profit: {self.take_profit_pct}%")

            # Risk percentage is already set above

            # Default runtime: 7 days
            self.runtime_days = int(risk_config.get('runtime', 7))

            # Default total profit target: 20% of account balance
            total_profit_target_str = risk_config.get('totalProfitTarget', '20%')
            self.total_profit_target = self._parse_percentage(total_profit_target_str, 20.0)

            # Default total loss limit: 10% of account balance
            total_loss_limit_str = risk_config.get('totalLossLimit', '10%')
            self.total_loss_limit = self._parse_percentage(total_loss_limit_str, 10.0)

            self.logger.log_info(f"Risk management initialized: "
                               f"daily_loss_limit={self.daily_loss_limit}%, "
                               f"max_position_size={self.max_position_size_pct}%, "
                               f"stop_loss={self.stop_loss_pct}%, "
                               f"take_profit={self.take_profit_pct}%, "
                               f"risk_percentage={self.risk_percentage_pct}%, "
                               f"runtime_days={self.runtime_days}, "
                               f"total_profit_target={self.total_profit_target}%, "
                               f"total_loss_limit={self.total_loss_limit}%, "
                               f"avoid_high_spread={self.avoid_high_spread}")

            # Immediately update risk management metrics in Firebase to ensure they're available right away
            self._update_risk_management_metrics()
        except Exception as e:
            self.logger.log_error(e, "Error initializing risk management, using defaults")
            # Set defaults if there's an error
            self.daily_loss_limit = 5.0  # 5% of account balance
            self.max_position_size_pct = 10.0  # 10% of account balance
            self.stop_loss_pct = 2.0  # 2% from entry price
            self.take_profit_pct = 4.0  # 4% from entry price
            self.risk_percentage_pct = 1.0  # 1% of account balance
            self.runtime_days = 7  # 7 days
            self.total_profit_target = 20.0  # 20% of account balance
            self.total_loss_limit = 10.0  # 10% of account balance
            self.avoid_high_spread = True  # Default to True for safety

    def _parse_percentage(self, percentage_str: str, default: float, unit: str = "percentage") -> float:
        """
        Parse a percentage string (e.g., '5%') to a float.

        Args:
            percentage_str (str): Percentage string to parse
            default (float): Default value if parsing fails
            unit (str): Unit of the value (percentage, pips, dollars)

        Returns:
            float: Parsed percentage as a float
        """
        try:
            if isinstance(percentage_str, str):
                # Remove '%' if present and convert to float
                cleaned_str = percentage_str.strip('%')
                return float(cleaned_str)
            elif isinstance(percentage_str, (int, float)):
                return float(percentage_str)
            else:
                return default
        except (ValueError, TypeError):
            return default

    def _convert_to_percentage(self, value: float, unit: str, price: float = None) -> float:
        """
        Convert a value to a percentage based on its unit.

        Args:
            value (float): Value to convert
            unit (str): Unit of the value (percentage, pips, dollars)
            price (float): Current price, needed for pips and dollars conversion

        Returns:
            float: Value as a percentage
        """
        if unit == "percentage":
            return value
        elif unit == "pips" and price is not None:
            # Convert pips to percentage based on current price
            # 1 pip is typically 0.0001 for 4-decimal pairs
            pip_value = 0.0001
            return (value * pip_value / price) * 100
        elif unit == "dollars" and price is not None:
            # Convert dollars to percentage based on account balance
            account_balance = self._get_account_balance()
            if account_balance > 0:
                return (value / account_balance) * 100
            return 0
        else:
            self.logger.log_warning(f"Unknown unit {unit} or missing price for conversion")
            return value

    def _get_account_balance(self) -> float:
        """
        Get the current account balance with retry mechanism.

        Returns:
            float: Current account balance, or 0.0 if all retries fail
        """
        max_retries = 5
        retry_delay = 1.0  # Start with 1 second delay

        for attempt in range(max_retries):
            try:
                account_summary = self.oanda_client.get_account_summary()
                if account_summary and "balance" in account_summary:
                    balance = float(account_summary["balance"])

                    if balance > 0:
                        self.logger.log_info(f"Account balance: {balance:.4f}")
                        return balance
                    else:
                        self.logger.log_warning(f"Attempt {attempt + 1}/{max_retries}: Received zero balance from OANDA API: {balance}")
                else:
                    self.logger.log_warning(f"Attempt {attempt + 1}/{max_retries}: Account summary missing or invalid from OANDA API")

            except Exception as e:
                self.logger.log_error(f"Attempt {attempt + 1}/{max_retries}: Error getting account balance from OANDA API: {e}")

            # If this wasn't the last attempt, wait before retrying
            if attempt < max_retries - 1:
                self.logger.log_info(f"Retrying in {retry_delay:.1f} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 1.5  # Exponential backoff

        # All retries failed
        self.logger.log_error(f"CRITICAL: Failed to get valid account balance after {max_retries} attempts")
        self.logger.log_error("This will cause trading to stop due to daily loss limit protection")
        return 0.0

    def warm_up(self) -> Dict[str, Any]:
        """Warm up the trading engine with historical data to ensure it's ready to go."""
        try:
            self.logger.log_info("Warming up trading engine...")
            # Check market status
            market_status = self.market_data_provider.check_market_status()
            self.logger.log_info(f"Market status: {market_status}")
            if not market_status["is_open"]:
                self.logger.log_info("Market is closed, skipping warm up")
                return {
                    "status": "market_closed",
                    "message": "Market is closed"
                }

            # Check if we have trading sessions defined
            trading_sessions = None
            if hasattr(self.strategy, 'trading_session') and self.strategy.trading_session:
                trading_sessions = self.strategy.trading_session
                self.logger.log_info(f"Trading sessions: {trading_sessions}")

                # Check if we're in a trading session
                session_info = self.market_data_provider.market_conditions.is_in_trading_session(trading_sessions)
                self.logger.log_info(f"Trading session info: {session_info}")

                if not session_info["in_session"]:
                    self.logger.log_info("Not in trading session, skipping warm up")
                    return {
                        "status": "not_in_session",
                        "message": f"Outside trading session: {', '.join(trading_sessions)}",
                        "session_info": session_info
                    }

            # Get open trades
            open_trades = self.oanda_client.get_open_trades()
            if len(open_trades) > 0:
                self.logger.log_error(Exception("Open trades already exist"), "Open trades already exist")
                self.firebase_client.append_user_log(
                    "‼️ Cannot start trade bot - There are some open trades already. Please close them before starting the trade bot."
                )
                raise Exception("Cannot start trade bot - There are some open trades already. Please close them before starting the trade bot.")

            market_data = self.market_data_provider.get_candles(
                symbol=self.strategy.instrument,
                timespan=self.strategy.timeframe,
                count=MIN_CANDLE_COUNT
            )

            if market_data["status"] == "market_data_error":
                error_msg = f"Market data error: {market_data['message']}"
                self.logger.log_error(Exception(error_msg), "Failed to warm up trading engine")
                return {
                    "status": "market_data_error",
                    "message": error_msg
                }

            self.logger.log_info(f"Fetched {len(market_data['candles'])} formatted_candles")

            # Calculate indicators
            indicators = self.strategy.calculate_indicators(market_data["candles"])

            # Get account summary
            account_summary = self.oanda_client.get_account_summary()
            if not account_summary:
                error_msg = "Failed to fetch account summary"
                self.logger.log_error(Exception(error_msg), error_msg)
                return {
                    "status": "error",
                    "message": error_msg
                }

            self.logger.log_info(f"Account balance: {account_summary['balance']}")

            # Format indicators for chart display
            formatted_indicators = {}
            for name, values in indicators.items():
                formatted_indicators[name] = [{
                    "time": candle["time"],
                    "value": float(value)
                } for candle, value in zip(market_data["candles"], values)]

            self.logger.log_info(f"Warmed up trading engine successfully")
            return {
                "status": "success",
                "message": "Warmed up trading engine successfully",
                "market_data": {
                    "candles": market_data["candles"],
                    "indicators": formatted_indicators
                },
                "account_summary": account_summary
            }

        except Exception as e:
            self.logger.log_error(e, "Error warming up trading engine")
            return {
                "status": "error",
                "message": str(e)
            }

    def _check_daily_loss_limit(self, account_balance: float) -> bool:
        """
        Check if the daily loss limit has been exceeded.

        Args:
            account_balance (float): Current account balance

        Returns:
            bool: True if daily loss limit has been exceeded, False otherwise
        """
        if self.daily_loss_limit is None:
            return False

        # Calculate daily loss limit in absolute terms
        daily_loss_limit_abs = account_balance * (self.daily_loss_limit / 100.0)

        # Check if daily loss exceeds the limit
        if self.daily_loss >= daily_loss_limit_abs:
            self.logger.log_warning(
                f"Daily loss limit exceeded: {self.daily_loss:.2f} > {daily_loss_limit_abs:.2f}"
            )
            self.firebase_client.append_user_log(
                f"⚠️ Daily loss limit of {self.daily_loss_limit}% exceeded. Trading paused for today."
            )
            return True

        return False

    def _check_runtime_expiration(self) -> bool:
        """
        Check if the bot's runtime has expired.

        Returns:
            bool: True if runtime has expired, False otherwise
        """
        if self.runtime_days is None:
            return False

        # Calculate end time
        end_time = self.start_time + timedelta(days=self.runtime_days)

        # Check if current time exceeds end time
        if datetime.now(timezone.utc) > end_time:
            self.logger.log_warning(
                f"Runtime of {self.runtime_days} days has expired. Started on {self.start_time}, ended on {end_time}"
            )
            self.firebase_client.append_user_log(
                f"⏰ Bot runtime of {self.runtime_days} days has expired. Bot will stop."
            )
            return True

        return False

    def _check_total_profit_target(self, account_balance: float) -> bool:
        """
        Check if the total profit target has been reached.

        Args:
            account_balance (float): Current account balance

        Returns:
            bool: True if total profit target has been reached, False otherwise
        """
        if self.total_profit_target is None:
            return False

        # Calculate total profit target in absolute terms
        total_profit_target_abs = account_balance * (self.total_profit_target / 100.0)

        # Check if total profit exceeds the target
        if self.total_profit >= total_profit_target_abs:
            self.logger.log_info(
                f"Total profit target reached: {self.total_profit:.2f} >= {total_profit_target_abs:.2f}"
            )
            self.firebase_client.append_user_log(
                f"✅ Total profit target of {self.total_profit_target}% reached. Bot will stop."
            )
            return True

        return False

    def _check_total_loss_limit(self, account_balance: float) -> bool:
        """
        Check if the total loss limit has been exceeded.

        Args:
            account_balance (float): Current account balance

        Returns:
            bool: True if total loss limit has been exceeded, False otherwise
        """
        if self.total_loss_limit is None:
            return False

        # Calculate total loss limit in absolute terms
        total_loss_limit_abs = account_balance * (self.total_loss_limit / 100.0)

        # Check if total loss exceeds the limit
        if self.total_loss >= total_loss_limit_abs:
            self.logger.log_warning(
                f"Total loss limit exceeded: {self.total_loss:.2f} > {total_loss_limit_abs:.2f}"
            )
            self.firebase_client.append_user_log(
                f"⛔ Total loss limit of {self.total_loss_limit}% exceeded. Bot will stop."
            )
            return True

        return False

    def _update_daily_loss(self, realized_pl: float, update_metrics: bool = False):
        """
        Update the daily loss tracker with a new realized P&L value.

        Args:
            realized_pl (float): Realized P&L from a closed trade
            update_metrics (bool, optional): Whether to update risk management metrics. Defaults to False.
        """
        # If P&L is negative, add it to the daily loss
        if realized_pl < 0:
            self.daily_loss += abs(realized_pl)
            self.logger.log_info(f"Updated daily loss: {self.daily_loss:.2f}")

            # Update risk management metrics in Firebase if requested
            if update_metrics:
                self._update_risk_management_metrics()

    def _update_risk_management_metrics(self):
        """
        Update risk management metrics in Firebase.
        This includes daily loss, total profit, total loss, and progress towards targets.
        """
        try:
            # Calculate runtime progress
            now = datetime.now(timezone.utc)
            elapsed_days = (now - self.start_time).total_seconds() / (24 * 60 * 60)  # Convert to days
            runtime_progress = min(100, (elapsed_days / self.runtime_days) * 100) if self.runtime_days > 0 else 0
            days_remaining = max(0, self.runtime_days - elapsed_days)

            # Get account balance to calculate absolute values
            account_summary = self.oanda_client.get_account_summary()
            account_balance = float(account_summary.get('balance', 0)) if account_summary else 0

            # Calculate absolute values for targets and limits
            daily_loss_limit_abs = account_balance * (self.daily_loss_limit / 100.0) if account_balance > 0 else 0
            max_position_size_abs = account_balance * (self.max_position_size_pct / 100.0) if account_balance > 0 else 0
            total_profit_target_abs = account_balance * (self.total_profit_target / 100.0) if account_balance > 0 else 0
            total_loss_limit_abs = account_balance * (self.total_loss_limit / 100.0) if account_balance > 0 else 0

            self.logger.log_info(f"Calculated absolute values: "
                               f"daily_loss_limit_abs={daily_loss_limit_abs}, "
                               f"max_position_size_abs={max_position_size_abs}, "
                               f"total_profit_target_abs={total_profit_target_abs}, "
                               f"total_loss_limit_abs={total_loss_limit_abs}")

            # Create risk management metrics dictionary
            risk_metrics = {
                "dailyLoss": self.daily_loss,
                "totalProfit": self.total_profit,
                "totalLoss": self.total_loss,
                "runtimeProgress": runtime_progress,
                "elapsedDays": elapsed_days,
                "daysRemaining": days_remaining,
                "accountBalance": account_balance,
                "dailyLossLimitAbs": daily_loss_limit_abs,
                "maxPositionSizeAbs": max_position_size_abs,
                "totalProfitTargetAbs": total_profit_target_abs,
                "totalLossLimitAbs": total_loss_limit_abs,
                "lastUpdated": now
            }

            self.logger.log_info(f"Updating risk management metrics: "
                               f"totalProfit={self.total_profit}, "
                               f"totalLoss={self.total_loss}, "
                               f"dailyLoss={self.daily_loss}, "
                               f"accountBalance={account_balance}")

            # Add risk management parameters using the new structure
            risk_params = {
                # New parameters
                "riskPercentage": f"{self.risk_percentage_pct}%" if self.risk_percentage_pct is not None else None,
                "riskRewardRatio": self.strategy.risk_reward_ratio if hasattr(self.strategy, 'risk_reward_ratio') else None,
                "stopLossMethod": self.strategy.stop_loss_method if hasattr(self.strategy, 'stop_loss_method') else "fixed",
                "fixedPips": self.strategy.fixed_pips if hasattr(self.strategy, 'fixed_pips') and self.strategy.fixed_pips is not None else None,
                "indicatorBasedSL": self.strategy.indicator_based_sl if hasattr(self.strategy, 'indicator_based_sl') else None,
                "lotSize": self.strategy.lot_size if hasattr(self.strategy, 'lot_size') else None,

                # Additional parameters
                "maxDailyLoss": f"{self.daily_loss_limit}%" if self.daily_loss_limit is not None else None,
                "maxPositionSize": f"{self.max_position_size_pct}%" if self.max_position_size_pct is not None else None,
                "totalProfitTarget": f"{self.total_profit_target}%" if self.total_profit_target is not None else None,
                "totalLossLimit": f"{self.total_loss_limit}%" if self.total_loss_limit is not None else None,
                "runtime": self.runtime_days,
                "startTime": self.start_time.isoformat(),
                "avoidHighSpread": self.avoid_high_spread  # Include the avoid_high_spread parameter
            }

            # Log the risk parameters for debugging
            self.logger.log_info(f"Risk parameters being saved to Firebase: {risk_params}")

            # Combine metrics and parameters
            risk_management_data = {
                "metrics": risk_metrics,
                "parameters": risk_params
            }

            # Update in Firebase
            self.firebase_client.update_risk_management(risk_management_data)

            self.logger.log_info("Updated risk management metrics in Firebase")
        except Exception as e:
            self.logger.log_error(e, "Error updating risk management metrics in Firebase")

    def _process_close_signal(self, signals: Dict[str, Any], market_data: Dict[str, Any], account_summary: Dict[str, Any], open_trades: List[TradeHistoryRow]) -> Optional[TradeExecutionResponse]:
        """
        Process a CLOSE signal and check for new entry signals in the same cycle.

        Args:
            signals (Dict[str, Any]): Trading signals from strategy
            market_data (Dict[str, Any]): Current market data
            account_summary (Dict[str, Any]): Account summary from OANDA
            open_trades (List[TradeHistoryRow]): List of open trades

        Returns:
            Optional[TradeExecutionResponse]: Trade execution results if successful
        """
        # Get current price
        current_price = float(market_data["candles"][-1]["close"])

        # Close the current position
        self.logger.log_info(f"Closing trade based on exit signal: {signals['reason']}")
        self.logger.log_info(f"Open trade details: ID={open_trades[0].tradeID}, instrument={open_trades[0].instrument}, units={open_trades[0].units}")

        # Format the instrument to match OANDA's format (EUR_USD instead of EUR/USD)
        instrument = self.strategy.instrument
        formatted_instrument = instrument.replace('/', '_')
        self.logger.log_info(f"Using instrument {instrument} (formatted as {formatted_instrument}) for closing trade")

        trade_result = self.oanda_client.execute_trade(
            instrument=instrument,
            units=float(open_trades[0].units),  # Use the same units as the open position
            type=TradeType.CLOSE,
            price=current_price
        )

        if trade_result:
            # Increment trades closed counter
            self.metrics['trades_closed'] += 1

            self.firebase_client.append_user_log(
                f"🟢 Trade executed: CLOSE order placed - {signals['reason']}"
            )

            # Report trade execution metrics
            if self.health_client:
                self.health_client.update_dependency("trade_execution", "healthy", "Successfully closed trade")

            # Update trackers if the trade had a P&L
            # Check if trade_result is a dict or a TradeExecutionResponse object
            realized_pl = 0
            if isinstance(trade_result, dict) and 'realizedPL' in trade_result:
                realized_pl = trade_result['realizedPL']
            elif hasattr(trade_result, 'pl'):
                realized_pl = trade_result.pl

            if realized_pl != 0:
                self.logger.log_info(f"Trade closed with realized P&L: {realized_pl}")

                # Update daily loss tracker if negative P&L (without updating metrics yet)
                if realized_pl < 0:
                    self._update_daily_loss(realized_pl, update_metrics=False)

                # Update total profit and loss trackers (without updating metrics yet)
                self._update_total_pnl(realized_pl, update_metrics=False)

                # Now update risk management metrics once - this is more efficient
                # This ensures the frontend gets the latest values with a single update
                self._update_risk_management_metrics()

                # Update the trade history in Firebase
                # Create a closed trade history row
                closed_trade = open_trades[0]
                # Get halfSpreadCost from the trade result if available
                half_spread_cost = None
                if isinstance(trade_result, dict) and 'halfSpreadCost' in trade_result:
                    half_spread_cost = trade_result['halfSpreadCost']
                elif hasattr(trade_result, 'halfSpreadCost'):
                    half_spread_cost = trade_result.halfSpreadCost

                closed_trade_history_row = TradeHistoryRow(
                    tradeID=closed_trade.tradeID,
                    type=closed_trade.type,
                    status=TradeStatus.CLOSED,
                    instrument=closed_trade.instrument,
                    price=closed_trade.price,
                    openTime=closed_trade.openTime,
                    units=closed_trade.units,
                    initialMarginRequired=closed_trade.initialMarginRequired,
                    unrealizedPL=0,  # No longer unrealized
                    realizedPL=realized_pl,
                    closeTime=datetime.now(timezone.utc),
                    takeProfitPrice=closed_trade.takeProfitPrice,
                    stopLossPrice=closed_trade.stopLossPrice,
                    halfSpreadCost=half_spread_cost,
                    commission=closed_trade.commission  # Pass through the original commission
                )

                # Update the existing trade in the trade history in Firebase
                self.logger.log_info(f"Updating trade history with closed trade: {closed_trade_history_row}")
                self.firebase_client.update_open_trades([closed_trade_history_row])

            # After closing the trade, check for new entry signals in the same cycle
            self.logger.log_info("Checking for new entry signals after closing trade...")

            # Refresh the list of open trades to confirm the trade was closed
            # First, clear the cache to ensure we get fresh data
            if hasattr(self.oanda_client, 'cache') and 'open_trades' in self.oanda_client.cache:
                self.oanda_client.cache['open_trades']['timestamp'] = 0  # Force cache refresh

            refreshed_open_trades = self.oanda_client.get_open_trades()

            # Check if the specific trade we just closed is no longer in the list
            closed_trade_id = closed_trade_history_row.tradeID if 'closed_trade_history_row' in locals() else None
            trade_still_open = next((t for t in refreshed_open_trades if t.tradeID == closed_trade_id), None) if closed_trade_id else None

            if not trade_still_open:
                # Trade was successfully closed, now check for new entry signals
                new_signals = self.strategy.calculate_signals(market_data=market_data)

                if new_signals and new_signals.get("action") in ["BUY", "SELL"]:
                    self.logger.log_info(f"New entry signal detected after closing trade: {new_signals['action']} - {new_signals['reason']}")

                    # Add position size to the new signals
                    new_signals["position_size"] = self.strategy.get_position_size(
                        account_balance=float(account_summary["balance"]),
                        current_price=current_price
                    )

                    # Execute the new trade
                    return self._execute_trade(new_signals, market_data, account_summary, [])
                else:
                    self.logger.log_info("No new entry signals after closing trade")
            else:
                if closed_trade_id:
                    self.logger.log_warning(f"Trade {closed_trade_id} appears to still be open in Oanda, cannot check for new entry signals")
                else:
                    self.logger.log_warning("Cannot verify if trade was successfully closed, skipping new entry signal check")

        return trade_result

    def _update_total_pnl(self, realized_pl: float, update_metrics: bool = False):
        """
        Update the total profit and loss trackers with a new realized P&L value.

        Args:
            realized_pl (float): Realized P&L from a closed trade
            update_metrics (bool, optional): Whether to update risk management metrics. Defaults to False.
        """
        if realized_pl > 0:
            self.total_profit += realized_pl
            self.logger.log_info(f"Updated total profit: {self.total_profit:.2f}")
        elif realized_pl < 0:
            self.total_loss += abs(realized_pl)
            self.logger.log_info(f"Updated total loss: {self.total_loss:.2f}")

        # Update risk management metrics in Firebase if requested
        if update_metrics:
            self._update_risk_management_metrics()

    def _track_execution_time(self, operation: str, start_time: float) -> None:
        """
        Track execution time for an operation.

        Args:
            operation (str): Operation name
            start_time (float): Start time from time.time()
        """
        import time
        execution_time = time.time() - start_time

        if operation not in self.metrics['execution_times']:
            self.metrics['execution_times'][operation] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0
            }

        metrics = self.metrics['execution_times'][operation]
        metrics['count'] += 1
        metrics['total_time'] += execution_time
        metrics['min_time'] = min(metrics['min_time'], execution_time)
        metrics['max_time'] = max(metrics['max_time'], execution_time)

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for the trading engine.

        Returns:
            Dict[str, Any]: Performance metrics including:
                - update_cycles: Number of update cycles run
                - trades_executed: Number of trades executed
                - trades_closed: Number of trades closed
                - execution_times: Execution time statistics for various operations
                - oanda_metrics: Performance metrics from the OANDA client
                - firebase_metrics: Performance metrics from the Firebase client
        """
        metrics = self.metrics.copy()

        # Calculate average execution times
        for operation, data in metrics['execution_times'].items():
            if data['count'] > 0:
                data['avg_time'] = data['total_time'] / data['count']

        # Add metrics from other components
        if hasattr(self.oanda_client, 'get_performance_metrics'):
            metrics['oanda_metrics'] = self.oanda_client.get_performance_metrics()

        if hasattr(self.firebase_client, 'get_performance_metrics'):
            metrics['firebase_metrics'] = self.firebase_client.get_performance_metrics()

        return metrics

    def update(self) -> Dict[str, Any]:
        """
        Update the trading engine state and execute trades if necessary.

        Returns:
            Dict[str, Any]: Update status and results
        """
        import time
        start_time = time.time()
        try:
            # Increment update cycle counter
            self.metrics['update_cycles'] += 1
            self.logger.log_info("Starting update cycle...")

            # Update risk management metrics periodically
            # This ensures runtime progress and other metrics are updated even without trades
            self._update_risk_management_metrics()

            # Update health metrics
            if self.health_client:
                # Update OANDA dependency status
                try:
                    # Check if we can get account data
                    self.oanda_client.get_account_summary()
                    self.health_client.update_dependency("oanda", "healthy", "Connected")
                except Exception as e:
                    self.health_client.update_dependency("oanda", "unhealthy", str(e))

                # Update market data dependency status
                try:
                    # Check if market data is available
                    market_open = self.market_data_provider.check_market_status()
                    self.health_client.update_dependency("market_data", "healthy", f"Market {'open' if market_open else 'closed'}")
                except Exception as e:
                    self.health_client.update_dependency("market_data", "unhealthy", str(e))

            # Check if runtime has expired
            if self._check_runtime_expiration():
                return {
                    "status": "runtime_expired",
                    "message": f"Bot runtime of {self.runtime_days} days has expired."
                }

            # Check if we have trading sessions defined
            trading_sessions = None
            if hasattr(self.strategy, 'trading_session') and self.strategy.trading_session:
                trading_sessions = self.strategy.trading_session
                self.logger.log_info(f"Trading sessions: {trading_sessions}")

            # Check comprehensive market conditions
            market_conditions = self.market_data_provider.check_market_conditions(
                symbol=self.strategy.instrument,
                trading_sessions=trading_sessions,
                avoid_high_spread=self.avoid_high_spread
            )
            self.logger.log_info(f"Market conditions: {market_conditions['is_safe_to_trade']}")

            # Update market status in Firebase
            self.firebase_client.update_market_status(market_conditions)

            if not market_conditions["market_hours"]["is_open"]:
                self.logger.log_info(f"Market is closed: {market_conditions['reason']}")
                self.firebase_client.append_user_log(
                    f"🔴 Market is closed: {market_conditions['reason']}"
                )
                return {
                    "status": "market_closed",
                    "message": market_conditions['reason']
                }

            # Check if we're in a trading session
            if trading_sessions and 'session_info' in market_conditions and market_conditions['session_info']:
                session_info = market_conditions['session_info']
                if not session_info["in_session"]:
                    self.logger.log_info(f"Not in trading session: {', '.join(trading_sessions)}")
                    self.firebase_client.append_user_log(
                        f"🔵 Outside trading session: {', '.join(trading_sessions)}"
                    )
                    return {
                        "status": "not_in_session",
                        "message": f"Outside trading session: {', '.join(trading_sessions)}",
                        "session_info": session_info
                    }

            # Check if it's safe to trade based on other conditions
            if not market_conditions["is_safe_to_trade"]:
                self.logger.log_info(f"Market conditions not suitable for trading: {market_conditions['reason']}")
                self.firebase_client.append_user_log(
                    f"⚠️ Market conditions not suitable for trading: {market_conditions['reason']}"
                )
                return {
                    "status": "market_conditions_unsuitable",
                    "message": market_conditions['reason'],
                    "conditions": market_conditions
                }

            # Fetch market data
            timeframe = self.strategy.timeframe
            self.logger.log_info(f"Using timeframe: {timeframe}")

            market_data = self.market_data_provider.get_candles(
                symbol=self.strategy.instrument,
                timespan=timeframe,
                count=MIN_CANDLE_COUNT
            )

            if market_data["status"] == "market_data_error":
                error_msg = f"Market data error: {market_data['message']}"
                self.logger.log_error(Exception(error_msg), error_msg)
                return {
                    "status": "market_data_error",
                    "message": error_msg
                }

            self.logger.log_info(f"Fetched {len(market_data['candles'])} candles")

            # Calculate indicators
            indicators = self.strategy.calculate_indicators(market_data["candles"])
            self.logger.log_info(f"Calculated indicators: {list(indicators.keys())}")

            # Get account summary
            account_summary = self.oanda_client.get_account_summary()
            if not account_summary:
                error_msg = "Failed to fetch account summary"
                self.logger.log_error(Exception(error_msg), error_msg)
                return {
                    "status": "error",
                    "message": error_msg
                }

            self.logger.log_info(f"Account balance: {account_summary['balance']}")

            # Get account balance as float
            account_balance = float(account_summary['balance'])

            # Check daily loss limit
            if self._check_daily_loss_limit(account_balance):
                return {
                    "status": "daily_loss_limit_exceeded",
                    "message": f"Daily loss limit of {self.daily_loss_limit}% exceeded"
                }

            # Check total profit target
            if self._check_total_profit_target(account_balance):
                return {
                    "status": "total_profit_target_reached",
                    "message": f"Total profit target of {self.total_profit_target}% reached"
                }

            # Check total loss limit
            if self._check_total_loss_limit(account_balance):
                return {
                    "status": "total_loss_limit_exceeded",
                    "message": f"Total loss limit of {self.total_loss_limit}% exceeded"
                }

            # Get open trades
            open_trades = self.oanda_client.get_open_trades()
            self.logger.log_info(f"Open trades: {open_trades}")

            # Update strategy with open trades information
            strategy_update = self.strategy.update(
                market_data={
                    "candles": market_data["candles"],
                    "indicators": indicators
                },
                account_balance=account_balance,
                open_trades=open_trades  # Pass open trades to the strategy
            )

            # Calculate trading signals
            signals = strategy_update.get("signals", {})
            self.logger.log_info(f"Generated signals: {signals}")

            # Execute trades based on signals
            trade_result = None
            if signals:
                self.logger.log_info("Executing trades based on signals...")
                trade_result = self._execute_trade(signals, {"candles": market_data["candles"]}, account_summary, open_trades)
                if trade_result:
                    self.logger.log_info(f"Trade executed: {trade_result}")
                else:
                    self.logger.log_info("No trades executed")

            # Format candles for chart display
            formatted_candles = [{
                "time": candle["time"],
                "open": float(candle["open"]),
                "high": float(candle["high"]),
                "low": float(candle["low"]),
                "close": float(candle["close"]),
                "volume": int(candle["volume"])
            } for candle in market_data["candles"]]

            # Format indicators for chart display
            formatted_indicators = {}
            for name, values in indicators.items():
                formatted_indicators[name] = [{
                    "time": candle["time"],
                    "value": float(value)
                } for candle, value in zip(market_data["candles"], values)]

            self.logger.log_info("Update cycle completed successfully")

            # Track execution time
            self._track_execution_time('update', start_time)

            return {
                "status": "success",
                "message": "Update completed successfully",
                "market_data": {
                    "candles": formatted_candles,
                    "indicators": formatted_indicators
                },
                "account_summary": account_summary,
                "open_trades": open_trades,
                "trade": trade_result,
                "performance_metrics": self.get_performance_metrics() if self.metrics['update_cycles'] % 10 == 0 else None  # Include metrics every 10 cycles
            }

        except SystemExit as e:
            # Re-raise SystemExit to propagate it up to the main loop
            self.logger.log_info(f"SystemExit received in trading engine update: {str(e)}")
            raise
        except Exception as e:
            error_msg = f"Error in update cycle: {str(e)}"
            self.logger.log_error(e, error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    def _execute_trade(self, signals: Dict[str, Any], market_data: Dict[str, Any], account_summary: Dict[str, Any], open_trades: List[TradeHistoryRow]) -> Optional[TradeExecutionResponse]:
        """
        Execute a trade based on strategy signals.

        Args:
            signals (Dict[str, Any]): Trading signals from strategy
            market_data (Dict[str, Any]): Current market data
            account_summary (Dict[str, Any]): Account summary from OANDA
            open_trades (List[TradeHistoryRow]): List of open trades

        Returns:
            Optional[TradeExecutionResponse]: Trade execution results if successful
        """
        import time
        start_time = time.time()
        try:
            # If we have a CLOSE signal and open trades, process it first
            if signals["action"] == "CLOSE" and len(open_trades) > 0:
                return self._process_close_signal(signals, market_data, account_summary, open_trades)

            # For BUY/SELL signals, check if we have open trades
            if signals["action"] in ["BUY", "SELL"] and len(open_trades) > 0:
                self.logger.log_info(f"Open trades: {open_trades} already exists - skipping trade")
                self.firebase_client.append_user_log(
                    "⚠️ Trade conditions met but we currently have an open position. "
                    "Forex trading rules require closing existing positions before opening new ones in the same instrument. "
                    "The bot will wait for the existing position to be closed before executing the new trade."
                )
                return None

            # Get current price
            current_price = float(market_data["candles"][-1]["close"])

            # Pip value will be calculated in the strategy's get_position_size method
            # based on the instrument and current price

            # Calculate position size based on account balance and risk parameters
            account_balance = float(account_summary["balance"])

            # Use the strategy's get_position_size method to calculate position size
            # This will handle all three scenarios: Fixed Pips, Indicator-Based SL, and Risk-Based SL
            # The pip value will be calculated inside the method based on the instrument and current price
            position_size = self.strategy.get_position_size(account_balance, current_price)

            # Apply max position size limit if available
            if self.max_position_size_pct is not None:
                max_position_size = account_balance * (self.max_position_size_pct / 100.0)
                if position_size > max_position_size:
                    self.logger.log_info(f"Position size {position_size} exceeds max {max_position_size}, limiting")
                    position_size = max_position_size

            # Use the stop loss and take profit percentages from the risk management initialization
            # These already prioritize strategy values over risk config values
            stop_loss_pct = self.stop_loss_pct / 100.0
            take_profit_pct = self.take_profit_pct / 100.0

            self.logger.log_info(f"Using position size: {position_size}, stop loss: {self.stop_loss_pct}%, take profit: {self.take_profit_pct}%")

            # Execute trade based on signal
            if signals["action"] == "BUY" and len(open_trades) == 0:
                # Calculate stop loss and take profit for long position
                stop_loss_price = current_price * (1 - stop_loss_pct)
                take_profit_price = current_price * (1 + take_profit_pct)

                trade_result = self.oanda_client.execute_trade(
                    instrument=self.strategy.instrument,
                    units=position_size,
                    type=TradeType.LONG,
                    price=current_price,
                    stop_loss=stop_loss_price,
                    take_profit=take_profit_price
                )

                if trade_result:
                    # Increment trades executed counter
                    self.metrics['trades_executed'] += 1

                self.logger.log_info(f"trade_result: {trade_result}")

                if trade_result:
                    self.firebase_client.append_user_log(
                        "🟢 Trade executed: LONG order placed"
                    )

                    # Report trade execution metrics
                    if self.health_client:
                        self.health_client.update_dependency("trade_execution", "healthy", "Successfully executed LONG trade")

                    return trade_result

            elif signals["action"] == "SELL" and len(open_trades) == 0:
                # Calculate stop loss and take profit for short position
                stop_loss_price = current_price * (1 + stop_loss_pct)
                take_profit_price = current_price * (1 - take_profit_pct)

                trade_result = self.oanda_client.execute_trade(
                    instrument=self.strategy.instrument,
                    units=-position_size,  # Negative units for short position
                    type=TradeType.SHORT,
                    price=current_price,
                    stop_loss=stop_loss_price,
                    take_profit=take_profit_price
                )

                if trade_result:
                    # Increment trades executed counter
                    self.metrics['trades_executed'] += 1

                if trade_result:
                    self.firebase_client.append_user_log(
                        "🟢 Trade executed: SHORT order placed"
                    )

                    # Report trade execution metrics
                    if self.health_client:
                        self.health_client.update_dependency("trade_execution", "healthy", "Successfully executed SHORT trade")

                    return trade_result

            # CLOSE signal is now handled by _process_close_signal

            # Track execution time
            self._track_execution_time('_execute_trade', start_time)

            return None

        except Exception as e:
            self.logger.log_error(e, "Error executing trade")
            # Track execution time even for errors
            self._track_execution_time('_execute_trade_error', start_time)
            return None

    def get_trade_history(self) -> List[Dict[str, Any]]:
        """
        Get the history of executed trades.

        Returns:
            List[Dict[str, Any]]: List of trade history entries
        """
        return self.trade_history

    def get_current_position(self) -> Optional[Dict[str, Any]]:
        """
        Get the current trading position.

        Returns:
            Optional[Dict[str, Any]]: Current position information if exists
        """
        positions = self.oanda_client.get_open_positions()
        return positions[0] if positions else None

    def convert_trade_execution_response_to_trade_history_row(self, trade_execution_response: TradeExecutionResponse) -> TradeHistoryRow:
        """
        Convert a TradeExecutionResponse to a TradeHistoryRow.

        Args:
            trade_execution_response (TradeExecutionResponse): The trade execution response to convert
        """
        return TradeHistoryRow(
            tradeID=trade_execution_response.tradeID,
            type=trade_execution_response.type,
            status=TradeStatus.OPEN,
            instrument=trade_execution_response.instrument,
            units=float(trade_execution_response.units),
            price=float(trade_execution_response.price),
            openTime=trade_execution_response.time if isinstance(trade_execution_response.time, datetime) else datetime.fromisoformat(trade_execution_response.time.replace('Z', '+00:00')),
            initialMarginRequired=float(trade_execution_response.initialMarginRequired),
            halfSpreadCost=float(trade_execution_response.halfSpreadCost),
            commission=float(trade_execution_response.commission),
            unrealizedPL=float(trade_execution_response.pl)
        )

    def close_and_update_open_trades(self, is_shutdown: bool = False) -> bool:
        """
        Close all open trades and update the trade history.

        Args:
            is_shutdown (bool): Whether this is being called during shutdown

        Returns:
            bool: True if all trades were closed successfully, False otherwise
        """
        try:
            self.logger.log_info("Attempting to close all open trades...")
            open_trades = self.oanda_client.get_open_trades()

            if not open_trades:
                self.logger.log_info("No open trades to close")
                return True

            self.logger.log_info(f"Found {len(open_trades)} open trades to close")

            # Track success/failure for each trade
            success_count = 0
            failure_count = 0

            for trade in open_trades:
                try:
                    self.logger.log_info(f"Closing trade {trade.tradeID}...")
                    closed_trade = self.oanda_client.close_trade(trade.tradeID)

                    if closed_trade:
                        # Units check is now more flexible during shutdown
                        if closed_trade["units"] != trade.units and not is_shutdown:
                            self.logger.log_warning(
                                f"Units mismatch when closing trade {trade.tradeID} - "
                                f"Closed: {closed_trade['units']}, Original: {trade.units}"
                            )

                        # Create trade history row
                        final_trade_history_row = TradeHistoryRow(
                            tradeID=closed_trade["tradeID"],
                            status=closed_trade["status"],
                            units=float(closed_trade["units"]),
                            realizedPL=float(closed_trade["realizedPL"]),
                            halfSpreadCost=float(closed_trade["halfSpreadCost"]),
                            closeTime=closed_trade["closeTime"],
                            type=trade.type,
                            instrument=trade.instrument,
                            price=trade.price,
                            openTime=trade.openTime,
                            initialMarginRequired=trade.initialMarginRequired,
                            commission=trade.commission,
                        )

                        # Update risk management metrics with realized P&L
                        realized_pl = float(closed_trade["realizedPL"])
                        self.logger.log_info(f"Trade {trade.tradeID} closed with realized P&L: {realized_pl}")

                        # Update daily loss tracker if negative P&L
                        if realized_pl < 0:
                            self._update_daily_loss(realized_pl)

                        # Update total profit and loss trackers
                        self._update_total_pnl(realized_pl)

                        # Update Firebase
                        try:
                            self.firebase_client.update_open_trades([final_trade_history_row])
                        except Exception as firebase_error:
                            # Don't let Firebase errors stop us during shutdown
                            self.logger.log_error(firebase_error, f"Failed to update trade history for {trade.tradeID} in Firebase")
                            if not is_shutdown:
                                raise

                        success_count += 1
                        self.logger.log_info(f"Successfully closed trade {trade.tradeID}")
                    else:
                        failure_count += 1
                        error_msg = f"Failed to close trade {trade.tradeID}"
                        self.logger.log_error(Exception(error_msg), error_msg)

                        if not is_shutdown:
                            self.firebase_client.append_user_log(
                                f"❌ Failed to close trade {trade.tradeID}"
                            )
                            raise Exception(f"Failed to close trade {trade.tradeID}")
                except Exception as e:
                    failure_count += 1
                    self.logger.log_error(e, f"Error closing trade {trade.tradeID}")

                    if not is_shutdown:
                        # During normal operation, propagate the error
                        self.firebase_client.append_user_log(
                            f"❌ Error closing trade {trade.tradeID}: {str(e)}"
                        )
                        raise

            # Log summary
            if success_count > 0 and failure_count == 0:
                self.logger.log_info(f"All {success_count} open trades closed successfully")
                self.firebase_client.append_user_log(
                    f"🟢 All {success_count} open trades closed successfully"
                )

                # Force a final update of risk management metrics
                self._update_risk_management_metrics()
                self.logger.log_info("Final risk management metrics updated after closing trades")

                return True
            elif success_count > 0 and failure_count > 0:
                self.logger.log_warning(f"Partially successful: Closed {success_count} trades, failed to close {failure_count} trades")
                self.firebase_client.append_user_log(
                    f"⚠️ Closed {success_count} trades, but failed to close {failure_count} trades"
                )

                # Force a final update of risk management metrics even with partial success
                self._update_risk_management_metrics()
                self.logger.log_info("Final risk management metrics updated after closing trades")

                return is_shutdown  # Only return True during shutdown
            elif failure_count > 0:
                self.logger.log_error(Exception(f"Failed to close all {failure_count} trades"), "Failed to close trades")
                self.firebase_client.append_user_log(
                    f"❌ Failed to close all {failure_count} trades"
                )
                return False

            return True

        except Exception as e:
            self.logger.log_error(e, "Error in close_and_update_open_trades")
            if not is_shutdown:
                self.firebase_client.append_user_log(
                    "❌ Error while closing trades"
                )
                raise
            return False
