import os
import json
import logging
from typing import Dict, Optional
from pathlib import Path

class ConfigManager:
    """Manages loading and validation of configuration files."""
    
    def __init__(self, config_dir: str = "config"):
        """
        Initialize the configuration manager.
        
        Args:
            config_dir (str): Directory containing configuration files
        """
        self.config_dir = Path(config_dir)
        self.logger = logging.getLogger(__name__)
        
        # Create config directory if it doesn't exist
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def load_config(self, filename: str) -> Optional[Dict]:
        """
        Load a configuration file.
        
        Args:
            filename (str): Name of the configuration file
            
        Returns:
            Optional[Dict]: Configuration dictionary if successful
        """
        try:
            config_path = self.config_dir / filename
            if not config_path.exists():
                self.logger.error(f"Configuration file not found: {filename}")
                return None
            
            with open(config_path, "r") as f:
                config = json.load(f)
            
            return config
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in configuration file {filename}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading configuration file {filename}: {e}")
            return None
    
    def save_config(self, config: Dict, filename: str) -> bool:
        """
        Save a configuration to a file.
        
        Args:
            config (Dict): Configuration dictionary to save
            filename (str): Name of the configuration file
            
        Returns:
            bool: True if successful
        """
        try:
            config_path = self.config_dir / filename
            
            with open(config_path, "w") as f:
                json.dump(config, f, indent=4)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving configuration file {filename}: {e}")
            return False
    
    def validate_strategy_config(self, config: Dict) -> Dict:
        """
        Validate a strategy configuration.
        
        Args:
            config (Dict): Strategy configuration to validate
            
        Returns:
            Dict: Validation results
        """
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Check required fields
        required_fields = [
            "name",
            "type",
            "instruments",
            "timeframe",
            "risk_management"
        ]
        
        for field in required_fields:
            if field not in config:
                validation_result["is_valid"] = False
                validation_result["errors"].append(f"Missing required field: {field}")
        
        # Validate instruments
        if "instruments" in config:
            if not isinstance(config["instruments"], list):
                validation_result["is_valid"] = False
                validation_result["errors"].append("Instruments must be a list")
            elif not config["instruments"]:
                validation_result["is_valid"] = False
                validation_result["errors"].append("At least one instrument must be specified")
        
        # Validate risk management
        if "risk_management" in config:
            risk_config = config["risk_management"]
            
            # Check position sizing
            if "position_size" not in risk_config:
                validation_result["warnings"].append("No position sizing rules specified")
            else:
                position_size = risk_config["position_size"]
                if "max_position_size" in position_size:
                    try:
                        float(position_size["max_position_size"])
                    except ValueError:
                        validation_result["errors"].append("max_position_size must be a number")
                
                if "risk_per_trade" in position_size:
                    try:
                        risk = float(position_size["risk_per_trade"])
                        if not 0 < risk <= 1:
                            validation_result["errors"].append("risk_per_trade must be between 0 and 1")
                    except ValueError:
                        validation_result["errors"].append("risk_per_trade must be a number")
            
            # Check stop loss and take profit
            if "stop_loss" not in risk_config:
                validation_result["warnings"].append("No stop loss specified")
            if "take_profit" not in risk_config:
                validation_result["warnings"].append("No take profit specified")
        
        # Validate indicators
        if "indicators" in config:
            if not isinstance(config["indicators"], list):
                validation_result["is_valid"] = False
                validation_result["errors"].append("Indicators must be a list")
            else:
                for indicator in config["indicators"]:
                    if "type" not in indicator:
                        validation_result["errors"].append("Each indicator must have a type")
                    if "parameters" not in indicator:
                        validation_result["errors"].append("Each indicator must have parameters")
        
        return validation_result
    
    def validate_engine_config(self, config: Dict) -> Dict:
        """
        Validate a trading engine configuration.
        
        Args:
            config (Dict): Engine configuration to validate
            
        Returns:
            Dict: Validation results
        """
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Check required fields
        required_fields = [
            "update_interval",
            "max_trades_per_day",
            "max_daily_loss"
        ]
        
        for field in required_fields:
            if field not in config:
                validation_result["is_valid"] = False
                validation_result["errors"].append(f"Missing required field: {field}")
        
        # Validate update interval
        if "update_interval" in config:
            try:
                interval = float(config["update_interval"])
                if interval < 1:
                    validation_result["errors"].append("update_interval must be at least 1 second")
            except ValueError:
                validation_result["errors"].append("update_interval must be a number")
        
        # Validate max trades per day
        if "max_trades_per_day" in config:
            try:
                max_trades = int(config["max_trades_per_day"])
                if max_trades < 1:
                    validation_result["errors"].append("max_trades_per_day must be at least 1")
            except ValueError:
                validation_result["errors"].append("max_trades_per_day must be an integer")
        
        # Validate max daily loss
        if "max_daily_loss" in config:
            try:
                max_loss = float(config["max_daily_loss"])
                if not 0 < max_loss <= 1:
                    validation_result["errors"].append("max_daily_loss must be between 0 and 1")
            except ValueError:
                validation_result["errors"].append("max_daily_loss must be a number")
        
        return validation_result 