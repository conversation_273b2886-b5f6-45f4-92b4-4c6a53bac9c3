{"name": "testStrat with SMA having RSI as source", "description": "", "instruments": "EUR/USD", "timeframe": "1m", "tradingSession": ["All"], "indicators": [{"id": "17463155718150j4abebs2hs", "indicator_class": "RSI", "type": "RSI", "parameters": {"period": 14}, "source": "price"}, {"id": "17463155757414jhu5y21tt8", "indicator_class": "SMA", "type": "SMA", "parameters": {"period": 50}, "source": "17463155718150j4abebs2hs"}], "entryRules": [{"id": "1746315587442hb6uml3mkve", "tradeType": "long", "indicator1": "17463155757414jhu5y21tt8", "operator": ">", "compareType": "value", "indicator2": "", "value": "1", "barRef": "close"}], "exitRules": [{"id": "1746315596604ittq3x9iui", "tradeType": "long", "indicator1": "17463155757414jhu5y21tt8", "operator": ">", "compareType": "value", "indicator2": "", "value": "90", "barRef": "close"}], "riskManagement": {"stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage", "riskPerTrade": "1%", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%"}, "user_id": "hUli47EgkKnHdyryQhzTyvn7ehxp", "id": "pg8A6o1hXZmM12EsGoY0"}