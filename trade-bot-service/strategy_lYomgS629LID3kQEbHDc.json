{"name": "testStrat", "description": "", "instruments": "EUR/USD", "timeframe": "1m", "tradingSession": ["All"], "indicators": [{"id": "17463155718150j4abebs2hs", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 14}, "source": "price"}, {"id": "17463155757414jhu5y21tt8", "type": "SMA", "indicator_class": "SMA", "parameters": {"period": 50}, "source": "17463155718150j4abebs2hs"}, {"id": "1746316025284u6ab7tvoq0e", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 20}, "source": "price"}], "entryRules": [{"id": "1746315587442hb6uml3mkve", "tradeType": "long", "indicator1": "17463155757414jhu5y21tt8", "operator": ">", "compareType": "value", "indicator2": "", "value": "1", "barRef": "close"}, {"id": "1746405916784s5s2aa1pkpb", "tradeType": "long", "indicator1": "1746316025284u6ab7tvoq0e", "operator": ">", "compareType": "value", "indicator2": "", "value": "1", "barRef": "close"}], "exitRules": [{"id": "1746480431327rmkl3s5gt5", "tradeType": "long", "indicator1": "17463155718150j4abebs2hs", "operator": ">", "compareType": "value", "indicator2": "", "value": "1", "barRef": "close"}], "riskManagement": {"riskPercentage": "2", "riskRewardRatio": "2", "stopLossMethod": "risk", "fixedPips": "", "indicatorBasedSL": {"indicator": "", "parameters": {}}, "lotSize": "999", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}, "entryLongGroupOperator": "AND", "user_id": "1EhFBUSLIoM9UjLa0hDqFokfP0dz", "id": "lYomgS629LID3kQEbHDc"}