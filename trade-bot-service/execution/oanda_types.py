from dataclasses import dataclass, asdict
from typing import Optional, List
from datetime import datetime
import json

"""
Structs representing the request and response from OANDA's endpoints.
DO NOT MODIFY THESE STRUCTS UNLESS OANDA CHANGES THEIR API.
DO NOT REMOVE COMMENTS FROM THESE STRUCTS.
""" 

# ------------------------------------------------------------------------------------------------------------------------------------------------------------------
# POST /v3/accounts/{accountID}/orders
# ----------------------------------------------------------------------------------------------------------------------------------------
@dataclass
class ClientExtensions:
    """Represents a ClientExtensions from OANDA."""
    # The Client ID of the Order/Trade
    id : str

    # A tag associated with the Order/Trade
    tag : str

    # A comment associated with the Order/Trade
    comment : str

@dataclass
class OandaTransaction:
    """Represents a Transaction from OANDA."""
    # String representation of the numerical OANDA-assigned TransactionID
    id: str
    
    # The date/time when the Transaction was created.
    # https://developer.oanda.com/rest-live-v20/primitives-df/#DateTime
    time: datetime
    
    # The ID of the user that initiated the creation of the Transaction.
    userID: int
    
    # The ID of the Account the Transaction was created for.
    # https://developer.oanda.com/rest-live-v20/account-df/#AccountID
    accountID: str
    
    # The ID of the "batch" that the Transaction belongs to. Transactions in
    # the same batch are applied to the Account simultaneously.
    # https://developer.oanda.com/rest-live-v20/transaction-df/#TransactionID
    batchID: str
    
    # The Request ID of the request which generated the transaction.
    requestID: str

@dataclass
class ConversionFactor:
    """Represents a ConversionFactor from OANDA."""
    # The factor by which to multiply the amount in the given currency to
    # obtain the amount in the home currency of the Account.
    factor : str

@dataclass
class HomeConversionFactors:
    # The ConversionFactor in effect for the Account for converting any gains
    # realized in Instrument quote units into units of the Account's home
    # currency.
    gainQuoteHome : ConversionFactor

    # The ConversionFactor in effect for the Account for converting any losses
    # realized in Instrument quote units into units of the Account's home
    # currency.
    lossQuoteHome : ConversionFactor

    # The ConversionFactor in effect for the Account for converting any gains
    # realized in Instrument base units into units of the Account's home
    # currency.
    gainBaseHome : ConversionFactor

    # The ConversionFactor in effect for the Account for converting any losses
    # realized in Instrument base units into units of the Account's home
    # currency.
    lossBaseHome : ConversionFactor

@dataclass
class PriceBucket:
    """Represents a PriceValue from OANDA."""
    # The Price offered by the PriceBucket
    price : str

    # The amount of liquidity offered by the PriceBucket
    liquidity : float
    
@dataclass
class ClientPrice:
    """Represents a ClientPrice from OANDA."""
    # The string "PRICE". Used to identify the a Price object when found in a
    # stream.
    type : str

    # The Price's Instrument.
    instrument : str

    # The date/time when the Price was created
    time : str

    # Flag indicating if the Price is tradeable or not
    tradeable : bool

    # The list of prices and liquidity available on the Instrument's bid side.
    # It is possible for this list to be empty if there is no bid liquidity
    # currently available for the Instrument in the Account.
    bids : list[PriceBucket]

    # The list of prices and liquidity available on the Instrument's ask side.
    # It is possible for this list to be empty if there is no ask liquidity
    # currently available for the Instrument in the Account.
    asks : list[PriceBucket]

    # The closeout bid Price. This Price is used when a bid is required to
    # closeout a Position (margin closeout or manual) yet there is no bid
    # liquidity. The closeout bid is never used to open a new position.
    closeoutBid : str

    # The closeout ask Price. This Price is used when a ask is required to
    # closeout a Position (margin closeout or manual) yet there is no ask
    # liquidity. The closeout ask is never used to open a new position.
    closeoutAsk : str

@dataclass
class TradeOpen:
    """Represents a TradeOpen from OANDA."""
    # The ID of the Trade that was opened
    tradeID : str

    # The number of units opened by the Trade
    units : str

    # The average price that the units were opened at.
    price : str

    # This is the fee charged for opening the trade if it has a guaranteed Stop
    # Loss Order attached to it.
    guaranteedExecutionFee : str

    # This is the fee charged for opening the trade if it has a guaranteed Stop
    # Loss Order attached to it, expressed in the Instrument's quote currency.
    quoteGuaranteedExecutionFee : str

    # The half spread cost for the trade open. This can be a positive or
    # negative value and is represented in the home currency of the Account.
    halfSpreadCost : str

    # The margin required at the time the Trade was created. Note, this is the
    # 'pure' margin required, it is not the 'effective' margin used that
    # factors in the trade risk if a GSLO is attached to the trade.
    initialMarginRequired : str

    # The client extensions for the newly opened Trade
    clientExtensions : Optional[ClientExtensions] = None

@dataclass
class TradeReduce:
    """Represents a TradeReduce from OANDA."""
    # The ID of the Trade that was reduced or closed
    tradeID : str

    # The number of units that the Trade was reduced by
    units : str

    # The average price that the units were closed at. This price may be
    # clamped for guaranteed Stop Loss Orders.
    price : str

    # The PL realized when reducing the Trade
    realizedPL : str

    # The financing paid/collected when reducing the Trade
    financing : str

    # The base financing paid/collected when reducing the Trade
    baseFinancing : str

    # This is the fee that is charged for closing the Trade if it has a
    # guaranteed Stop Loss Order attached to it.
    guaranteedExecutionFee : str

    # This is the fee that is charged for closing the Trade if it has a
    # guaranteed Stop Loss Order attached to it, expressed in the Instrument's
    # quote currency.
    quoteGuaranteedExecutionFee : str

    # The half spread cost for the trade reduce/close. This can be a positive
    # or negative value and is represented in the home currency of the Account.
    halfSpreadCost : str

    # The quote financing paid/collected when reducing the Trade
    quoteFinancing : Optional[str] = None

    # The financing rate in effect for the instrument used to calculate the
    # amount of financing paid/collected when reducing the Trade. This field
    # will only be set if the AccountFinancingMode at the time of the order
    # fill is SECOND_BY_SECOND_INSTRUMENT. The value is in decimal rather than
    # percentage points, e.g. 5% is represented as 0.05.
    financingRate : Optional[str] = None

@dataclass
class TakeProfitDetails:
    # Specifies the distance (in price units) from the Trade's open price to
    # use as the Take Profit Order price. Only one of the distance and price
    # fields may be specified.
    # For OrynTrade, right now we are only using the distance field
    distance : str
    
    # The price that the Take Profit Order will be triggered at. Only one of
    # the price and distance fields may be specified.
    price : Optional[str] = None

    # The time in force for the created Take Profit Order. This may only be
    # GTC, GTD or GFD.
    timeInForce : Optional[str] = None

    # The date when the Take Profit Order will be cancelled on if timeInForce
    # is GTD.
    gtdTime : Optional[str] = None

    # The Client Extensions to add to the Take Profit Order when created.
    clientExtensions : Optional[ClientExtensions] = None

@dataclass
class StopLossDetails:
    # Specifies the distance (in price units) from the Trade's open price to
    # use as the Stop Loss Order price. Only one of the distance and price
    # fields may be specified.
    # For OrynTrade, right now we are only using the distance field
    distance : str
    
    # The price that the Stop Loss Order will be triggered at. Only one of the
    # price and distance fields may be specified.
    price : Optional[str] = None

    # The time in force for the created Stop Loss Order. This may only be GTC,
    # GTD or GFD.
    timeInForce : Optional[str] = None

    # The date when the Stop Loss Order will be cancelled on if timeInForce is
    # GTD.
    gtdTime : Optional[str] = None

    # The Client Extensions to add to the Stop Loss Order when created.
    clientExtensions : Optional[ClientExtensions] = None

@dataclass
class GuaranteedStopLossDetails:
    # The price that the Guaranteed Stop Loss Order will be triggered at. Only
    # one of the price and distance fields may be specified.
    price : str

    # Specifies the distance (in price units) from the Trade's open price to
    # use as the Guaranteed Stop Loss Order price. Only one of the distance and
    # price fields may be specified. 
    distance : str

    # The time in force for the created Guaranteed Stop Loss Order. This may
    # only be GTC, GTD or GFD.
    timeInForce : str

    # The date when the Guaranteed Stop Loss Order will be cancelled on if
    # timeInForce is GTD.
    gtdTime : str

    # The Client Extensions to add to the Guaranteed Stop Loss Order when
    # created.
    clientExtensions : ClientExtensions

@dataclass
class TrailingStopLossDetails:
    # The distance (in price units) from the Trade's fill price that the
    # Trailing Stop Loss Order will be triggered at.
    distance : str

    # The time in force for the created Trailing Stop Loss Order. This may only
    # be GTC, GTD or GFD.
    timeInForce : str

    # The date when the Trailing Stop Loss Order will be cancelled on if
    # timeInForce is GTD.
    gtdTime : str

    # The Client Extensions to add to the Trailing Stop Loss Order when
    # created.
    clientExtensions : ClientExtensions

@dataclass
class MarketOrderRequest:
    # The type of the Order to Create. Must be set to "MARKET" when creating a
    # Market Order.
    type : str

    # The Market Order's Instrument.
    instrument : str

    # The quantity requested to be filled by the Market Order. A positive
    # number of units results in a long Order, and a negative number of units
    # results in a short Order.
    units : str

    # The time-in-force requested for the Market Order. Restricted to FOK or
    # IOC for a MarketOrder.
    timeInForce : str

    # The worst price that the client is willing to have the Market Order
    # filled at.
    priceBound : Optional[str] = None

    # Specification of how Positions in the Account are modified when the Order
    # is filled.
    positionFill : Optional[str] = None

    # The client extensions to add to the Order. Do not set, modify, or delete
    # clientExtensions if your account is associated with MT4.
    clientExtensions : Optional[ClientExtensions] = None

    # TakeProfitDetails specifies the details of a Take Profit Order to be
    # created on behalf of a client. This may happen when an Order is filled
    # that opens a Trade requiring a Take Profit, or when a Trade's dependent
    # Take Profit Order is modified directly through the Trade.
    takeProfitOnFill : Optional[TakeProfitDetails] = None

    # StopLossDetails specifies the details of a Stop Loss Order to be created
    # on behalf of a client. This may happen when an Order is filled that opens
    # a Trade requiring a Stop Loss, or when a Trade's dependent Stop Loss
    # Order is modified directly through the Trade.
    stopLossOnFill : Optional[StopLossDetails] = None

    # GuaranteedStopLossDetails specifies the details of a Guaranteed Stop Loss
    # Order to be created on behalf of a client. This may happen when an Order
    # is filled that opens a Trade requiring a Guaranteed Stop Loss, or when a
    # Trade's dependent Guaranteed Stop Loss Order is modified directly through
    # the Trade.
    guaranteedStopLossOnFill : Optional[GuaranteedStopLossDetails] = None

    # TrailingStopLossDetails specifies the details of a Trailing Stop Loss
    # Order to be created on behalf of a client. This may happen when an Order
    # is filled that opens a Trade requiring a Trailing Stop Loss, or when a
    # Trade's dependent Trailing Stop Loss Order is modified directly through
    # the Trade.
    trailingStopLossOnFill : Optional[TrailingStopLossDetails] = None

    # Client Extensions to add to the Trade created when the Order is filled
    # (if such a Trade is created). Do not set, modify, or delete
    # tradeClientExtensions if your account is associated with MT4.
    tradeClientExtensions : Optional[ClientExtensions] = None

@dataclass
class OandaExecuteTradeRequest:
    """
    Structured request for OANDA's execute trade endpoint.
    POST /v3/accounts/{accountID}/orders
    """
    # Specification of the Order to create
    # NOTE: We are currently only using the MarketOrderRequest
    # There are other order types available, but we do not use them yet
    # List of the other order types: 
    # - LimitOrderRequest
    # - StopOrderRequest
    # - MarketIfTouchedOrderRequest
    # - TakeProfitOrderRequest
    # - StopLossOrderRequest
    # - GuaranteedStopLossOrderRequest
    order: MarketOrderRequest

    def to_dict(self) -> dict:
        """
        Convert the request to a dictionary, removing None values.
        Since requests.post() expects a dictionary
        """
        def clean_dict(d):
            """Recursively remove keys with None values."""
            if isinstance(d, dict):
                return {k: clean_dict(v) for k, v in d.items() if v is not None}
            elif isinstance(d, list):
                return [clean_dict(v) for v in d if v is not None]
            else:
                return d

        return clean_dict(asdict(self))

    def to_json(self) -> str:
        """
        Convert the request to a JSON string if needed.
        """
        return json.dumps(self.to_dict())

@dataclass
class OrderCancelTransaction:
    """Represents a OrderCancelTransaction from OANDA."""
    # The Transaction's Identifier.
    id : str
    
    # The date/time when the Transaction was created.
    time : str
    
    # The ID of the user that initiated the creation of the Transaction.
    userID : int
    
    # The ID of the Account the Transaction was created for.
    accountID : str
    
    # The ID of the "batch" that the Transaction belongs to. Transactions in
    # the same batch are applied to the Account simultaneously.
    batchID : str
    
    # The Request ID of the request which generated the transaction.
    requestID : str

    # The Type of the Transaction. Always set to "ORDER_CANCEL" for an
    # OrderCancelTransaction.
    type : str

    # The ID of the Order cancelled
    orderID : str

    # The reason that the Order was cancelled.
    reason : str

    # The client ID of the Order cancelled (only provided if the Order has a
    # client Order ID).
    clientOrderID : Optional[str] = None

    # The ID of the Order that replaced this Order (only provided if this Order
    # was cancelled for replacement).
    replacedByOrderID : Optional[str] = None

@dataclass
class OandaOrderFillTransaction:
    """Represents OrderFillTransaction from OANDA."""
    # The Transaction's Identifier.
    # https://developer.oanda.com/rest-live-v20/transaction-df/#TransactionID
    id: str
    
    # The date/time when the Transaction was created.
    # https://developer.oanda.com/rest-live-v20/primitives-df/#DateTime
    time: str
    
    # The ID of the user that initiated the creation of the Transaction.
    # https://developer.oanda.com/rest-live-v20/transaction-df/#UserID
    userID: int
    
    # The ID of the Account the Transaction was created for.
    # https://developer.oanda.com/rest-live-v20/account-df/#AccountID
    accountID: str
    
    # The ID of the "batch" that the Transaction belongs to. Transactions in
    # the same batch are applied to the Account simultaneously.
    # https://developer.oanda.com/rest-live-v20/transaction-df/#TransactionID
    batchID: str
    
    # The Request ID of the request which generated the transaction.
    requestID: str
    
    # The Type of the Transaction. Always set to "ORDER_FILL" for an
    # OrderFillTransaction.
    # https://developer.oanda.com/rest-live-v20/transaction-df/#TransactionType
    type: str

    # The name of the filled Order's instrument.
    # https://developer.oanda.com/rest-live-v20/primitives-df/#InstrumentName
    instrument : str

    # The number of units filled by the OrderFill.
    # https://developer.oanda.com/rest-live-v20/primitives-df/#DecimalNumber
    units : str

    # The price that all of the units of the OrderFill should have been filled
    # at, in the absence of guaranteed price execution. This factors in the
    # Account's current ClientPrice, used liquidity and the units of the
    # OrderFill only. If no Trades were closed with their price clamped for
    # guaranteed stop loss enforcement, then this value will match the price
    # fields of each Trade opened, closed, and reduced, and they will all be
    # the exact same.
    fullVWAP : str

    # The profit or loss incurred when the Order was filled.
    # pl = quotePL × HomeConversionFactors
    pl : str

    # The profit or loss incurred when the Order was filled, in the
    # Instrument's quote currency.
    quotePL : str

    # The financing paid or collected when the Order was filled.
    financing : str

    # The financing paid or collected when the Order was filled, in the
    # Instrument's base currency.
    baseFinancing : str

    # The commission charged in the Account's home currency as a result of
    # filling the Order. The commission is always represented as a positive
    # quantity of the Account's home currency, however it reduces the balance
    # in the Account.
    commission : str

    # The total guaranteed execution fees charged for all Trades opened, closed
    # or reduced with guaranteed Stop Loss Orders.
    guaranteedExecutionFee : str

    # The total guaranteed execution fees charged for all Trades opened, closed
    # or reduced with guaranteed Stop Loss Orders, expressed in the
    # Instrument's quote currency.
    quoteGuaranteedExecutionFee : str

    # The Account's balance after the Order was filled.
    accountBalance : str

    # The half spread cost for the OrderFill, which is the sum of the
    # halfSpreadCost values in the tradeOpened, tradesClosed and tradeReduced
    # fields. This can be a positive or negative value and is represented in
    # the home currency of the Account.
    halfSpreadCost : str

    # The HomeConversionFactors in effect at the time of the OrderFill.
    # https://developer.oanda.com/rest-live-v20/primitives-df/#HomeConversionFactors
    homeConversionFactors : HomeConversionFactors

    # The Trade that was opened when the Order was filled (only provided if
    # filling the Order resulted in a new Trade).
    tradeOpened : Optional[TradeOpen] = None

    # The Trades that were closed when the Order was filled (only provided if
    # filling the Order resulted in a closing open Trades).
    tradesClosed : Optional[list[TradeReduce]] = None

    # The Trade that was reduced when the Order was filled (only provided if
    # filling the Order resulted in reducing an open Trade).
    tradeReduced : Optional[TradeReduce] = None

    # The client Order ID of the Order filled (only provided if the client has
    # assigned one).
    # https://developer.oanda.com/rest-live-v20/transaction-df/#ClientID
    clientOrderID : Optional[str] = None

    # The number of units requested in the original Order.
    # This field is not documented in the API reference but is present in responses.
    requestedUnits : Optional[str] = None

    # The financing paid or collected when the Order was filled, in the
    # Instrument's quote currency.
    quoteFinancing : Optional[str] = None

    # The ID of the Order filled.
    # https://developer.oanda.com/rest-live-v20/order-df/#OrderID
    orderID : Optional[str] = None

    # The price in effect for the account at the time of the Order fill.
    fullPrice : Optional[ClientPrice] = None

    # The reason that an Order was filled
    # https://developer.oanda.com/rest-live-v20/transaction-df/#OrderFillReason
    reason : Optional[str] = None

    # This is the conversion factor in effect for the Account at the time of
    # the OrderFill for converting any gains realized in Instrument quote units
    # into units of the Account’s home currency.
    # Deprecated: Will be removed in a future API update.
    gainQuoteHomeConversionFactor : Optional[str] = None

    # This is the conversion factor in effect for the Account at the time of
    # the OrderFill for converting any losses realized in Instrument quote
    # units into units of the Account’s home currency.
    # Deprecated: Will be removed in a future API update.
    lossQuoteHomeConversionFactor : Optional[str] = None

    # This field is now deprecated and should no longer be used. The individual
    # tradesClosed, tradeReduced and tradeOpened fields contain the
    # exact/official price each unit was filled at.
    # Deprecated: Will be removed in a future API update.
    price : Optional[str] = None

@dataclass
class OandaExecuteTradeResponseSuccess:
    """
    Structured response from OANDA's execute trade endpoint.
    POST /v3/accounts/{accountID}/orders
    HTTP 201: The Order was created as specified
    """
    # The Transaction that created the Order specified by the request.
    orderCreateTransaction: OandaTransaction

    # The IDs of all Transactions that were created while satisfying the
    # request.
    relatedTransactionIDs: list[str]

    # The ID of the most recent Transaction created for the Account
    lastTransactionID: str
    
    # The Transaction that filled the newly created Order. Only provided when
    # the Order was immediately filled.
    orderFillTransaction: Optional[OandaOrderFillTransaction] = None

    # The Transaction that cancelled the newly created Order. Only provided
    # when the Order was immediately cancelled.
    orderCancelTransaction: Optional[OrderCancelTransaction] = None

    # The Transaction that reissues the Order. Only provided when the Order is
    # configured to be reissued for its remaining units after a partial fill
    # and the reissue was successful.
    orderReissueTransaction: Optional[OandaTransaction] = None

    # 
    # the Order is configured to be reissued for its remaining units after a
    # partial fill and the reissue was rejected.
    orderReissueRejectTransaction: Optional[OandaTransaction] = None

    @staticmethod
    def from_dict(data: dict) -> 'OandaExecuteTradeResponseSuccess':
        return OandaExecuteTradeResponseSuccess(
            orderCreateTransaction=data.get("orderCreateTransaction"),
            orderFillTransaction=OandaOrderFillTransaction(**data["orderFillTransaction"]) if "orderFillTransaction" in data else None,
            orderCancelTransaction=OrderCancelTransaction(**data["orderCancelTransaction"]) if "orderCancelTransaction" in data else None,
            relatedTransactionIDs=data.get("relatedTransactionIDs"),
            lastTransactionID=data.get("lastTransactionID")
        )

@dataclass
class OandaExecuteTradeResponseInvalid:
    """
    Structured response from OANDA's execute trade endpoint.
    POST /v3/accounts/{accountID}/orders
    HTTP 400: The Order specification was invalid
    """
    # The Transaction that rejected the creation of the Order as requested
    orderRejectTransaction : OandaTransaction

    # The IDs of all Transactions that were created while satisfying the
    # request.
    relatedTransactionIDs : list[str]

    # The ID of the most recent Transaction created for the Account
    lastTransactionID : str

    # The human-readable description of the error that has occurred.
    # This field is required.
    errorMessage : str

    # The code of the error that has occurred. This field may not be returned
    # for some errors.
    errorCode : Optional[str] = None

@dataclass
class OandaExecuteTradeResponseDoesNotExist:
    """
    Structured response from OANDA's execute trade endpoint.
    POST /v3/accounts/{accountID}/orders
    HTTP 404: The Order or Account specified does not exist.
    """
    # The Transaction that rejected the creation of the Order as requested.
    # Only present if the Account exists.
    orderRejectTransaction : OandaTransaction

    # The IDs of all Transactions that were created while satisfying the
    # request. Only present if the Account exists.
    relatedTransactionIDs : list[str]

    # The ID of the most recent Transaction created for the Account. Only
    # present if the Account exists.
    lastTransactionID : str

    # The human-readable description of the error that has occurred.
    errorMessage : str

    # The code of the error that has occurred. This field may not be returned
    # for some errors.
    errorCode : Optional[str] = None

@dataclass
class OandaOrderCreateTransaction:
    """Represents orderCreateTransaction in the response
    from OANDA's execute trade endpoint
    POST /v3/accounts/{accountID}/orders
    """
    orderFillTransaction: OandaOrderFillTransaction

@dataclass
class TakeProfitOrder:
    """Represents a TakeProfitOrder from OANDA."""
    # 
    # The Order’s identifier, unique within the Order’s Account.
    # 
    id : str

    # 
    # The time when the Order was created.
    # 
    createTime : str

    # 
    # The current state of the Order.
    # 
    state : str

    # 
    # The type of the Order. Always set to “TAKE_PROFIT” for Take Profit
    # Orders.
    # 
    type : str

    # 
    # The ID of the Trade to close when the price threshold is breached.
    # 
    tradeID : str

    # 
    # The price threshold specified for the TakeProfit Order. The associated
    # Trade will be closed by a market price that is equal to or better than
    # this threshold.
    # 
    price : str

    # 
    # The time-in-force requested for the TakeProfit Order. Restricted to
    # “GTC”, “GFD” and “GTD” for TakeProfit Orders.
    # 
    timeInForce : str

    # 
    # Specification of which price component should be used when determining if
    # an Order should be triggered and filled. This allows Orders to be
    # triggered based on the bid, ask, mid, default (ask for buy, bid for sell)
    # or inverse (ask for sell, bid for buy) price depending on the desired
    # behaviour. Orders are always filled using their default price component.
    # This feature is only provided through the REST API. Clients who choose to
    # specify a non-default trigger condition will not see it reflected in any
    # of OANDA’s proprietary or partner trading platforms, their transaction
    # history or their account statements. OANDA platforms always assume that
    # an Order’s trigger condition is set to the default value when indicating
    # the distance from an Order’s trigger price, and will always provide the
    # default trigger condition when creating or modifying an Order. A special
    # restriction applies when creating a Guaranteed Stop Loss Order. In this
    # case the TriggerCondition value must either be “DEFAULT”, or the
    # “natural” trigger side “DEFAULT” results in. So for a Guaranteed Stop
    # Loss Order for a long trade valid values are “DEFAULT” and “BID”, and for
    # short trades “DEFAULT” and “ASK” are valid.
    # 
    triggerCondition : str

    # 
    # The date/time when the TakeProfit Order will be cancelled if its
    # timeInForce is “GTD”.
    # 
    gtdTime : Optional[str] = None

    # 
    # The client ID of the Trade to be closed when the price threshold is
    # breached.
    # 
    clientTradeID : Optional[str] = None

    # 
    # The client extensions of the Order. Do not set, modify, or delete
    # clientExtensions if your account is associated with MT4.
    # 
    clientExtensions : Optional[ClientExtensions] = None

    # 
    # ID of the Transaction that filled this Order (only provided when the
    # Order’s state is FILLED)
    # 
    fillingTransactionID : Optional[str] = None

    # 
    # Date/time when the Order was filled (only provided when the Order’s state
    # is FILLED)
    # 
    filledTime : Optional[str] = None

    # 
    # Trade ID of Trade opened when the Order was filled (only provided when
    # the Order’s state is FILLED and a Trade was opened as a result of the
    # fill)
    # 
    tradeOpenedID : Optional[str] = None

    # 
    # Trade ID of Trade reduced when the Order was filled (only provided when
    # the Order’s state is FILLED and a Trade was reduced as a result of the
    # fill)
    # 
    tradeReducedID : Optional[str] = None

    # 
    # Trade IDs of Trades closed when the Order was filled (only provided when
    # the Order’s state is FILLED and one or more Trades were closed as a
    # result of the fill)
    # 
    tradeClosedIDs : Optional[list[str]] = None

    # 
    # ID of the Transaction that cancelled the Order (only provided when the
    # Order’s state is CANCELLED)
    # 
    cancellingTransactionID : Optional[str] = None

    # 
    # Date/time when the Order was cancelled (only provided when the state of
    # the Order is CANCELLED)
    # 
    cancelledTime : Optional[str] = None

    # 
    # The ID of the Order that was replaced by this Order (only provided if
    # this Order was created as part of a cancel/replace).
    # 
    replacesOrderID : Optional[str] = None

    # 
    # The ID of the Order that replaced this Order (only provided if this Order
    # was cancelled as part of a cancel/replace).
    # 
    replacedByOrderID : Optional[str] = None

@dataclass
class StopLossOrder:
    """Represents a Stop Loss Order from OANDA."""
    # 
    # The Order’s identifier, unique within the Order’s Account.
    # 
    id : str

    # 
    # The time when the Order was created.
    # 
    createTime : str

    # 
    # The current state of the Order.
    # 
    state : str

    # 
    # The type of the Order. Always set to “STOP_LOSS” for Stop Loss Orders.
    # 
    type : str

    # 
    # The ID of the Trade to close when the price threshold is breached.
    # 
    tradeID : str

    # 
    # The price threshold specified for the Stop Loss Order. The associated
    # Trade will be closed by a market price that is equal to or worse than
    # this threshold.
    # 
    price : str

    # 
    # The time-in-force requested for the StopLoss Order. Restricted to “GTC”,
    # “GFD” and “GTD” for StopLoss Orders.
    # 
    timeInForce : str

    # 
    # Specification of which price component should be used when determining if
    # an Order should be triggered and filled. This allows Orders to be
    # triggered based on the bid, ask, mid, default (ask for buy, bid for sell)
    # or inverse (ask for sell, bid for buy) price depending on the desired
    # behaviour. Orders are always filled using their default price component.
    # This feature is only provided through the REST API. Clients who choose to
    # specify a non-default trigger condition will not see it reflected in any
    # of OANDA’s proprietary or partner trading platforms, their transaction
    # history or their account statements. OANDA platforms always assume that
    # an Order’s trigger condition is set to the default value when indicating
    # the distance from an Order’s trigger price, and will always provide the
    # default trigger condition when creating or modifying an Order. A special
    # restriction applies when creating a Guaranteed Stop Loss Order. In this
    # case the TriggerCondition value must either be “DEFAULT”, or the
    # “natural” trigger side “DEFAULT” results in. So for a Guaranteed Stop
    # Loss Order for a long trade valid values are “DEFAULT” and “BID”, and for
    # short trades “DEFAULT” and “ASK” are valid.
    # 
    triggerCondition : str

    # 
    # The trigger mode for the StopLoss Order.
    # 
    triggerMode : Optional[str] = None

    # 
    # The date/time when the StopLoss Order will be cancelled if its
    # timeInForce is “GTD”.
    # 
    gtdTime : Optional[str] = None

    # 
    # Specifies the distance (in price units) from the Account’s current price
    # to use as the Stop Loss Order price. If the Trade is short the
    # Instrument’s bid price is used, and for long Trades the ask is used.
    # 
    distance : Optional[str] = None

    # 
    # The client ID of the Trade to be closed when the price threshold is
    # breached.
    # 
    clientTradeID : Optional[str] = None

    # 
    # The premium that will be charged if the Stop Loss Order is guaranteed and
    # the Order is filled at the guaranteed price. It is in price units and is
    # charged for each unit of the Trade.
    # 
    # 
    # Deprecated: Will be removed in a future API update.
    # 
    guaranteedExecutionPremium : Optional[str] = None

    # 
    # The client extensions of the Order. Do not set, modify, or delete
    # clientExtensions if your account is associated with MT4.
    # 
    clientExtensions : Optional[ClientExtensions] = None

    # 
    # Flag indicating that the Stop Loss Order is guaranteed. The default value
    # depends on the GuaranteedStopLossOrderMode of the account, if it is
    # REQUIRED, the default will be true, for DISABLED or ENABLED the default
    # is false.
    # 
    # 
    # Deprecated: Will be removed in a future API update.
    # 
    guaranteed : Optional[bool] = None

    # 
    # ID of the Transaction that filled this Order (only provided when the
    # Order’s state is FILLED)
    # 
    fillingTransactionID : Optional[str] = None

    # 
    # Date/time when the Order was filled (only provided when the Order’s state
    # is FILLED)
    # 
    filledTime : Optional[str] = None

    # 
    # Trade ID of Trade opened when the Order was filled (only provided when
    # the Order’s state is FILLED and a Trade was opened as a result of the
    # fill)
    # 
    tradeOpenedID : Optional[str] = None

    # 
    # Trade ID of Trade reduced when the Order was filled (only provided when
    # the Order’s state is FILLED and a Trade was reduced as a result of the
    # fill)
    # 
    tradeReducedID : Optional[str] = None

    # 
    # Trade IDs of Trades closed when the Order was filled (only provided when
    # the Order’s state is FILLED and one or more Trades were closed as a
    # result of the fill)
    # 
    tradeClosedIDs : Optional[list[str]] = None

    # 
    # ID of the Transaction that cancelled the Order (only provided when the
    # Order’s state is CANCELLED)
    # 
    cancellingTransactionID : Optional[str] = None

    # 
    # Date/time when the Order was cancelled (only provided when the state of
    # the Order is CANCELLED)
    # 
    cancelledTime : Optional[str] = None

    # 
    # The ID of the Order that was replaced by this Order (only provided if
    # this Order was created as part of a cancel/replace).
    # 
    replacesOrderID : Optional[str] = None

    # 
    # The ID of the Order that replaced this Order (only provided if this Order
    # was cancelled as part of a cancel/replace).
    # 
    replacedByOrderID : Optional[str] = None
    
@dataclass
class TrailingStopLossOrder:
    """Represents a TrailingStopLossOrder from OANDA."""
    # 
    # The Order’s identifier, unique within the Order’s Account.
    # 
    id : str

    # 
    # The time when the Order was created.
    # 
    createTime : str

    # 
    # The current state of the Order.
    # 
    state : str

    # 
    # The type of the Order. Always set to “TRAILING_STOP_LOSS” for Trailing
    # Stop Loss Orders.
    # 
    type : str

    # 
    # The ID of the Trade to close when the price threshold is breached.
    # 
    tradeID : str

    # 
    # The time-in-force requested for the TrailingStopLoss Order. Restricted to
    # “GTC”, “GFD” and “GTD” for TrailingStopLoss Orders.
    # 
    timeInForce : str

    # 
    # Specification of which price component should be used when determining if
    # an Order should be triggered and filled. This allows Orders to be
    # triggered based on the bid, ask, mid, default (ask for buy, bid for sell)
    # or inverse (ask for sell, bid for buy) price depending on the desired
    # behaviour. Orders are always filled using their default price component.
    # This feature is only provided through the REST API. Clients who choose to
    # specify a non-default trigger condition will not see it reflected in any
    # of OANDA’s proprietary or partner trading platforms, their transaction
    # history or their account statements. OANDA platforms always assume that
    # an Order’s trigger condition is set to the default value when indicating
    # the distance from an Order’s trigger price, and will always provide the
    # default trigger condition when creating or modifying an Order. A special
    # restriction applies when creating a Guaranteed Stop Loss Order. In this
    # case the TriggerCondition value must either be “DEFAULT”, or the
    # “natural” trigger side “DEFAULT” results in. So for a Guaranteed Stop
    # Loss Order for a long trade valid values are “DEFAULT” and “BID”, and for
    # short trades “DEFAULT” and “ASK” are valid.
    # 
    triggerCondition : str

    # 
    # The trigger price for the Trailing Stop Loss Order. The trailing stop
    # value will trail (follow) the market price by the TSL order’s configured
    # “distance” as the market price moves in the winning direction. If the
    # market price moves to a level that is equal to or worse than the trailing
    # stop value, the order will be filled and the Trade will be closed.
    # 
    trailingStopValue : str

    # 
    # The date/time when the StopLoss Order will be cancelled if its
    # timeInForce is “GTD”.
    # 
    gtdTime : Optional[str] = None

    # 
    # The client ID of the Trade to be closed when the price threshold is
    # breached.
    # 
    clientTradeID : Optional[str] = None

    # 
    # The price distance (in price units) specified for the TrailingStopLoss
    # Order.
    # 
    distance : Optional[str] = None

    # 
    # The client extensions of the Order. Do not set, modify, or delete
    # clientExtensions if your account is associated with MT4.
    # 
    clientExtensions : Optional[ClientExtensions] = None


    # 
    # ID of the Transaction that filled this Order (only provided when the
    # Order’s state is FILLED)
    # 
    fillingTransactionID : Optional[str] = None

    # 
    # Date/time when the Order was filled (only provided when the Order’s state
    # is FILLED)
    # 
    filledTime : Optional[str] = None

    # 
    # Trade ID of Trade opened when the Order was filled (only provided when
    # the Order’s state is FILLED and a Trade was opened as a result of the
    # fill)
    # 
    tradeOpenedID : Optional[str] = None

    # 
    # Trade ID of Trade reduced when the Order was filled (only provided when
    # the Order’s state is FILLED and a Trade was reduced as a result of the
    # fill)
    # 
    tradeReducedID : Optional[str] = None

    # 
    # Trade IDs of Trades closed when the Order was filled (only provided when
    # the Order’s state is FILLED and one or more Trades were closed as a
    # result of the fill)
    # 
    tradeClosedIDs : Optional[list[str]] = None

    # 
    # ID of the Transaction that cancelled the Order (only provided when the
    # Order’s state is CANCELLED)
    # 
    cancellingTransactionID : Optional[str] = None

    # 
    # Date/time when the Order was cancelled (only provided when the state of
    # the Order is CANCELLED)
    # 
    cancelledTime : Optional[str] = None

    # 
    # The ID of the Order that was replaced by this Order (only provided if
    # this Order was created as part of a cancel/replace).
    # 
    replacesOrderID : Optional[str] = None

    # 
    # The ID of the Order that replaced this Order (only provided if this Order
    # was cancelled as part of a cancel/replace).
    # 
    replacedByOrderID : Optional[str] = None

@dataclass
class Trade:
    """Represents a Trade from OANDA."""
    # 
    # The Trade’s identifier, unique within the Trade’s Account.
    # 
    id: str

    # 
    # The Trade’s Instrument.
    # 
    instrument: str

    # 
    # The execution price of the Trade.
    # 
    price: str

    # 
    # The date/time when the Trade was opened.
    # 
    openTime: str

    # 
    # The current state of the Trade.
    # 
    state: str

    # 
    # The initial size of the Trade. Negative values indicate a short Trade,
    # and positive values indicate a long Trade.
    # 
    initialUnits : str

    # 
    # The margin required at the time the Trade was created. Note, this is the
    # ‘pure’ margin required, it is not the ‘effective’ margin used that
    # factors in the trade risk if a GSLO is attached to the trade.
    # 
    initialMarginRequired : str

    # 
    # The number of units currently open for the Trade. This value is reduced
    # to 0.0 as the Trade is closed.
    # 
    currentUnits : str

    # 
    # The total profit/loss realized on the closed portion of the Trade.
    # 
    realizedPL : str

    # 
    # The unrealized profit/loss on the open portion of the Trade.
    # 
    unrealizedPL : str

    # 
    # Margin currently used by the Trade.
    # 
    marginUsed : str

    # 
    # The financing paid/collected for this Trade.
    # 
    financing : str

    # 
    # The dividend adjustment paid for this Trade.
    # 
    dividendAdjustment : str

    # 
    # The IDs of the Transactions that have closed portions of this Trade.
    # 
    closingTransactionIDs : Optional[list[str]] = None

    # 
    # The average closing price of the Trade. Only present if the Trade has
    # been closed or reduced at least once.
    # 
    averageClosePrice : Optional[str] = None

    # 
    # The date/time when the Trade was fully closed. Only provided for Trades
    # whose state is CLOSED.
    # 
    closeTime : Optional[str] = None

    # 
    # The client extensions of the Trade.
    # 
    clientExtensions : Optional[ClientExtensions] = None

    # 
    # Full representation of the Trade’s Take Profit Order, only provided if
    # such an Order exists.
    # 
    takeProfitOrder : Optional[TakeProfitOrder] = None

    # 
    # Full representation of the Trade’s Stop Loss Order, only provided if such
    # an Order exists.
    # 
    stopLossOrder : Optional[StopLossOrder] = None

    # 
    # Full representation of the Trade’s Trailing Stop Loss Order, only
    # provided if such an Order exists.
    # 
    trailingStopLossOrder : Optional[TrailingStopLossOrder] = None



@dataclass
class OandaOpenTradesResponse:
    """
    Structured response from OANDA's open trades endpoint.
    GET /v3/accounts/{accountID}/openTrades
    """
    # The Account’s list of open Trades
    trades : list[Trade]

    # The ID of the most recent Transaction created for the Account
    lastTransactionID : Optional[str] = None

    @staticmethod
    def from_dict(data: dict) -> 'OandaOpenTradesResponse':
        return OandaOpenTradesResponse(
            trades=[Trade(**trade) for trade in data.get("trades", [])],
            lastTransactionID=data.get("lastTransactionID")
        )
