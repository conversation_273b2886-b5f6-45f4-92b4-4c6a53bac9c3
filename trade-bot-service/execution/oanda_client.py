from typing import Dict, List, Optional, Any, Union, Tuple
import os
import time
import threading
from datetime import datetime, timezone, timedelta
import requests
from utils.logger import Logger
from utils.retry_utils import with_retry, RetryableError
from execution.firebase_client import FirebaseClient
from utils.oanda_utils import get_oanda_url
from utils.connection_manager import ConnectionManager, ConnectionState
from execution.oanda_types import OandaExecuteTradeRequest, MarketOrderRequest, StopLossDetails, TakeProfitDetails, OandaExecuteTradeResponseSuccess, OandaExecuteTradeResponseInvalid, OandaExecuteTradeResponseDoesNotExist, OandaOrderFillTransaction, TradeOpen, OandaOpenTradesResponse, Trade, TakeProfitOrder, StopLossOrder, TradeReduce
from core.trading_engine_types import TradeExecutionResponse, TradeType, TradeHistoryRow, TradeStatus

class OandaClient:
    """Client for interacting with OANDA API."""

    def __init__(self, firebase_client: FirebaseClient, health_client=None):
        """
        Initialize OANDA client.

        Args:
            firebase_client (FirebaseClient): Firebase client instance for credentials
            health_client: Optional health client for reporting connection status
        """
        self.logger = Logger("oanda_client")
        self.firebase_client = firebase_client
        self.health_client = health_client
        self.instrument_precision = {}

        # Cache for API responses
        self.cache = {
            'account_summary': {'data': None, 'timestamp': 0, 'ttl': 10},  # 10 second TTL
            'open_trades': {'data': None, 'timestamp': 0, 'ttl': 5},        # 5 second TTL
            'instruments': {'data': None, 'timestamp': 0, 'ttl': 3600}      # 1 hour TTL
        }

        # Performance metrics
        self.metrics = {
            'api_calls': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'execution_times': {}
        }

        # Track order IDs to prevent duplicates
        self.processed_order_ids = set()
        self.last_transaction_id = None

        try:
            # Get OANDA credentials from Firebase
            self.logger.log_info("Fetching OANDA credentials...")
            self.base_url = get_oanda_url()
            credentials = self.firebase_client.get_oanda_credentials()

            if not credentials:
                raise ValueError("Failed to get OANDA credentials")

            self.api_key = credentials["api_key"]
            self.account_id = credentials["account_id"]

            # Initialize connection manager
            self.connection_manager = ConnectionManager("oanda", health_client)

            # Register connection event callbacks
            self.connection_manager.register_callback("disconnect", self._on_disconnect)
            self.connection_manager.register_callback("reconnect", self._on_reconnect)
            self.connection_manager.register_callback("failure", self._on_connection_failure)

            # Just mark as connected initially without checking
            # We'll check the connection on the first API call
            self.connection_manager.connect()

            # Schedule a connection check to run soon but not immediately
            threading.Timer(1.0, self._delayed_connection_check).start()

        except Exception as e:
            self.logger.log_error(f"Error initializing OANDA client: {e}")
            # Set default values to prevent attribute errors
            self.api_key = None
            self.account_id = None
            self.base_url = get_oanda_url()

    def _get_from_cache(self, cache_key: str) -> Tuple[bool, Any]:
        """
        Get data from cache if it exists and is not expired.

        Args:
            cache_key (str): Cache key to retrieve

        Returns:
            Tuple[bool, Any]: (hit, data) where hit is True if cache hit, False otherwise
        """
        if cache_key not in self.cache:
            return False, None

        cache_entry = self.cache[cache_key]
        current_time = time.time()

        # Check if cache is valid
        if cache_entry['data'] is not None and current_time - cache_entry['timestamp'] < cache_entry['ttl']:
            self.metrics['cache_hits'] += 1
            return True, cache_entry['data']

        self.metrics['cache_misses'] += 1
        return False, None

    def _update_cache(self, cache_key: str, data: Any) -> None:
        """
        Update cache with new data.

        Args:
            cache_key (str): Cache key to update
            data (Any): Data to cache
        """
        if cache_key in self.cache:
            self.cache[cache_key]['data'] = data
            self.cache[cache_key]['timestamp'] = time.time()

    def _track_execution_time(self, operation: str, start_time: float) -> None:
        """
        Track execution time for an operation.

        Args:
            operation (str): Operation name
            start_time (float): Start time from time.time()
        """
        execution_time = time.time() - start_time

        if operation not in self.metrics['execution_times']:
            self.metrics['execution_times'][operation] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0
            }

        metrics = self.metrics['execution_times'][operation]
        metrics['count'] += 1
        metrics['total_time'] += execution_time
        metrics['min_time'] = min(metrics['min_time'], execution_time)
        metrics['max_time'] = max(metrics['max_time'], execution_time)

    def _delayed_connection_check(self):
        """
        Perform a delayed connection check.
        This is called after initialization to verify the connection is working.
        """
        try:
            self.logger.log_info("Performing delayed connection check...")
            self._check_connection()
        except Exception as e:
            self.logger.log_error(f"Delayed connection check failed: {e}")

    def _on_disconnect(self):
        """
        Handle disconnect event.
        """
        self.logger.log_warning("OANDA connection disconnected")
        if self.health_client:
            self.health_client.update_dependency("oanda", "unhealthy", "Connection disconnected")

    def _on_reconnect(self):
        """
        Handle reconnect event.
        """
        self.logger.log_info("OANDA connection reconnected")
        if self.health_client:
            self.health_client.update_dependency("oanda", "healthy", "Connection restored")

    def _on_connection_failure(self):
        """
        Handle connection failure event.
        """
        self.logger.log_error("OANDA connection failed repeatedly")
        if self.health_client:
            self.health_client.update_dependency("oanda", "critical", "Connection failed repeatedly")

    def _check_connection(self) -> bool:
        """
        Check if connection to OANDA API is working.

        Returns:
            bool: True if connection is working, False otherwise
        """
        try:
            # Make a simple request to check connection
            self.get_account_summary()
            self.logger.log_info("Connection check successful")
            return True
        except Exception as e:
            self.logger.log_error(f"Connection check failed: {str(e)}")
            return False

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for the OANDA client.

        Returns:
            Dict[str, Any]: Performance metrics
        """
        metrics = self.metrics.copy()

        # Calculate average execution times
        for operation, data in metrics['execution_times'].items():
            if data['count'] > 0:
                data['avg_time'] = data['total_time'] / data['count']

        return metrics

    def _on_disconnect(self):
        """Handle disconnection events."""
        self.logger.log_warning("Disconnected from OANDA API")
        if self.firebase_client:
            self.firebase_client.append_user_log(
                "⚠️ Connection to broker temporarily lost. Attempting to reconnect..."
            )

    def _on_reconnect(self):
        """Handle reconnection events."""
        self.logger.log_info("Reconnected to OANDA API")
        if self.firebase_client:
            self.firebase_client.append_user_log(
                "✅ Connection to broker restored."
            )

    def _on_connection_failure(self):
        """Handle connection failure events."""
        self.logger.log_error("Failed to reconnect to OANDA API")
        if self.firebase_client:
            self.firebase_client.append_user_log(
                "❌ Unable to reconnect to broker after multiple attempts. Please check your internet connection."
            )

    def _delayed_connection_check(self):
        """Run a delayed connection check after initialization."""
        try:
            self.logger.log_info("Running delayed connection check...")
            self.connection_manager.check_connection(self._check_connection, timeout=10.0)
        except Exception as e:
            self.logger.log_warning(f"Delayed connection check failed: {str(e)}")

    def _check_connection(self) -> bool:
        """Check if connection to OANDA API is working.

        Returns:
            bool: True if connection is working, False otherwise
        """
        try:
            # Make a simple request to check connection
            self.get_account_summary()
            self.logger.log_info("Connection check successful")
            return True
        except Exception as e:
            self.logger.log_error(f"Connection check failed: {str(e)}")
            return False

    def get_account_balance(self) -> float:
        """Get current account balance."""
        try:
            response = self._make_request("GET", f"/accounts/{self.account_id}")
            return float(response["account"]["balance"])
        except Exception as e:
            self.logger.log_error(e, "Failed to get account balance")
            raise

    @with_retry(max_retries=3, base_delay=1.0, max_delay=10.0, use_circuit_breaker=True)
    def _make_oanda_request(self, method: str, url: str, headers: Dict[str, str], data: Optional[Dict[str, Any]] = None) -> requests.Response:
        """Make an HTTP request to OANDA API with retry logic and circuit breaker pattern.

        Args:
            method (str): HTTP method
            url (str): Full URL
            headers (Dict[str, str]): Request headers
            data (Dict[str, Any], optional): Request data for POST requests

        Returns:
            requests.Response: The HTTP response

        Raises:
            RetryableError: If the request should be retried
            requests.exceptions.RequestException: For other request errors
        """
        # Check connection state and update if needed
        if hasattr(self, 'connection_manager'):
            if self.connection_manager.state == ConnectionState.DISCONNECTED:
                self.logger.log_info("Connection is disconnected, attempting to reconnect before request")
                try:
                    self.connection_manager.reconnect(self._check_connection)
                except Exception as e:
                    self.logger.log_warning(f"Reconnection attempt failed: {str(e)}")
                    # Continue with the request anyway

        self.logger.log_info(f"Making {method} request to OANDA API: {url}")

        try:
            # Use a session for better connection pooling and reliability
            session = requests.Session()

            # Set longer timeouts for better handling of slow connections
            # connect_timeout: time to establish the connection
            # read_timeout: time to receive the first byte after connection is established
            connect_timeout = 10.0  # seconds
            read_timeout = 30.0  # seconds
            timeout = (connect_timeout, read_timeout)

            # Add additional headers for better debugging
            headers.update({
                'User-Agent': 'TradingBot/1.0',
                'Accept': 'application/json'
            })

            if method == "GET":
                response = session.get(url, headers=headers, timeout=timeout)
            elif method == "POST":
                response = session.post(url, headers=headers, json=data, timeout=timeout)
            elif method == "PUT":
                response = session.put(url, headers=headers, json=data, timeout=timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            self.logger.log_info(f"Response status code: {response.status_code}")

            # Handle rate limiting (429) or server errors (5xx)
            if response.status_code == 429 or 500 <= response.status_code < 600:
                self.logger.log_warning(f"Retryable error: OANDA API returned status {response.status_code}")
                self.logger.log_warning(f"Response headers: {response.headers}")

                # If we have a Retry-After header, log it
                retry_after = response.headers.get('Retry-After')
                if retry_after:
                    self.logger.log_info(f"Retry-After header: {retry_after}")

                # Raise a RetryableError to trigger the retry mechanism
                raise RetryableError(f"OANDA API returned status {response.status_code}")

            # For other error status codes, just raise the standard exception
            if not response.ok:
                self.logger.log_error(f"OANDA API error: Status {response.status_code}")
                self.logger.log_error(f"Response headers: {response.headers}")
                self.logger.log_error(f"Response text: {response.text}")
                try:
                    error_data = response.json()
                    self.logger.log_error(f"Error details: {error_data}")
                except:
                    self.logger.log_error("Could not parse error response as JSON")
                response.raise_for_status()  # This will raise an HTTPError

            # Update connection state on success
            if hasattr(self, 'connection_manager') and self.connection_manager.state != ConnectionState.CONNECTED:
                self.connection_manager.state = ConnectionState.CONNECTED
                self.connection_manager.last_connected_time = datetime.now(timezone.utc)
                self.logger.log_info("Connection state updated to CONNECTED after successful request")

            return response

        except requests.exceptions.ConnectTimeout as e:
            self.logger.log_error(f"Connection timeout while connecting to OANDA API: {str(e)}")
            # Update connection state
            if hasattr(self, 'connection_manager'):
                self.connection_manager.state = ConnectionState.DISCONNECTED
                self.connection_manager.last_disconnected_time = datetime.now(timezone.utc)
            raise RetryableError(f"Connection timeout: {str(e)}") from e

        except requests.exceptions.ReadTimeout as e:
            self.logger.log_error(f"Read timeout while waiting for OANDA API response: {str(e)}")
            # Update connection state
            if hasattr(self, 'connection_manager'):
                self.connection_manager.state = ConnectionState.DISCONNECTED
                self.connection_manager.last_disconnected_time = datetime.now(timezone.utc)
            raise RetryableError(f"Read timeout: {str(e)}") from e

        except requests.exceptions.ConnectionError as e:
            self.logger.log_error(f"Connection error with OANDA API: {str(e)}")
            # Update connection state
            if hasattr(self, 'connection_manager'):
                self.connection_manager.state = ConnectionState.DISCONNECTED
                self.connection_manager.last_disconnected_time = datetime.now(timezone.utc)
            raise RetryableError(f"Connection error: {str(e)}") from e

        except requests.exceptions.SSLError as e:
            self.logger.log_error(f"SSL error with OANDA API: {str(e)}")
            # Update connection state
            if hasattr(self, 'connection_manager'):
                self.connection_manager.state = ConnectionState.DISCONNECTED
                self.connection_manager.last_disconnected_time = datetime.now(timezone.utc)
            raise RetryableError(f"SSL error: {str(e)}") from e

        except requests.exceptions.RequestException as e:
            self.logger.log_error(f"Request exception with OANDA API: {str(e)}")
            # Update connection state for other request exceptions
            if hasattr(self, 'connection_manager'):
                self.connection_manager.state = ConnectionState.DISCONNECTED
                self.connection_manager.last_disconnected_time = datetime.now(timezone.utc)
            raise

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make an HTTP request to OANDA API.

        Args:
            method (str): HTTP method
            endpoint (str): API endpoint
            data (dict, optional): Request data

        Returns:
            Dict[str, Any]: Parsed JSON response
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            url = f"{self.base_url}{endpoint}"

            # Check connection status and attempt reconnection if needed
            if hasattr(self, 'connection_manager') and self.connection_manager.state != ConnectionState.CONNECTED:
                self.logger.log_warning(f"Connection to OANDA is {self.connection_manager.state}, attempting to reconnect")
                self.connection_manager.reconnect(self._check_connection)

            # Use the retry mechanism to make the API call
            response = self._make_oanda_request(method, url, headers, data)

            # If we get here, the request was successful, so update connection status
            if hasattr(self, 'connection_manager'):
                if self.connection_manager.state != ConnectionState.CONNECTED:
                    self.connection_manager.state = ConnectionState.CONNECTED
                    self.logger.log_info("Connection to OANDA restored")

            # Check for duplicate transaction IDs to prevent duplicate trades
            if method == "POST" and "/orders" in endpoint:
                # Extract transaction ID from response
                response_json = response.json()
                if "orderCreateTransaction" in response_json and "id" in response_json["orderCreateTransaction"]:
                    transaction_id = response_json["orderCreateTransaction"]["id"]

                    # Check if we've already processed this transaction
                    if transaction_id in self.processed_order_ids:
                        self.logger.log_warning(f"Duplicate transaction detected: {transaction_id}. Ignoring.")
                        raise ValueError(f"Duplicate transaction detected: {transaction_id}")

                    # Add to processed transactions
                    self.processed_order_ids.add(transaction_id)

                    # Keep the set from growing too large
                    if len(self.processed_order_ids) > 1000:
                        # Remove oldest entries (assuming transaction IDs are sequential)
                        self.processed_order_ids = set(sorted(self.processed_order_ids)[-500:])

            return response.json()

        except requests.exceptions.RequestException as e:
            # Update connection status
            if hasattr(self, 'connection_manager'):
                self.connection_manager.state = ConnectionState.DISCONNECTED
                # Trigger reconnection process
                self.connection_manager.reconnect(self._check_connection)

            self.logger.log_error(e, f"API request failed: {method} {endpoint}")
            raise

    def get_account_summary(self) -> Optional[Dict]:
        """
        Get account summary including balance and open positions.

        Returns:
            Optional[Dict]: Account summary or None if request fails
        """
        try:
            # Check cache first
            start_time = time.time()
            cache_hit, cached_data = self._get_from_cache('account_summary')
            if cache_hit:
                self.logger.log_info("Using cached account summary data")
                return cached_data

            # Use the _make_request method which already has retry logic
            self.metrics['api_calls'] += 1
            data = self._make_request("GET", f"/accounts/{self.account_id}/summary")

            # Only log summary at debug level to reduce verbosity
            self.logger.log_info(f"Account balance: {data.get('account', {}).get('balance')}")

            account_data = data.get("account", {})

            # Update cache
            self._update_cache('account_summary', account_data)

            # Track execution time
            self._track_execution_time('get_account_summary', start_time)

            return account_data

        except Exception as e:
            self.logger.log_error(f"Error getting account summary: {e}")
            self.logger.log_error(f"Exception type: {type(e).__name__}")

            # Log more details for debugging
            if hasattr(e, 'response') and e.response is not None:
                self.logger.log_error(f"HTTP Status Code: {e.response.status_code}")
                self.logger.log_error(f"Response Headers: {e.response.headers}")
                try:
                    self.logger.log_error(f"Response Body: {e.response.text}")
                except:
                    pass

            return None

    def get_open_positions(self) -> List[Dict]:
        """
        Get list of open positions.

        Returns:
            List[Dict]: List of open positions
        """
        try:
            # Use the _make_request method which already has retry logic
            data = self._make_request("GET", f"/accounts/{self.account_id}/openPositions")
            positions = data.get("positions", [])

            # Format positions for internal use
            formatted_positions = []
            for position in positions:
                if float(position.get("long", {}).get("units", 0)) > 0:
                    formatted_positions.append({
                        "instrument": position.get("instrument"),
                        "units": float(position.get("long", {}).get("units", 0)),
                        "averagePrice": float(position.get("long", {}).get("averagePrice", 0)),
                        "unrealizedPL": float(position.get("unrealizedPL", 0)),
                        "realizedPL": float(position.get("pl", 0)),
                        "status": "open",
                        "type": "buy",
                        "openTime": position.get("long", {}).get("openTime"),
                        "transactionId": position.get("long", {}).get("tradeIDs", [None])[0],
                        "orderId": position.get("long", {}).get("orderID"),
                    })
                elif float(position.get("short", {}).get("units", 0)) > 0:
                    formatted_positions.append({
                        "instrument": position.get("instrument"),
                        "units": float(position.get("short", {}).get("units", 0)),
                        "averagePrice": float(position.get("short", {}).get("averagePrice", 0)),
                        "unrealizedPL": float(position.get("unrealizedPL", 0)),
                        "realizedPL": float(position.get("pl", 0)),
                        "status": "open",
                        "type": "sell",
                        "openTime": position.get("short", {}).get("openTime"),
                        "transactionId": position.get("short", {}).get("tradeIDs", [None])[0],
                        "orderId": position.get("short", {}).get("orderID"),
                    })

            return formatted_positions

        except Exception as e:
            self.logger.log_error(f"Error getting open positions: {e}")
            return []

    def get_open_trades(self) -> List[TradeHistoryRow]:
        """
        Get list of open trades.

        Returns:
            List[TradeHistoryRow]: List of open trades
        """
        try:
            # Check cache first
            start_time = time.time()
            cache_hit, cached_data = self._get_from_cache('open_trades')
            if cache_hit:
                self.logger.log_info("Using cached open trades data")
                return cached_data

            # Use the _make_request method which already has retry logic
            self.metrics['api_calls'] += 1
            data = self._make_request("GET", f"/accounts/{self.account_id}/openTrades")

            self.logger.log_info(f"OANDA open trades response data: {data}")

            oanda_open_trades_response = OandaOpenTradesResponse.from_dict(data)

            trade_history_rows = self.convert_oanda_open_trades_to_trade_history_row(oanda_open_trades_response.trades)

            # Update cache
            self._update_cache('open_trades', trade_history_rows)

            # Track execution time
            self._track_execution_time('get_open_trades', start_time)

            return trade_history_rows

        except Exception as e:
            self.logger.log_error(f"Error getting open trades: {e}")
            return []

    def convert_oanda_open_trades_to_trade_history_row(self, open_trades: List[Trade]) -> List[TradeHistoryRow]:
        """
        Convert OANDA open trades to TradeHistoryRow.
        """
        result = []
        for trade in open_trades:
            takeProfitOrder = None
            stopLossOrder = None

            if trade.takeProfitOrder:
                takeProfitOrder = TakeProfitOrder(**trade.takeProfitOrder)

            if trade.stopLossOrder:
                stopLossOrder = StopLossOrder(**trade.stopLossOrder)

            # Convert OANDA instrument format (EUR_USD) to our format (EUR/USD)
            formatted_instrument = trade.instrument.replace('_', '/')
            self.logger.log_info(f"Converting instrument format from {trade.instrument} to {formatted_instrument}")

            result.append(TradeHistoryRow(
                tradeID=trade.id,
                type=TradeType.LONG if float(trade.currentUnits) > 0 else TradeType.SHORT,
                status=TradeStatus.OPEN,
                instrument=formatted_instrument,  # Use the formatted instrument
                units=float(trade.currentUnits),
                price=float(trade.price),
                openTime=datetime.fromisoformat(trade.openTime.replace('Z', '+00:00')),
                initialMarginRequired=float(trade.initialMarginRequired),
                unrealizedPL=float(trade.unrealizedPL),
                realizedPL=float(trade.realizedPL),
                takeProfitPrice=float(takeProfitOrder.price) if takeProfitOrder else None,
                stopLossPrice=float(stopLossOrder.price) if stopLossOrder else None
            ))
        return result

    def execute_trade(self, instrument: str, units: float, type: TradeType,
                     price: float, stop_loss: Optional[float] = None, take_profit: Optional[float] = None) -> Optional[TradeExecutionResponse]:
        """
        Execute a trade with OANDA.

        Args:
            instrument (str): Trading instrument (e.g., "EUR_USD")
            units (float): Number of units to trade
            type (TradeType): Trade type (LONG, SHORT, or CLOSE)
            price (float): Current market price
            stop_loss (Optional[float], optional): Stop loss price. Not required for CLOSE trades. Defaults to None.
            take_profit (Optional[float], optional): Take profit price. Not required for CLOSE trades. Defaults to None.

        Returns:
            Optional[TradeExecutionResponse]: Trade execution response or None if failed
        """
        try:
            # Format units for all instruments - multiply by 1000 and round to whole numbers
            units = round(units * 1000)  # Convert to thousands and round to whole numbers

            # Define precision mapping for different instruments
            # Default to 3 decimal places for most instruments
            precision_mapping = {
                "USD_JPY": 3,  # JPY pairs typically use 3 decimal places
                "EUR_JPY": 3,
                "GBP_JPY": 3,
                "AUD_JPY": 3,
                "NZD_JPY": 3,
                "CAD_JPY": 3,
                "CHF_JPY": 3,
                "EUR_USD": 5,  # Major pairs typically use 5 decimal places
                "GBP_USD": 5,
                "AUD_USD": 5,
                "NZD_USD": 5,
                "USD_CAD": 5,
                "USD_CHF": 5,
                "EUR_GBP": 5,
                "EUR_AUD": 5,
                "EUR_CAD": 5,
                "EUR_CHF": 5,
                "GBP_AUD": 5,
                "GBP_CAD": 5,
                "GBP_CHF": 5,
            }

            # Only calculate stop loss and take profit distances for LONG and SHORT trades
            stop_loss_distance = None
            take_profit_distance = None
            stop_loss_distance_rounded = None
            take_profit_distance_rounded = None

            if type != TradeType.CLOSE and stop_loss is not None and take_profit is not None:
                # Get precision for the instrument, default to 3 if not in mapping
                precision = precision_mapping.get(instrument, 3)

                self.logger.log_info(f"Using precision {precision} for instrument {instrument}")

                # Calculate stop loss and take profit distances
                stop_loss_distance = abs(price - stop_loss)
                take_profit_distance = abs(take_profit - price)

                # Round to appropriate precision
                stop_loss_distance_rounded = round(stop_loss_distance, precision)
                take_profit_distance_rounded = round(take_profit_distance, precision)

            self.logger.log_info(f"Original distances - SL: {stop_loss_distance}, TP: {take_profit_distance}")
            self.logger.log_info(f"Rounded distances - SL: {stop_loss_distance_rounded}, TP: {take_profit_distance_rounded}")

            # Handle CLOSE trade type differently
            if type == TradeType.CLOSE:
                # For CLOSE trades, we use the close_trade method
                # First, get the open trades to find the trade ID
                open_trades = self.get_open_trades()

                # Format the instrument for comparison (OANDA uses EUR_USD format)
                formatted_instrument = instrument.replace('/', '_')
                self.logger.log_info(f"Looking for open trade with instrument {formatted_instrument} (original: {instrument})")

                # Try to find the trade with either format of the instrument
                trade_to_close = next((trade for trade in open_trades
                                     if trade.instrument == formatted_instrument or trade.instrument == instrument), None)

                if not trade_to_close:
                    self.logger.log_warning(f"No open trade found for {instrument} to close")
                    # Log all open trades to help diagnose the issue
                    self.logger.log_info(f"Available open trades: {[{t.tradeID: t.instrument} for t in open_trades]}")
                    return None

                self.logger.log_info(f"Closing trade {trade_to_close.tradeID} for {instrument} (formatted as {formatted_instrument})")
                response_data = self.close_trade(trade_to_close.tradeID)

                # Create a simple response for CLOSE trades
                if response_data:
                    try:
                        # Get the realized PL from the response
                        realized_pl = 0
                        if 'realizedPL' in response_data:
                            realized_pl = float(response_data['realizedPL'])
                        elif 'orderFillTransaction' in response_data and 'pl' in response_data['orderFillTransaction']:
                            realized_pl = float(response_data['orderFillTransaction']['pl'])

                        self.logger.log_info(f"Creating TradeExecutionResponse with realized PL: {realized_pl}")

                        # Create the response with the correct parameter names
                        return TradeExecutionResponse(
                            tradeID=trade_to_close.tradeID,
                            instrument=instrument,
                            units=units,
                            price=price,
                            type=type,
                            time=datetime.now(timezone.utc),
                            initialMarginRequired=0,  # Not relevant for closing trades
                            halfSpreadCost=0,  # Not available in the response
                            commission=0,  # Not available in the response
                            accountBalance=0,  # Not relevant for closing trades
                            pl=realized_pl
                        )
                    except Exception as e:
                        self.logger.log_error(f"Error creating TradeExecutionResponse: {e}")
                        return None
                return None

            # For LONG and SHORT trades, prepare the order data
            # Check if stop_loss and take_profit are provided
            if stop_loss is None or take_profit is None:
                self.logger.log_error("Stop loss and take profit are required for LONG and SHORT trades")
                return None

            # Prepare order data
            oanda_execute_trade_request = OandaExecuteTradeRequest(
                order=MarketOrderRequest(
                    type="MARKET",
                    instrument=instrument,
                    units=str(-units) if type == TradeType.SHORT else str(units),
                    timeInForce="FOK",
                    positionFill="DEFAULT",
                    stopLossOnFill=StopLossDetails(distance=str(stop_loss_distance_rounded)),
                    takeProfitOnFill=TakeProfitDetails(distance=str(take_profit_distance_rounded))
                )
            )

            # Convert to dictionary for the request
            order_data = oanda_execute_trade_request.to_dict()

            self.logger.log_info(f"Executing {type} order for {instrument} with units {units}")
            self.logger.log_info(f"Stop loss distance: {stop_loss_distance}, Take profit distance: {take_profit_distance}")

            # Execute order using the _make_request method which already has retry logic
            response_data = self._make_request("POST", f"/accounts/{self.account_id}/orders", order_data)
            self.logger.log_info(f"OANDA response: {response_data}")

            # The _make_request method will raise an exception if the status code is not 2xx
            # So if we get here, the request was successful
            # Parse the response into our OANDA response class
            oanda_response = OandaExecuteTradeResponseSuccess.from_dict(response_data)

            # Check if the order was filled
            if oanda_response.orderFillTransaction:
                self.logger.log_info(f"Successfully executed {type} order for {instrument}")
                if oanda_response.orderFillTransaction.tradeOpened is not None:
                    trade_execution_response = self.convert_oanda_execute_trade_response_to_trade_execution_response(type, oanda_response)
                    return trade_execution_response
                else:
                    self.logger.log_error(f"Order was executed but is not a new order. Response: {oanda_response}")
                    return None
            else:
                # FOK (Fill or Kill) orders can be created but not filled if:
                # 1. There is insufficient liquidity to fill the entire order
                # 2. The price moved outside the priceBound (if specified)
                # 3. The order would have violated account position limits
                if oanda_response.orderCancelTransaction:
                    cancel_transaction = oanda_response.orderCancelTransaction
                    self.logger.log_info(f"Order cancel transaction: {cancel_transaction}")
                    reason = cancel_transaction.reason
                    if "FIFO_VIOLATION" in reason:
                        self.logger.log_info(f"FIFO violation: {reason}")
                        self.firebase_client.append_user_log(
                            "⚠️ Trade rejected: FIFO (First In, First Out) violation. "
                            "Forex trading rules require closing existing positions before opening new ones in the same instrument. "
                            "The bot will wait for the existing position to be closed before executing the new trade."
                        )
                        return None
                    elif "INSUFFICIENT_MARGIN" in reason:
                        self.logger.log_info(f"Insufficient margin: {reason}")
                        self.firebase_client.append_user_log(
                            "⚠️ Trade rejected: Insufficient margin in your account. "
                            "The bot will be stopped to prevent further issues. "
                            "Please edit your strategy to reduce the risk percentage or lot size, "
                            "or add more funds to your account before restarting the bot."
                        )
                        # Stop the bot due to insufficient margin
                        # This will raise SystemExit which will propagate up
                        self._handle_insufficient_margin()
                        # We should never reach here, but just in case
                        return None
                    else:
                        self.logger.log_info(f"Order was created but not filled. Reason: {reason}")
                        return None
                else:
                    self.logger.log_error(f"Order was created but not filled (likely due to insufficient liquidity or price movement). Response: {oanda_response}")
                    return None

            # We should never get here because _make_request will raise an exception for non-2xx status codes
            # But keeping this as a fallback
            return None

        except Exception as e:
            self.logger.log_error(f"Error executing trade: {e}")
            return None

    def _handle_insufficient_margin(self):
        """
        Handle insufficient margin error by stopping the bot.
        This creates a stop flag file that will be detected by the main loop,
        causing the bot to exit gracefully.
        """
        try:
            # Create a stop flag file to prevent the bot from continuing
            # Use the same path format as in main.py
            stop_flag_file = f"/tmp/trade_bot_stopped_{self.account_id}"
            with open(stop_flag_file, 'w') as f:
                f.write(f"Stopped due to insufficient margin at {datetime.now(timezone.utc).isoformat()}")
            self.logger.log_info(f"Created stop flag file due to insufficient margin: {stop_flag_file}")

            # Update bot status in Firebase
            self.firebase_client.update_bot_status(
                FirebaseClient.BotStatus.ERROR,
                {"message": "Trading bot stopped due to insufficient margin. Please reduce risk or add funds."}
            )

            # Set the should_exit flag in the main bot if possible
            # This is a more direct way to stop the bot
            self.logger.log_info("Setting should_exit flag to True to stop the bot immediately")
            # Force the bot to exit by raising a SystemExit exception
            # This will be caught by the main loop's exception handler
            raise SystemExit("Insufficient margin detected, stopping bot")
        except SystemExit:
            # Re-raise SystemExit to propagate it up
            raise
        except Exception as e:
            self.logger.log_error(f"Error handling insufficient margin: {e}")

    def convert_oanda_execute_trade_response_to_trade_execution_response(self, type: TradeType, oanda_response: OandaExecuteTradeResponseSuccess) -> Optional[TradeExecutionResponse]:
        """
        Convert OANDA response to TradeExecutionResponse.
        This function is only for when orders are filled and a new trade is opened.

        Args:
            response (OandaExecuteTradeResponseSuccess): OANDA response
        """
        try:
            if not oanda_response.orderFillTransaction or not oanda_response.orderFillTransaction.tradeOpened:
                raise ValueError("No trade was opened in the OANDA response")

            order_fill_transaction = oanda_response.orderFillTransaction
            trade_opened = TradeOpen(**order_fill_transaction.tradeOpened)

            return TradeExecutionResponse(
                type=type,
                tradeID=trade_opened.tradeID,
                time=order_fill_transaction.time,
                units=trade_opened.units,
                price=trade_opened.price,
                initialMarginRequired=trade_opened.initialMarginRequired,
                instrument=order_fill_transaction.instrument,
                halfSpreadCost=order_fill_transaction.halfSpreadCost,
                commission=order_fill_transaction.commission,
                accountBalance=order_fill_transaction.accountBalance,
                pl=order_fill_transaction.quotePL
            )
        except Exception as e:
            self.logger.log_error(f"Error converting OANDA response to TradeExecutionResponse: {e}")
            return None

    def close_position(self, instrument: str) -> Optional[Dict]:
        """Close a position using OANDA's position close endpoint."""
        try:
            self.logger.log_info(f"Closing position for {instrument}")

            # Convert instrument format from EUR/USD to EUR_USD
            formatted_instrument = instrument.replace("/", "_")

            # First check if position exists and get its type
            positions = self.get_open_positions()
            position = next((p for p in positions if p["instrument"] == instrument), None)

            if not position:
                self.logger.log_info(f"No open position found for {instrument}")
                return None

            # Prepare request body based on position type
            request_body = {}
            if position["type"] == "buy":
                request_body["longUnits"] = "ALL"
            else:  # position["type"] == "sell"
                request_body["shortUnits"] = "ALL"

            # Make request to close position using the _make_request method which already has retry logic
            response_data = self._make_request("PUT", f"/accounts/{self.account_id}/positions/{formatted_instrument}/close", request_body)
            self.logger.log_info(f"Successfully closed {position['type']} position for {instrument}")

            # Extract trade data from response
            fill_transaction = response_data.get("longOrderFillTransaction", {}) or response_data.get("shortOrderFillTransaction", {})
            if not fill_transaction:
                self.logger.log_info("No fill transaction found in response")
                return None

            # Format trade data
            trade_data = {
                "orderCreateTransaction": fill_transaction,
                "orderFillTransaction": fill_transaction,
                "orderCancelTransaction": None
            }

            return trade_data

        except Exception as e:
            self.logger.log_error(e, "Error closing position")
            raise

    @with_retry(max_retries=5, base_delay=1.0, max_delay=10.0, use_circuit_breaker=True)
    def close_trade(self, trade_id: str) -> Optional[Dict]:
        """Close a trade using OANDA's trade close endpoint with enhanced retry logic.

        Args:
            trade_id (str): The ID of the trade to close

        Returns:
            Optional[Dict]: Information about the closed trade or None if closing failed
        """
        try:
            self.logger.log_info(f"Closing trade {trade_id}")

            # Make request to close trade using the _make_request method which already has retry logic
            response_data = self._make_request("PUT", f"/accounts/{self.account_id}/trades/{trade_id}/close")

            self.logger.log_info(f"OANDA close trade response: {response_data}")

            oanda_response = OandaExecuteTradeResponseSuccess.from_dict(response_data)

            # Check if the order was filled
            if oanda_response.orderFillTransaction:
                self.logger.log_info(f"Successfully closed trade {trade_id}")
                if oanda_response.orderFillTransaction.tradesClosed is not None:
                    if len(oanda_response.orderFillTransaction.tradesClosed) > 1:
                        # This should ideally not happen since forex limits us to only one open trade per instrument
                        # and for now each strategy is limited to one instrument.
                        # But we'll handle it anyway just in case.
                        self.logger.log_warning(f"More than one trade was closed in the OANDA response. Response: {oanda_response}")
                        # Continue anyway and use the first trade
                        trade_closed = TradeReduce(**oanda_response.orderFillTransaction.tradesClosed[0])
                        if trade_closed.tradeID == trade_id:
                            self.logger.log_info(f"Successfully closed trade {trade_id}")
                            return {
                                "tradeID": trade_closed.tradeID,
                                "status": TradeStatus.CLOSED,
                                "units": abs(float(trade_closed.units)),
                                "realizedPL": float(trade_closed.realizedPL),
                                "halfSpreadCost": float(trade_closed.halfSpreadCost),
                                "closeTime": datetime.fromisoformat(oanda_response.orderFillTransaction.time.replace('Z', '+00:00'))
                            }
                        else:
                            # Try to find our trade in the list
                            for trade_data in oanda_response.orderFillTransaction.tradesClosed:
                                trade_closed = TradeReduce(**trade_data)
                                if trade_closed.tradeID == trade_id:
                                    self.logger.log_info(f"Found and closed trade {trade_id}")
                                    return {
                                        "tradeID": trade_closed.tradeID,
                                        "status": TradeStatus.CLOSED,
                                        "units": abs(float(trade_closed.units)),
                                        "realizedPL": float(trade_closed.realizedPL),
                                        "halfSpreadCost": float(trade_closed.halfSpreadCost),
                                        "closeTime": datetime.fromisoformat(oanda_response.orderFillTransaction.time.replace('Z', '+00:00'))
                                    }

                            # If we get here, we couldn't find our trade
                            self.logger.log_error(f"Trade {trade_id} was not found in closed trades. Response: {oanda_response}")
                            # Raise a RetryableError to trigger retry
                            raise RetryableError(f"Trade {trade_id} not found in closed trades")
                    else:
                        trade_closed = TradeReduce(**oanda_response.orderFillTransaction.tradesClosed[0])
                        if trade_closed.tradeID == trade_id:
                            self.logger.log_info(f"Successfully closed trade {trade_id}")
                            return {
                                "tradeID": trade_closed.tradeID,
                                "status": TradeStatus.CLOSED,
                                "units": abs(float(trade_closed.units)),
                                "realizedPL": float(trade_closed.realizedPL),
                                "halfSpreadCost": float(trade_closed.halfSpreadCost),
                                "closeTime": datetime.fromisoformat(oanda_response.orderFillTransaction.time.replace('Z', '+00:00'))
                            }
                        else:
                            self.logger.log_error(f"Trade {trade_id} was not closed. Instead a different trade was closed. Response: {oanda_response}")
                            # Raise a RetryableError to trigger retry
                            raise RetryableError(f"Wrong trade closed: expected {trade_id}, got {trade_closed.tradeID}")
                else:
                    self.logger.log_error(f"No trades closed in the OANDA response. Response: {oanda_response}")
                    # Raise a RetryableError to trigger retry
                    raise RetryableError("No trades closed in response")
            else:
                self.logger.log_error(f"Trade {trade_id} was not filled. Response: {oanda_response}")
                # Raise a RetryableError to trigger retry
                raise RetryableError("Trade close order not filled")

        except RetryableError:
            # Let the retry decorator handle these
            raise
        except Exception as e:
            self.logger.log_error(e, f"Error closing trade {trade_id}")
            # For other exceptions, we'll also retry
            raise RetryableError(f"Error closing trade: {str(e)}") from e
