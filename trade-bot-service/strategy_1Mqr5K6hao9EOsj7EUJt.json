{"name": "testStrat with RSI having SMA as a source", "description": "", "instruments": "EUR/USD", "timeframe": "1m", "tradingSession": ["All"], "indicators": [{"id": "17463095231729iwecbspi98", "indicator_class": "SMA", "type": "SMA", "parameters": {"period": 50}, "source": "price"}, {"id": "1746315273261rjyw039rnod", "indicator_class": "RSI", "type": "RSI", "parameters": {"period": 14}, "source": "17463095231729iwecbspi98"}], "entryRules": [{"id": "1746315287011oabowairsvf", "tradeType": "long", "indicator1": "1746315273261rjyw039rnod", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "1", "barRef": "close"}], "exitRules": [{"id": "1746315298585mzpjucx534k", "tradeType": "long", "indicator1": "1746315273261rjyw039rnod", "operator": ">", "compareType": "value", "indicator2": "", "value": "90", "barRef": "close"}], "riskManagement": {"stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage", "riskPerTrade": "1%", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%"}, "user_id": "hUli47EgkKnHdyryQhzTyvn7ehxp", "id": "1Mqr5K6hao9EOsj7EUJt"}