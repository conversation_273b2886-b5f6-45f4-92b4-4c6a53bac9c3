#!/usr/bin/env python3
"""
Test script for the Enhanced Market Data Provider.

This script tests the GCS + Polygon hybrid approach to ensure it works correctly.
"""

import os
import sys
import time
from datetime import datetime, timezone

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data.hybrid_market_data import HybridMarketDataWrapper
from utils.logger import Logger

def test_enhanced_market_data():
    """Test the enhanced market data provider."""
    logger = Logger("TestEnhancedMarketData")
    
    # Check for required environment variables
    api_key = os.getenv('POLYGON_API_KEY')
    if not api_key:
        logger.log_error("POLYGON_API_KEY environment variable not set")
        return False
    
    try:
        logger.log_info("🚀 Testing Enhanced Market Data Provider")
        
        # Initialize the hybrid wrapper
        logger.log_info("Initializing hybrid market data wrapper...")
        wrapper = HybridMarketDataWrapper(api_key)
        
        # Test different timeframes
        test_cases = [
            {"symbol": "EUR/USD", "timespan": "5m", "count": 100},
            {"symbol": "EUR/USD", "timespan": "1h", "count": 50},
            {"symbol": "GBP/USD", "timespan": "15m", "count": 200}
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            logger.log_info(f"\n📊 Test Case {i}: {test_case}")
            
            start_time = time.time()
            result = wrapper.get_candles(
                symbol=test_case["symbol"],
                timespan=test_case["timespan"],
                count=test_case["count"]
            )
            execution_time = time.time() - start_time
            
            if result["status"] == "success":
                candles = result["candles"]
                source = result.get("source", "unknown")
                
                logger.log_info(f"✅ Success: {len(candles)} candles from {source}")
                logger.log_info(f"⏱️ Execution time: {execution_time:.2f}s")
                
                # Show sample data
                if candles:
                    first_candle = candles[0]
                    last_candle = candles[-1]
                    
                    first_time = datetime.fromtimestamp(first_candle["time"], tz=timezone.utc)
                    last_time = datetime.fromtimestamp(last_candle["time"], tz=timezone.utc)
                    
                    logger.log_info(f"📅 Date range: {first_time} to {last_time}")
                    logger.log_info(f"💰 Price range: {first_candle['close']:.5f} to {last_candle['close']:.5f}")
            else:
                logger.log_error(f"❌ Failed: {result.get('message', 'Unknown error')}")
                return False
        
        # Test performance metrics
        logger.log_info("\n📈 Performance Metrics:")
        metrics = wrapper.get_performance_metrics()
        logger.log_info(f"Metrics: {metrics}")
        
        # Test market status
        logger.log_info("\n🏪 Testing market status...")
        market_status = wrapper.check_market_status()
        logger.log_info(f"Market is open: {market_status.get('is_open', 'unknown')}")
        
        # Test quote
        logger.log_info("\n💱 Testing quote...")
        quote = wrapper.get_quote("EUR/USD")
        if "bid" in quote and "ask" in quote:
            logger.log_info(f"EUR/USD Quote - Bid: {quote['bid']:.5f}, Ask: {quote['ask']:.5f}, Spread: {quote['spread']:.5f}")
        
        # Cleanup
        wrapper.cleanup()
        logger.log_info("\n✅ All tests completed successfully!")
        return True
        
    except Exception as e:
        logger.log_error(f"❌ Test failed with error: {str(e)}")
        return False

def main():
    """Main function."""
    print("Enhanced Market Data Provider Test")
    print("=" * 50)
    
    # Load environment variables from .env file if it exists
    try:
        from dotenv import load_dotenv
        if os.path.exists('.env'):
            load_dotenv()
            print("✅ Loaded environment variables from .env file")
    except ImportError:
        print("⚠️ python-dotenv not installed, skipping .env file loading")
    
    # Run the test
    success = test_enhanced_market_data()
    
    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
