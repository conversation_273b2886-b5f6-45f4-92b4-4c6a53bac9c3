"""
Monitoring module for the strategy controller.
Handles collection and storage of health metrics from trade bots.
"""

import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from pydantic import BaseModel

import firebase_admin
from firebase_admin import firestore

# Initialize logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("monitoring")

# Models for monitoring data
class DependencyStatus(BaseModel):
    status: str
    message: str

class HealthData(BaseModel):
    status: str
    version: str
    timestamp: str
    uptime: float
    service: str
    pod_name: str
    pod_ip: str
    strategy_id: Optional[str] = None
    user_id: Optional[str] = None
    dependencies: Dict[str, DependencyStatus] = {}
    error: Optional[str] = None

class AlertData(BaseModel):
    service: str
    status: str
    timestamp: str
    message: str
    strategy_id: Optional[str] = None
    user_id: Optional[str] = None
    resolved: bool = False
    resolved_at: Optional[str] = None

class MonitoringService:
    """Service for collecting and managing monitoring data."""
    
    def __init__(self, db: firestore.Client):
        """Initialize the monitoring service."""
        self.db = db
        self.health_data_cache = {}
        self.alerts = []
        
    def update_health_data(self, health_data: HealthData) -> Dict[str, Any]:
        """
        Update health data for a service.
        
        Args:
            health_data: Health data from the service
            
        Returns:
            Dict with status and message
        """
        try:
            # Create a unique ID for this service instance
            service_id = f"{health_data.service}-{health_data.pod_name}"
            if health_data.strategy_id:
                service_id = f"{service_id}-{health_data.strategy_id}"
            
            # Update cache
            self.health_data_cache[service_id] = health_data.dict()
            
            # Store in Firebase
            health_doc_ref = self.db.collection("monitoring").document("health")
            
            # Get current health data
            health_doc = health_doc_ref.get()
            if health_doc.exists:
                current_data = health_doc.to_dict()
            else:
                current_data = {"services": {}, "last_updated": datetime.now(timezone.utc).isoformat()}
            
            # Update services data
            if "services" not in current_data:
                current_data["services"] = {}
            
            current_data["services"][service_id] = health_data.dict()
            current_data["last_updated"] = datetime.now(timezone.utc).isoformat()
            
            # Calculate overall system status
            system_status = "healthy"
            for service_data in current_data["services"].values():
                if service_data.get("status") != "healthy":
                    system_status = "unhealthy"
                    break
            
            current_data["status"] = system_status
            
            # Save to Firebase
            health_doc_ref.set(current_data)
            
            # Check for alerts
            self._check_for_alerts(health_data)
            
            return {"status": "success", "message": "Health data updated"}
        except Exception as e:
            logger.error(f"Error updating health data: {str(e)}")
            return {"status": "error", "message": f"Error updating health data: {str(e)}"}
    
    def create_alert(self, alert_data: AlertData) -> Dict[str, Any]:
        """
        Create a new alert.
        
        Args:
            alert_data: Alert data
            
        Returns:
            Dict with status and message
        """
        try:
            # Add to alerts list
            self.alerts.append(alert_data.dict())
            
            # Store in Firebase
            alert_ref = self.db.collection("monitoring").document("alerts").collection("active").document()
            alert_ref.set(alert_data.dict())
            
            return {"status": "success", "message": "Alert created", "alert_id": alert_ref.id}
        except Exception as e:
            logger.error(f"Error creating alert: {str(e)}")
            return {"status": "error", "message": f"Error creating alert: {str(e)}"}
    
    def resolve_alert(self, alert_id: str) -> Dict[str, Any]:
        """
        Resolve an alert.
        
        Args:
            alert_id: ID of the alert to resolve
            
        Returns:
            Dict with status and message
        """
        try:
            # Get the alert
            alert_ref = self.db.collection("monitoring").document("alerts").collection("active").document(alert_id)
            alert_doc = alert_ref.get()
            
            if not alert_doc.exists:
                return {"status": "error", "message": f"Alert {alert_id} not found"}
            
            # Update alert
            alert_data = alert_doc.to_dict()
            alert_data["resolved"] = True
            alert_data["resolved_at"] = datetime.now(timezone.utc).isoformat()
            
            # Move to resolved collection
            resolved_ref = self.db.collection("monitoring").document("alerts").collection("resolved").document(alert_id)
            resolved_ref.set(alert_data)
            
            # Delete from active collection
            alert_ref.delete()
            
            # Update in-memory alerts
            for i, alert in enumerate(self.alerts):
                if alert.get("id") == alert_id:
                    self.alerts[i]["resolved"] = True
                    self.alerts[i]["resolved_at"] = alert_data["resolved_at"]
                    break
            
            return {"status": "success", "message": f"Alert {alert_id} resolved"}
        except Exception as e:
            logger.error(f"Error resolving alert: {str(e)}")
            return {"status": "error", "message": f"Error resolving alert: {str(e)}"}
    
    def get_health_data(self) -> Dict[str, Any]:
        """
        Get all health data.
        
        Returns:
            Dict with system status and services data
        """
        try:
            # Get from Firebase
            health_doc = self.db.collection("monitoring").document("health").get()
            
            if health_doc.exists:
                return health_doc.to_dict()
            else:
                return {
                    "status": "unknown",
                    "last_updated": datetime.now(timezone.utc).isoformat(),
                    "services": {}
                }
        except Exception as e:
            logger.error(f"Error getting health data: {str(e)}")
            return {
                "status": "error",
                "message": f"Error getting health data: {str(e)}",
                "services": self.health_data_cache
            }
    
    def get_alerts(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """
        Get all alerts.
        
        Args:
            active_only: Whether to get only active alerts
            
        Returns:
            List of alerts
        """
        try:
            alerts = []
            
            # Get from Firebase
            if active_only:
                alert_refs = self.db.collection("monitoring").document("alerts").collection("active").stream()
            else:
                # Get both active and resolved alerts
                active_refs = self.db.collection("monitoring").document("alerts").collection("active").stream()
                resolved_refs = self.db.collection("monitoring").document("alerts").collection("resolved").stream()
                
                alert_refs = list(active_refs) + list(resolved_refs)
            
            for alert_doc in alert_refs:
                alert_data = alert_doc.to_dict()
                alert_data["id"] = alert_doc.id
                alerts.append(alert_data)
            
            return alerts
        except Exception as e:
            logger.error(f"Error getting alerts: {str(e)}")
            return self.alerts
    
    def _check_for_alerts(self, health_data: HealthData):
        """
        Check health data for potential alerts.
        
        Args:
            health_data: Health data to check
        """
        # Check overall status
        if health_data.status != "healthy":
            # Create an alert
            alert_data = AlertData(
                service=health_data.service,
                status=health_data.status,
                timestamp=datetime.now(timezone.utc).isoformat(),
                message=f"Service {health_data.service} is {health_data.status}",
                strategy_id=health_data.strategy_id,
                user_id=health_data.user_id
            )
            
            self.create_alert(alert_data)
        
        # Check dependencies
        for dep_name, dep_status in health_data.dependencies.items():
            if dep_status.status != "healthy":
                # Create an alert
                alert_data = AlertData(
                    service=f"{health_data.service}-{dep_name}",
                    status="unhealthy",
                    timestamp=datetime.now(timezone.utc).isoformat(),
                    message=f"Dependency {dep_name} for service {health_data.service} is {dep_status.status}: {dep_status.message}",
                    strategy_id=health_data.strategy_id,
                    user_id=health_data.user_id
                )
                
                self.create_alert(alert_data)
    
    def cleanup_old_data(self):
        """Clean up old health data and resolved alerts."""
        try:
            # Clean up old health data
            health_doc = self.db.collection("monitoring").document("health").get()
            if health_doc.exists:
                health_data = health_doc.to_dict()
                services = health_data.get("services", {})
                
                # Remove services that haven't reported in the last hour
                current_time = datetime.now(timezone.utc)
                services_to_remove = []
                
                for service_id, service_data in services.items():
                    last_updated = datetime.fromisoformat(service_data.get("timestamp", current_time.isoformat()))
                    if (current_time - last_updated) > timedelta(hours=1):
                        services_to_remove.append(service_id)
                
                for service_id in services_to_remove:
                    del services[service_id]
                
                # Update Firebase
                health_data["services"] = services
                health_data["last_updated"] = current_time.isoformat()
                
                self.db.collection("monitoring").document("health").set(health_data)
            
            # Clean up old resolved alerts (older than 7 days)
            week_ago = current_time - timedelta(days=7)
            old_alerts = self.db.collection("monitoring").document("alerts").collection("resolved").where(
                "timestamp", "<", week_ago.isoformat()
            ).stream()
            
            for alert_doc in old_alerts:
                alert_doc.reference.delete()
        except Exception as e:
            logger.error(f"Error cleaning up old data: {str(e)}")

# Initialize monitoring service
monitoring_service = None

def init_monitoring(db: firestore.Client):
    """Initialize the monitoring service."""
    global monitoring_service
    monitoring_service = MonitoringService(db)
    
    # Start background cleanup task
    import threading
    
    def cleanup_task():
        while True:
            try:
                monitoring_service.cleanup_old_data()
            except Exception as e:
                logger.error(f"Error in cleanup task: {str(e)}")
            
            # Run every hour
            time.sleep(3600)
    
    cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
    cleanup_thread.start()
    
    return monitoring_service
