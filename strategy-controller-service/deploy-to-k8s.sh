#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Configuration
PROJECT_ID="oryntrade"
SERVICE_NAME="strategy-controller"
REPOSITORY="oryn-containers"
IMAGE_NAME="us-central1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${SERVICE_NAME}:latest"

echo "🚀 Starting deployment process for ${SERVICE_NAME}"

# Build the Docker image for linux/amd64 platform
echo "🏗️ Building Docker image for linux/amd64 platform..."
docker buildx build --platform linux/amd64 \
    -t ${IMAGE_NAME} \
    --push \
    .

# Apply Kubernetes manifests
echo "🔄 Applying Kubernetes manifests..."
kubectl apply -f deployment.yaml

# Force a rollout to use the new image
echo "🔄 Rolling out new deployment..."
kubectl rollout restart deployment/${SERVICE_NAME}

# Wait for rollout to complete
echo "⏳ Waiting for rollout to complete..."
kubectl rollout status deployment/${SERVICE_NAME}

echo "✅ Strategy Controller has been deployed successfully"
echo "📊 To access the service, run: kubectl port-forward svc/strategy-controller-service 8080:80"
echo "🔒 Remember to update the Firebase credentials secret with your actual Firebase key."
echo "👉 Usage guide:"
echo "   - Health check: GET /"
echo "   - List strategies: GET /list-strategies/{user_id} (requires authentication)"
echo "   - Start strategy: POST /start-strategy (requires authentication)"
echo "   - Stop strategy: POST /stop-strategy (requires authentication)"
echo "   - Get strategy status: GET /strategy-status/{user_id}/{strategy_id} (requires authentication)" 