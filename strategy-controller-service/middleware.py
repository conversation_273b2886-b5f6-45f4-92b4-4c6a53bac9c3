from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import time
from datetime import datetime
from logging_config import log_request, structured_logger

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log all incoming requests and their timing."""
    
    async def dispatch(self, request: Request, call_next):
        # Record request start time
        start_time = time.time()
        
        # Add a request ID if not present
        if "X-Request-ID" not in request.headers:
            request_id = f"{datetime.utcnow().timestamp()}-{hash(request)}"
            request.scope["headers"].append((b"x-request-id", request_id.encode()))
        
        # Initial log of the incoming request
        structured_logger.info(
            "Request received",
            endpoint=request.url.path,
            method=request.method,
            client_ip=request.client.host
        )
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Calculate request duration
            duration_ms = (time.time() - start_time) * 1000
            
            # Log the completed request
            log_request(
                request=request,
                response_status=response.status_code,
                duration_ms=duration_ms
            )
            
            return response
        except Exception as e:
            # Log the exception
            duration_ms = (time.time() - start_time) * 1000
            structured_logger.error(
                "Request failed with exception",
                endpoint=request.url.path,
                method=request.method,
                client_ip=request.client.host,
                duration_ms=duration_ms,
                exception=str(e),
                exc_info=True
            )
            raise 