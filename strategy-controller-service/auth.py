import firebase_admin
from firebase_admin import auth, credentials
from fastapi import Request, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging
import os

# Bearer token authentication scheme
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    Validate Firebase ID token and return the user ID.
    This function is used as a FastAPI dependency.
    """
    # Check if we're in development mode
    if os.getenv("ENVIRONMENT", "").lower() == "development":
        try:
            # In development, try normal auth first
            # Get the token from the authorization header
            token = credentials.credentials
            
            # For development testing, validate token if it looks ok, otherwise use default test user
            if token and token.startswith("Bearer "):
                try:
                    decoded_token = auth.verify_id_token(token.replace("Bearer ", ""))
                    user_id = decoded_token.get("uid")
                    logging.info(f"Auth successful in development for user: {user_id}")
                    return user_id
                except Exception as auth_err:
                    logging.warning(f"Auth failed in development mode, using default test user: {auth_err}")
            
            # If we reach here in development mode, use a default test user ID
            logging.info("Using default test user in development mode")
            return "test-user-development"
        except Exception as e:
            logging.warning(f"Development auth fallback: {e}")
            return "test-user-development"
    
    # Production authentication
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # Get the token from the authorization header
        token = credentials.credentials
        
        # Verify Firebase token
        decoded_token = auth.verify_id_token(token)
        
        # Extract user ID
        user_id = decoded_token.get("uid")
        
        if not user_id:
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user_id
    
    except Exception as e:
        logging.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=401,
            detail=f"Could not validate credentials: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

# Optional: Helper to bypass authentication during development or for certain endpoints
async def get_optional_user(request: Request, credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    Try to validate Firebase ID token, but don't fail if not present.
    This can be used for endpoints that can work with or without authentication.
    """
    if not credentials:
        return None
    
    try:
        token = credentials.credentials
        decoded_token = auth.verify_id_token(token)
        user_id = decoded_token.get("uid")
        return user_id
    except Exception as e:
        logging.warning(f"Optional authentication failed: {e}")
        return None 