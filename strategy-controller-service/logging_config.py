import os
import logging
import json
from datetime import datetime
import traceback

# Configure basic logging
def setup_logging(log_level=logging.INFO):
    """
    Set up enhanced logging with structured output for better monitoring.
    """
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Create logger
    logger = logging.getLogger("strategy-controller")
    logger.setLevel(log_level)
    
    # Return the configured logger
    return logger

# Create a logger instance
logger = setup_logging()

class StructuredLogger:
    """A wrapper for structured logging in JSON format."""
    
    def __init__(self, service_name="strategy-controller"):
        self.service_name = service_name
        self.logger = logging.getLogger(service_name)
    
    def _log(self, level, message, **kwargs):
        """Format log with additional metadata and contextual information."""
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "service": self.service_name,
            "message": message,
            "level": level,
        }
        
        # Add any additional context
        log_data.update(kwargs)
        
        # Log as JSON string for structured logging
        self.logger.log(
            getattr(logging, level),
            json.dumps(log_data)
        )
    
    def info(self, message, **kwargs):
        """Log info level message."""
        self._log("INFO", message, **kwargs)
    
    def warning(self, message, **kwargs):
        """Log warning level message."""
        self._log("WARNING", message, **kwargs)
    
    def error(self, message, exc_info=None, **kwargs):
        """Log error level message with optional exception info."""
        if exc_info:
            kwargs["exception"] = traceback.format_exc()
        self._log("ERROR", message, **kwargs)
    
    def critical(self, message, exc_info=None, **kwargs):
        """Log critical level message with optional exception info."""
        if exc_info:
            kwargs["exception"] = traceback.format_exc()
        self._log("CRITICAL", message, **kwargs)
    
    def debug(self, message, **kwargs):
        """Log debug level message."""
        self._log("DEBUG", message, **kwargs)

# Create a structured logger instance
structured_logger = StructuredLogger()

# Function to log API requests for monitoring
def log_request(request, response_status, user_id=None, duration_ms=None):
    """Log API request details for monitoring."""
    try:
        log_data = {
            "endpoint": request.url.path,
            "method": request.method,
            "status_code": response_status,
            "client_ip": request.client.host,
            "user_agent": request.headers.get("user-agent", "unknown"),
        }
        
        if user_id:
            log_data["user_id"] = user_id
            
        if duration_ms is not None:
            log_data["duration_ms"] = duration_ms
            
        # Add request ID if available
        request_id = request.headers.get("X-Request-ID")
        if request_id:
            log_data["request_id"] = request_id
            
        structured_logger.info("API request processed", **log_data)
    except Exception as e:
        # Don't let logging errors affect the main application
        logging.error(f"Error in request logging: {e}")

# Context manager for timing API requests
class TimedOperation:
    """Context manager for timing operations and logging their duration."""
    
    def __init__(self, operation_name, logger=None, **log_context):
        self.operation_name = operation_name
        self.logger = logger or structured_logger
        self.log_context = log_context
        self.start_time = None
        
    def __enter__(self):
        self.start_time = datetime.utcnow()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.utcnow()
        duration = (end_time - self.start_time).total_seconds() * 1000  # in milliseconds
        
        log_data = {
            "operation": self.operation_name,
            "duration_ms": duration,
            **self.log_context
        }
        
        if exc_type:
            log_data["exception"] = str(exc_val)
            log_data["exception_type"] = exc_type.__name__
            self.logger.error(f"Operation {self.operation_name} failed", **log_data)
        else:
            self.logger.info(f"Operation {self.operation_name} completed", **log_data) 