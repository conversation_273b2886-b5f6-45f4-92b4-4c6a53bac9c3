apiVersion: apps/v1
kind: Deployment
metadata:
  name: strategy-controller
  labels:
    app: strategy-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: strategy-controller
  template:
    metadata:
      labels:
        app: strategy-controller
    spec:
      serviceAccountName: strategy-controller-sa
      containers:
        - name: strategy-controller
          image: us-central1-docker.pkg.dev/oryntrade/oryn-containers/strategy-controller:latest
          ports:
            - containerPort: 8080
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
---
apiVersion: v1
kind: Service
metadata:
  name: strategy-controller-service
spec:
  selector:
    app: strategy-controller
  ports:
    - port: 80
      targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: strategy-controller-sa
  annotations:
    iam.gke.io/gcp-service-account: "<EMAIL>"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pod-manager
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch", "create", "update", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: strategy-controller-pod-manager
subjects:
  - kind: ServiceAccount
    name: strategy-controller-sa
roleRef:
  kind: Role
  name: pod-manager
  apiGroup: rbac.authorization.k8s.io
