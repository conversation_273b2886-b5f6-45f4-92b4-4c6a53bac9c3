
        <!DOCTYPE html>
        <html>
        <head>
            <title>Oryn Monitoring Dashboard</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                h1 { color: #333; }
                .card { background-color: white; border-radius: 5px; padding: 15px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .healthy { color: green; }
                .unhealthy { color: red; }
                .unknown { color: orange; }
            </style>
        </head>
        <body>
            <h1>Oryn Monitoring Dashboard</h1>
            <div class="card">
                <h2>System Status</h2>
                <div id="system-status">Loading...</div>
            </div>
            <div class="card">
                <h2>Services</h2>
                <div id="services">Loading...</div>
            </div>
            <div class="card">
                <h2>Alerts</h2>
                <div id="alerts">Loading...</div>
            </div>

            <script>
                // Fetch health data
                async function fetchHealthData() {
                    const response = await fetch('/monitoring/health');
                    const data = await response.json();
                    return data;
                }

                // Fetch alerts
                async function fetchAlerts() {
                    const response = await fetch('/monitoring/alerts');
                    const data = await response.json();
                    return data.alerts;
                }

                // Update dashboard
                async function updateDashboard() {
                    try {
                        const healthData = await fetchHealthData();
                        const alerts = await fetchAlerts();

                        // Update system status
                        const systemStatusEl = document.getElementById('system-status');
                        systemStatusEl.innerHTML = `
                            <p class="${healthData.status}">${healthData.status.toUpperCase()}</p>
                            <p>Last updated: ${new Date(healthData.last_updated).toLocaleString()}</p>
                        `;

                        // Update services
                        const servicesEl = document.getElementById('services');
                        let servicesHtml = '';

                        for (const [serviceId, serviceData] of Object.entries(healthData.services || {})) {
                            servicesHtml += `
                                <div class="service">
                                    <h3>${serviceData.service} (${serviceData.pod_name})</h3>
                                    <p class="${serviceData.status}">${serviceData.status.toUpperCase()}</p>
                                    <p>Version: ${serviceData.version}</p>
                                    <p>Uptime: ${formatUptime(serviceData.uptime)}</p>
                                    <p>Last updated: ${new Date(serviceData.timestamp).toLocaleString()}</p>
                                    ${serviceData.strategy_id ? `<p>Strategy ID: ${serviceData.strategy_id}</p>` : ''}
                                    ${serviceData.user_id ? `<p>User ID: ${serviceData.user_id}</p>` : ''}
                                    ${serviceData.error ? `<p class="unhealthy">Error: ${serviceData.error}</p>` : ''}

                                    <h4>Dependencies</h4>
                                    <ul>
                                        ${Object.entries(serviceData.dependencies || {}).map(([depName, depStatus]) => `
                                            <li>
                                                <span>${depName}: </span>
                                                <span class="${depStatus.status}">${depStatus.status.toUpperCase()}</span>
                                                ${depStatus.message ? `<span> - ${depStatus.message}</span>` : ''}
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                                <hr>
                            `;
                        }

                        servicesEl.innerHTML = servicesHtml || '<p>No services found</p>';

                        // Update alerts
                        const alertsEl = document.getElementById('alerts');
                        let alertsHtml = '';

                        for (const alert of alerts) {
                            alertsHtml += `
                                <div class="alert">
                                    <h3>${alert.service}</h3>
                                    <p class="${alert.status}">${alert.status.toUpperCase()}</p>
                                    <p>${alert.message}</p>
                                    <p>Created: ${new Date(alert.timestamp).toLocaleString()}</p>
                                    ${alert.strategy_id ? `<p>Strategy ID: ${alert.strategy_id}</p>` : ''}
                                    ${alert.user_id ? `<p>User ID: ${alert.user_id}</p>` : ''}
                                    <button onclick="resolveAlert('${alert.id}')">Resolve</button>
                                </div>
                                <hr>
                            `;
                        }

                        alertsEl.innerHTML = alertsHtml || '<p>No active alerts</p>';
                    } catch (error) {
                        console.error('Error updating dashboard:', error);
                    }
                }

                // Format uptime
                function formatUptime(seconds) {
                    const days = Math.floor(seconds / 86400);
                    const hours = Math.floor((seconds % 86400) / 3600);
                    const minutes = Math.floor((seconds % 3600) / 60);

                    let result = '';
                    if (days > 0) result += `${days}d `;
                    if (hours > 0 || days > 0) result += `${hours}h `;
                    result += `${minutes}m`;

                    return result;
                }

                // Resolve alert
                async function resolveAlert(alertId) {
                    try {
                        await fetch(`/monitoring/alerts/${alertId}/resolve`, { method: 'POST' });
                        updateDashboard();
                    } catch (error) {
                        console.error('Error resolving alert:', error);
                    }
                }

                // Initial update
                updateDashboard();

                // Update every 30 seconds
                setInterval(updateDashboard, 30000);
            </script>
        </body>
        </html>
        