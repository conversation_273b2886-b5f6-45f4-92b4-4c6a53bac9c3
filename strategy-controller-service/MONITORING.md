# Oryn Trading Platform Monitoring System

This document provides instructions for accessing and using the Oryn Trading Platform monitoring dashboard in both local development and production environments.

## Overview

The monitoring dashboard provides real-time visibility into the health and status of all system components, including:

- Strategy controller service status
- Trade bot instances health
- Dependency status (OANDA, Firebase, etc.)
- Active alerts and issues
- Trade execution metrics

## Accessing the Monitoring Dashboard

### Local Development Environment

To access the monitoring dashboard in your local development environment:

1. **Ensure the Strategy Controller is Running**
   - The strategy controller service should be running on port 8080
   - You can start it with `cd strategy-controller-service && ./run_local.sh`

2. **Install the ModHeader Chrome Extension**
   - Install from the [Chrome Web Store](https://chrome.google.com/webstore/detail/modheader/idgpnmonknjnojddfkpgkljpfnnfcklj)
   - This extension allows you to add authentication headers to your requests

3. **Configure ModHeader**
   - Click on the ModHeader extension icon in your Chrome toolbar
   - Add a new header:
     - Name: `Authorization`
     - Value: `Bearer dev-token`
   - Make sure the header is enabled (toggle switch is on)

4. **Access the Dashboard**
   - Navigate to `http://localhost:8080/monitoring`
   - The dashboard should load with all system health information

### Production Environment

To access the monitoring dashboard in the production environment:

1. **Obtain a Valid Authentication Token**
   - Contact your system administrator for a valid authentication token
   - This token should be associated with an admin account

2. **Install the ModHeader Chrome Extension**
   - Install from the [Chrome Web Store](https://chrome.google.com/webstore/detail/modheader/idgpnmonknjnojddfkpgkljpfnnfcklj)

3. **Configure ModHeader**
   - Click on the ModHeader extension icon in your Chrome toolbar
   - Add a new header:
     - Name: `Authorization`
     - Value: `Bearer YOUR_PRODUCTION_TOKEN`
   - Make sure the header is enabled (toggle switch is on)

4. **Access the Dashboard**
   - Navigate to `https://api.oryntrade.com/monitoring`
   - The dashboard should load with all system health information

## Using the Monitoring Dashboard

### System Status Overview

The top section of the dashboard shows the overall system status:

- **Healthy**: All components are functioning normally
- **Unhealthy**: One or more components have issues
- **Last Updated**: When the status was last refreshed
- **Uptime**: How long the monitoring service has been running

### Service Health Cards

Each service in the system has a health card showing:

- **Service Name**: Name and instance identifier
- **Status**: Current health status (healthy, unhealthy)
- **Version**: Software version running
- **Uptime**: How long the service has been running
- **Dependencies**: Status of service dependencies
- **Error Messages**: Any reported errors (if unhealthy)

For trade bots, additional information is shown:

- **Strategy ID**: The strategy being executed
- **User ID**: The user who owns the strategy
- **Pod Name**: The Kubernetes pod name (in production)

### Active Alerts

The alerts section shows any active issues requiring attention:

- **Service**: The affected service
- **Status**: Alert severity
- **Message**: Description of the issue
- **Created**: When the alert was generated
- **Resolve Button**: Click to mark an alert as resolved

### Trade Execution Statistics

The statistics section shows performance metrics:

- **Success Rate**: Percentage of successful trade executions
- **Average Execution Time**: Time taken to execute trades
- **Total Trades**: Number of trades processed

## Troubleshooting

### Cannot Access the Dashboard

If you cannot access the dashboard:

1. **Check the Strategy Controller**
   - Ensure the strategy controller service is running
   - Check the logs for any errors: `cd strategy-controller-service && tail -f logs/app.log`

2. **Verify Authentication**
   - Make sure the ModHeader extension is properly configured
   - Check that the authentication token is valid
   - Try refreshing the token if it may have expired

3. **Network Issues**
   - Ensure you can reach the server (try `ping api.oryntrade.com` for production)
   - Check if any firewalls might be blocking access

### Dashboard Shows Unhealthy Services

If the dashboard shows unhealthy services:

1. **Check the Service Logs**
   - Look at the logs for the unhealthy service
   - Check for error messages or exceptions

2. **Verify Dependencies**
   - Ensure all dependencies (OANDA, Firebase, etc.) are accessible
   - Check network connectivity to external services

3. **Restart the Service**
   - Try restarting the unhealthy service
   - Monitor the logs during restart for any issues

## Additional Information

### API Access

You can also access the monitoring data programmatically:

```bash
# Get health data
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8080/monitoring/health

# Get alerts
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8080/monitoring/alerts
```

### Automatic Refresh

The dashboard automatically refreshes every 30 seconds to show the latest data. You can manually refresh by reloading the page.

### Security Note

The monitoring dashboard contains sensitive system information. Always:

- Use strong, unique authentication tokens
- Do not share your token with unauthorized users
- Log out or disable ModHeader when you're done
- Access the dashboard over HTTPS in production

## Support

For additional help or to report issues with the monitoring dashboard:

- **Local Development**: Contact the development team lead
- **Production**: Submit a support ticket through the internal support system

---

This documentation will be updated as new features are added to the monitoring system.
