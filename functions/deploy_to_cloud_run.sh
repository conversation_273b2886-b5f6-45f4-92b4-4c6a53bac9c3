#!/bin/bash

# Oryn WebSocket Service - Cloud Run Deployment Script
# This script builds and deploys the WebSocket service to Google Cloud Run

set -e  # Exit on any error

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"oryntrade"}
SERVICE_NAME="oryn-websocket-service"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Deploying Oryn WebSocket Service to Cloud Run${NC}"
echo "=================================================="
echo "Project ID: ${PROJECT_ID}"
echo "Service Name: ${SERVICE_NAME}"
echo "Region: ${REGION}"
echo "Image: ${IMAGE_NAME}"
echo "=================================================="

# Check if required tools are installed
echo -e "${YELLOW}🔧 Checking prerequisites...${NC}"

if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI not found. Please install Google Cloud SDK.${NC}"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker not found. Please install Docker.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Authenticate and set project
echo -e "${YELLOW}🔐 Setting up Google Cloud project...${NC}"
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo -e "${YELLOW}🔌 Enabling required APIs...${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Build the Docker image
echo -e "${YELLOW}🏗️ Building Docker image...${NC}"
docker build -t ${IMAGE_NAME} .

# Push the image to Google Container Registry
echo -e "${YELLOW}📤 Pushing image to Container Registry...${NC}"
docker push ${IMAGE_NAME}

# Deploy to Cloud Run
echo -e "${YELLOW}🚀 Deploying to Cloud Run...${NC}"
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME} \
    --platform managed \
    --region ${REGION} \
    --allow-unauthenticated \
    --port 8080 \
    --memory 2Gi \
    --cpu 2 \
    --min-instances 1 \
    --max-instances 10 \
    --concurrency 1000 \
    --timeout 3600 \
    --set-env-vars ENVIRONMENT=production \
    --set-env-vars POLYGON_API_KEY=${POLYGON_API_KEY}

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --platform managed --region ${REGION} --format 'value(status.url)')

echo ""
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo "=================================================="
echo -e "${GREEN}Service URL: ${SERVICE_URL}${NC}"
echo -e "${GREEN}WebSocket URL: ${SERVICE_URL/https/wss}/ws${NC}"
echo -e "${GREEN}Health Check: ${SERVICE_URL}/health${NC}"
echo -e "${GREEN}Status: ${SERVICE_URL}/status${NC}"
echo "=================================================="

# Test the deployment
echo -e "${YELLOW}🧪 Testing deployment...${NC}"
if curl -f "${SERVICE_URL}/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${RED}❌ Health check failed${NC}"
    echo "Please check the logs: gcloud run logs read --service=${SERVICE_NAME} --region=${REGION}"
fi

echo ""
echo -e "${BLUE}📝 Next steps:${NC}"
echo "1. Update your frontend to use the WebSocket URL: ${SERVICE_URL/https/wss}/ws"
echo "2. Monitor the service: gcloud run logs tail --service=${SERVICE_NAME} --region=${REGION}"
echo "3. Scale if needed: gcloud run services update ${SERVICE_NAME} --max-instances=50 --region=${REGION}"

echo ""
echo -e "${GREEN}🎯 Deployment complete!${NC}"
