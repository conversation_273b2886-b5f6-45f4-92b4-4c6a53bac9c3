#!/usr/bin/env python3
"""
Test script for production WebSocket setup.
Tests both local production server and Firestore state management.
"""

import os
import sys
import asyncio
import json
import websockets
import requests
from datetime import datetime, timezone

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_environment():
    """Test environment setup."""
    print("🔄 Testing Environment Setup...")

    required_vars = ["POLYGON_API_KEY"]
    missing_vars = []

    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * 10}...{value[-4:]}")
        else:
            print(f"❌ {var}: Not set")
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False

    print("✅ Environment setup is correct")
    return True

async def test_production_server(base_url="http://localhost:8081"):
    """Test the production FastAPI server."""
    print(f"🔄 Testing Production Server at {base_url}...")

    try:
        # Test health endpoint
        print("📊 Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check passed: {health_data.get('status')}")
            print(f"   Connected clients: {health_data.get('connected_clients', 0)}")
            print(f"   Polygon connected: {health_data.get('polygon_connected', False)}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False

        # Test status endpoint
        print("📈 Testing status endpoint...")
        response = requests.get(f"{base_url}/status", timeout=10)
        if response.status_code == 200:
            status_data = response.json()
            print(f"✅ Status check passed")
            print(f"   WebSocket server running: {status_data.get('is_running', False)}")
            print(f"   Production state: {status_data.get('use_production_state', False)}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False

        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ Server connection failed: {e}")
        return False

async def test_websocket_connection(ws_url="ws://localhost:8081/ws"):
    """Test WebSocket connection and basic functionality."""
    print(f"🔄 Testing WebSocket Connection at {ws_url}...")

    try:
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket connection established")

            # Wait for welcome message
            welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            welcome_data = json.loads(welcome_msg)

            if welcome_data.get("type") == "connection":
                print(f"✅ Welcome message received: {welcome_data.get('message')}")
                session_id = welcome_data.get('session_id')
                print(f"   Session ID: {session_id}")
            else:
                print(f"❌ Unexpected welcome message: {welcome_data}")
                return False

            # Test subscription
            print("📡 Testing subscription...")
            subscribe_msg = {
                "type": "subscribe",
                "forex_pair": "EURUSD",
                "timeframe": "1m"
            }
            await websocket.send(json.dumps(subscribe_msg))

            # Wait for subscription confirmation
            response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
            response_data = json.loads(response)

            if response_data.get("type") == "subscription_confirmed":
                print(f"✅ Subscription confirmed for {response_data.get('forex_pair')}")
            else:
                print(f"❌ Subscription failed: {response_data}")
                return False

            # Test ping
            print("🏓 Testing ping...")
            ping_msg = {"type": "ping"}
            await websocket.send(json.dumps(ping_msg))

            pong_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            pong_data = json.loads(pong_response)

            if pong_data.get("type") == "pong":
                print("✅ Ping/pong test passed")
            else:
                print(f"❌ Ping/pong test failed: {pong_data}")
                return False

            print("✅ WebSocket tests completed successfully")
            return True

    except asyncio.TimeoutError:
        print("❌ WebSocket test timed out")
        return False
    except websockets.exceptions.ConnectionClosed:
        print("❌ WebSocket connection closed unexpectedly")
        return False
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False

async def test_firestore_state():
    """Test Firestore state management."""
    print("🔄 Testing Firestore State Management...")

    try:
        from production_state_manager import ProductionStateManager

        state_manager = ProductionStateManager()
        print(f"✅ State manager initialized for instance: {state_manager.instance_id}")

        # Test adding a subscription
        print("📝 Testing subscription management...")
        needs_polygon = await state_manager.add_user_subscription(
            "test_user", "test_session", "EURUSD", "1m"
        )
        print(f"✅ Added test subscription (needs_polygon: {needs_polygon})")

        # Test getting subscribed users
        users = await state_manager.get_users_subscribed_to("EURUSD", "1m")
        print(f"✅ Found {len(users)} users subscribed to EURUSD")

        # Test removing subscription
        result = await state_manager.remove_user_subscription("test_user", "test_session")
        print(f"✅ Removed test subscription (should_unsubscribe: {result is not None})")

        print("✅ Firestore state management tests passed")
        return True

    except Exception as e:
        print(f"❌ Firestore test failed: {e}")
        print("   Make sure you have Firestore access and proper credentials")
        return False

async def main():
    """Run all tests."""
    print("🧪 Starting Production Setup Tests")
    print("=" * 50)

    # Test environment
    if not test_environment():
        print("❌ Environment test failed - stopping")
        return

    print("\n" + "=" * 50)

    # Test Firestore (if available)
    try:
        await test_firestore_state()
    except Exception as e:
        print(f"⚠️ Skipping Firestore test: {e}")

    print("\n" + "=" * 50)

    # Check if production server is running
    server_running = await test_production_server()

    if server_running:
        print("\n" + "=" * 50)
        # Test WebSocket if server is running
        await test_websocket_connection()
    else:
        print("\n💡 To test the production server:")
        print("   1. Run: python production_websocket_server.py")
        print("   2. Then run this test script again")

    print("\n" + "=" * 50)
    print("🧪 Production setup tests completed")

if __name__ == "__main__":
    asyncio.run(main())
