"""
Polygon.io WebSocket client service for real-time forex data streaming.
"""

import asyncio
import json
import logging
import os
import time
import websockets
from datetime import datetime, timezone
from typing import Dict, List, Optional, Callable, Any
import threading
from websocket import WebSocketApp
import traceback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PolygonWebSocketClient:
    """
    WebSocket client for connecting to Polygon.io real-time data streams.
    Handles forex data streaming with automatic reconnection and error handling.
    """

    def __init__(self, api_key: str, on_message_callback: Optional[Callable] = None):
        """
        Initialize the Polygon WebSocket client.

        Args:
            api_key: Polygon.io API key
            on_message_callback: Callback function for processing incoming messages
        """
        self.api_key = api_key
        self.on_message_callback = on_message_callback
        self.ws = None
        self.is_connected = False
        self.subscriptions = set()
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 5  # seconds

        # Polygon WebSocket URLs
        self.ws_url = "wss://socket.polygon.io/forex"

        # Connection state
        self.connection_thread = None
        self.should_reconnect = True

        logger.info("PolygonWebSocketClient initialized")

    def connect(self):
        """Start the WebSocket connection in a separate thread."""
        if self.connection_thread and self.connection_thread.is_alive():
            logger.warning("Connection thread already running")
            return

        self.should_reconnect = True
        self.connection_thread = threading.Thread(target=self._run_websocket, daemon=True)
        self.connection_thread.start()
        logger.info("WebSocket connection thread started")

    def disconnect(self):
        """Disconnect from the WebSocket."""
        self.should_reconnect = False
        if self.ws:
            self.ws.close()
        logger.info("WebSocket disconnection requested")

    def _run_websocket(self):
        """Run the WebSocket connection with automatic reconnection."""
        while self.should_reconnect:
            try:
                self.ws = WebSocketApp(
                    self.ws_url,
                    on_open=self._on_open,
                    on_message=self._on_message,
                    on_error=self._on_error,
                    on_close=self._on_close
                )

                logger.info(f"Connecting to Polygon WebSocket: {self.ws_url}")
                self.ws.run_forever()

            except Exception as e:
                logger.error(f"WebSocket connection error: {e}")
                logger.error(traceback.format_exc())

            if self.should_reconnect:
                self.reconnect_attempts += 1
                if self.reconnect_attempts <= self.max_reconnect_attempts:
                    delay = min(self.reconnect_delay * self.reconnect_attempts, 60)
                    logger.info(f"Reconnecting in {delay} seconds (attempt {self.reconnect_attempts})")
                    time.sleep(delay)
                else:
                    logger.error("Max reconnection attempts reached")
                    break

    def _on_open(self, ws):
        """Handle WebSocket connection opened."""
        logger.info("WebSocket connection opened")
        self.is_connected = True
        self.reconnect_attempts = 0

        # Authenticate with API key
        auth_message = {
            "action": "auth",
            "params": self.api_key
        }
        ws.send(json.dumps(auth_message))
        logger.info("Authentication message sent")

    def _on_message(self, ws, message):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(message)
            logger.info(f"📨 Raw Polygon message received: {data}")

            # Handle different message types
            if isinstance(data, list):
                for item in data:
                    self._process_message(item)
            else:
                self._process_message(data)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse WebSocket message: {e}")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
            logger.error(traceback.format_exc())

    def send_ping(self):
        """Send a ping to keep the connection alive and test connectivity."""
        try:
            if self.ws and self.is_connected:
                ping_message = {"action": "ping"}
                self.ws.send(json.dumps(ping_message))
                logger.info("📡 Sent ping to Polygon WebSocket")
        except Exception as e:
            logger.error(f"Error sending ping: {e}")

    def _process_message(self, message: Dict[str, Any]):
        """Process individual WebSocket messages."""
        try:
            # Log all incoming messages for debugging
            logger.info(f"🔍 Polygon message received: {message}")

            # Handle authentication response
            if message.get("ev") == "status":
                if message.get("status") == "auth_success":
                    logger.info("✅ WebSocket authentication successful")
                    # Re-subscribe to any existing subscriptions
                    self._resubscribe()
                elif message.get("status") == "auth_failed":
                    logger.error("❌ WebSocket authentication failed")
                    return
                else:
                    logger.info(f"📊 Status message: {message}")

            # Handle forex candle data (CA = forex aggregate/candle)
            elif message.get("ev") == "CA":
                logger.info(f"📈 Received candle data: {message}")
                candle_data = self._format_candle_data(message)
                if candle_data and self.on_message_callback:
                    self.on_message_callback(candle_data)

            # Handle forex tick data (T = forex ticks/real-time trades)
            elif message.get("ev") == "T":
                logger.info(f"📊 Received tick data: {message}")
                tick_data = self._format_tick_data(message)
                if tick_data and self.on_message_callback:
                    self.on_message_callback(tick_data)

            # Handle forex quote data (C = forex quotes/real-time prices)
            elif message.get("ev") == "C":
                logger.info(f"💱 Received quote data: {message}")
                quote_data = self._format_quote_data(message)
                if quote_data and self.on_message_callback:
                    self.on_message_callback(quote_data)

            # Handle unknown message types
            else:
                logger.info(f"❓ Unknown message type: {message.get('ev', 'no_ev')} - {message}")

        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
            logger.error(f"Message: {message}")

    def _format_candle_data(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Format Polygon WebSocket candle data to match our expected format.

        Args:
            message: Raw Polygon WebSocket message

        Returns:
            Formatted candle data or None if invalid
        """
        try:
            # Extract symbol - for candle data (CA), Polygon uses 'pair' field
            symbol = message.get("pair", message.get("sym", "")).replace("C:", "")
            if not symbol:
                logger.warning(f"No symbol found in candle message: {message}")
                return None

            # Convert timestamp from milliseconds to seconds
            timestamp = message.get("s", 0) / 1000

            logger.info(f"🔧 Formatting candle data: symbol={symbol}, timestamp={timestamp}")

            formatted_data = {
                "type": "candle",  # Mark as candle data for CandleBuilder
                "symbol": symbol,
                "timestamp": timestamp,
                "time": int(timestamp),  # For chart compatibility
                "open": message.get("o", 0),
                "high": message.get("h", 0),
                "low": message.get("l", 0),
                "close": message.get("c", 0),
                "volume": message.get("v", 0),
                "vwap": message.get("vw", 0),  # Volume weighted average price
                "transactions": message.get("n", 0),  # Number of transactions
                "is_complete": True,  # Polygon sends complete candles
                "timeframe": self._extract_timeframe(message),
                "received_at": datetime.now(timezone.utc).isoformat()
            }

            logger.info(f"✅ Formatted candle data: {symbol} OHLC={formatted_data['open']}/{formatted_data['high']}/{formatted_data['low']}/{formatted_data['close']}")

            return formatted_data

        except Exception as e:
            logger.error(f"Error formatting candle data: {e}")
            return None

    def _format_quote_data(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Format Polygon WebSocket quote data for real-time price updates.

        Args:
            message: Raw Polygon WebSocket quote message

        Returns:
            Formatted quote data or None if invalid
        """
        try:
            # Extract symbol - for forex it should be like "EUR/USD"
            symbol = message.get("p", "")
            if symbol.startswith("C."):
                symbol = symbol[2:]  # Remove "C." prefix
            if not symbol:
                return None

            # Convert timestamp from milliseconds to seconds
            timestamp = message.get("t", 0) / 1000

            # Get bid/ask prices
            bid = message.get("b", 0)
            ask = message.get("a", 0)

            # Calculate mid price for current candle update
            mid_price = (bid + ask) / 2 if bid and ask else 0

            formatted_data = {
                "type": "quote",  # Distinguish from candle data
                "symbol": symbol,
                "timestamp": timestamp,
                "time": int(timestamp),
                "bid": bid,
                "ask": ask,
                "mid_price": mid_price,
                "current_price": mid_price,  # For real-time candle updates
                "is_realtime": True,
                "received_at": datetime.now(timezone.utc).isoformat()
            }

            return formatted_data

        except Exception as e:
            logger.error(f"Error formatting quote data: {e}")
            return None

    def _format_tick_data(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Format Polygon WebSocket tick data for real-time candle updates.

        Args:
            message: Raw Polygon WebSocket tick message

        Returns:
            Formatted tick data or None if invalid
        """
        try:
            # Extract symbol and remove C: prefix for forex
            symbol = message.get("sym", "").replace("C:", "")
            if not symbol:
                return None

            # Convert timestamp from nanoseconds to seconds
            timestamp = message.get("t", 0) / 1000000000  # Polygon ticks are in nanoseconds

            # Get tick price (for forex ticks, price is usually in 'p' field)
            price = message.get("p", 0)

            formatted_data = {
                "type": "tick",  # Distinguish from candle data
                "symbol": symbol,
                "timestamp": timestamp,
                "time": int(timestamp),
                "price": price,
                "current_price": price,  # For real-time candle updates
                "is_realtime": True,
                "is_tick": True,
                "received_at": datetime.now(timezone.utc).isoformat()
            }

            return formatted_data

        except Exception as e:
            logger.error(f"Error formatting tick data: {e}")
            return None

    def _extract_timeframe(self, message: Dict[str, Any]) -> str:
        """Extract timeframe from message or default to 1m."""
        # Polygon doesn't always include timeframe in the message
        # We'll need to track this based on our subscriptions
        return "1m"  # Default to 1 minute

    def _on_error(self, ws, error):
        """Handle WebSocket errors."""
        logger.error(f"WebSocket error: {error}")
        self.is_connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket connection closed."""
        logger.info(f"WebSocket connection closed: {close_status_code} - {close_msg}")
        self.is_connected = False

    def subscribe_to_forex_pair(self, forex_pair: str, timeframe: str = "1m"):
        """
        Subscribe to real-time data for a forex pair.

        Args:
            forex_pair: Forex pair (e.g., "EURUSD", "GBPUSD")
            timeframe: Timeframe for candles (e.g., "1m", "5m", "1h")
        """
        try:
            # Format symbol for Polygon forex (correct format with slash)
            # Convert EURUSD -> EUR/USD for Polygon API
            if len(forex_pair) == 6:
                base_currency = forex_pair[:3]
                quote_currency = forex_pair[3:]
                forex_symbol = f"{base_currency}/{quote_currency}"
            else:
                forex_symbol = forex_pair  # Already has slash or different format

            # Use the correct Polygon forex subscription format
            logger.info(f"🔧 Formatted forex symbol: {forex_pair} -> C.{forex_symbol}")

            # Subscribe to forex candles and quotes using correct format
            subscriptions = [
                {
                    "action": "subscribe",
                    "params": f"CA.{forex_symbol}"  # CA.EUR/USD format for candle data
                },
                {
                    "action": "subscribe",
                    "params": f"C.{forex_symbol}"  # C.EUR/USD format for quote data (for real-time price updates)
                }
            ]

            logger.info(f"📡 Subscribing to {len(subscriptions)} data types for C.{forex_symbol}")

            if self.ws and self.is_connected:
                for subscription in subscriptions:
                    self.ws.send(json.dumps(subscription))
                    self.subscriptions.add(subscription["params"])
                    logger.info(f"Subscribed to {subscription['params']}")
            else:
                # Store subscriptions for when connection is established
                for subscription in subscriptions:
                    self.subscriptions.add(subscription["params"])
                    logger.info(f"Stored subscription for {subscription['params']} (not connected yet)")

        except Exception as e:
            logger.error(f"Error subscribing to {forex_pair}: {e}")

    def unsubscribe_from_forex_pair(self, forex_pair: str):
        """
        Unsubscribe from real-time data for a forex pair.

        Args:
            forex_pair: Forex pair to unsubscribe from
        """
        try:
            # Format symbol for Polygon forex (correct format with slash)
            if len(forex_pair) == 6:
                base_currency = forex_pair[:3]
                quote_currency = forex_pair[3:]
                forex_symbol = f"{base_currency}/{quote_currency}"
            else:
                forex_symbol = forex_pair

            # Unsubscribe from both candle and quote data
            candle_symbol = f"CA.{forex_symbol}"
            quote_symbol = f"C.{forex_symbol}"

            for symbol in [candle_symbol, quote_symbol]:
                if symbol in self.subscriptions:
                    unsubscription = {
                        "action": "unsubscribe",
                        "params": symbol
                    }

                    if self.ws and self.is_connected:
                        self.ws.send(json.dumps(unsubscription))

                    self.subscriptions.remove(symbol)
                    logger.info(f"Unsubscribed from {symbol}")

        except Exception as e:
            logger.error(f"Error unsubscribing from {forex_pair}: {e}")

    def _resubscribe(self):
        """Re-subscribe to all stored subscriptions after reconnection."""
        if not self.subscriptions:
            return

        for subscription in self.subscriptions:
            try:
                subscribe_message = {
                    "action": "subscribe",
                    "params": subscription
                }
                self.ws.send(json.dumps(subscribe_message))
                logger.info(f"Re-subscribed to {subscription}")
            except Exception as e:
                logger.error(f"Error re-subscribing to {subscription}: {e}")

    def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status and statistics."""
        return {
            "is_connected": self.is_connected,
            "subscriptions": list(self.subscriptions),
            "reconnect_attempts": self.reconnect_attempts,
            "max_reconnect_attempts": self.max_reconnect_attempts
        }


# Global instance for the WebSocket client
_polygon_ws_client = None

def get_polygon_websocket_client() -> PolygonWebSocketClient:
    """Get or create the global Polygon WebSocket client instance."""
    global _polygon_ws_client

    if _polygon_ws_client is None:
        api_key = os.getenv("POLYGON_API_KEY")
        if not api_key:
            raise ValueError("POLYGON_API_KEY environment variable not set")

        _polygon_ws_client = PolygonWebSocketClient(api_key)

    return _polygon_ws_client
