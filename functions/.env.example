# Oryn WebSocket Service Environment Configuration

# Required: Polygon.io API Key
POLYGON_API_KEY=your_polygon_api_key_here

# Environment (development or production)
ENVIRONMENT=development

# WebSocket Server Configuration
WEBSOCKET_HOST=localhost
WEBSOCKET_PORT=8765

# Cloud Run Configuration (for production)
PORT=8080
GOOGLE_CLOUD_PROJECT=oryntrade

# Optional: Logging Level
LOG_LEVEL=INFO

# Optional: Service Configuration
MAX_CONNECTIONS=1000
CLEANUP_INTERVAL_MINUTES=30
