# Oryn Real-time WebSocket Service - Phase 1.5 (Production Ready)

This document describes the Phase 1.5 implementation of the real-time WebSocket service for streaming live forex data from Polygon.io to the frontend. This version is production-ready and can be deployed to Google Cloud Run.

## Overview

Phase 1.5 implements a complete production-ready WebSocket infrastructure:

1. **Polygon.io WebSocket Client** - Connects to Polygon.io for real-time forex data
2. **Production Candle Broadcaster** - WebSocket server with Firestore state management
3. **FastAPI Production Server** - Production-grade web server with health checks
4. **Firestore State Management** - Persistent state across instance restarts
5. **Docker Containerization** - Ready for Cloud Run deployment
6. **Automated Deployment** - Scripts for easy Cloud Run deployment

## Architecture

```
Polygon.io WebSocket → PolygonWebSocketClient → CandleBroadcaster → Frontend Clients
                                             ↓
                                    Firebase Functions (Management)
```

## Files Created

### Core Services
- `polygon_websocket_service.py` - Polygon.io WebSocket client with reconnection logic
- `realtime_candle_broadcaster.py` - WebSocket server with production state management
- `production_state_manager.py` - **NEW** Firestore state management for production
- `production_websocket_server.py` - **NEW** FastAPI production server
- `main.py` - Added Firebase Functions for service management

### Production Deployment
- `Dockerfile` - **NEW** Container configuration for Cloud Run
- `.dockerignore` - **NEW** Docker build optimization
- `deploy_to_cloud_run.sh` - **NEW** Automated deployment script
- `.env.example` - **NEW** Environment configuration template

### Testing & Utilities
- `test_websocket_service.py` - Test script for WebSocket functionality
- `test_production_setup.py` - **NEW** Production setup testing
- `start_websocket_service.py` - Standalone service runner for development
- `requirements.txt` - Updated with production dependencies

## Dependencies Added

```
websockets>=12.0
websocket-client>=1.6.4
fastapi>=0.104.1
uvicorn>=0.24.0
```

## Production Deployment

### Quick Deploy to Cloud Run

1. **Set up environment variables:**
```bash
export POLYGON_API_KEY="your_polygon_api_key"
export GOOGLE_CLOUD_PROJECT="your-project-id"
```

2. **Deploy with one command:**
```bash
cd functions
./deploy_to_cloud_run.sh
```

### Manual Deployment Steps

1. **Build Docker image:**
```bash
docker build -t gcr.io/your-project/oryn-websocket .
```

2. **Push to Container Registry:**
```bash
docker push gcr.io/your-project/oryn-websocket
```

3. **Deploy to Cloud Run:**
```bash
gcloud run deploy oryn-websocket-service \
  --image gcr.io/your-project/oryn-websocket \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars ENVIRONMENT=production,POLYGON_API_KEY=$POLYGON_API_KEY
```

### Local Production Testing

1. **Run production server locally:**
```bash
cd functions
ENVIRONMENT=production python production_websocket_server.py
```

2. **Test the setup:**
```bash
python test_production_setup.py
```

## Environment Variables Required

```bash
POLYGON_API_KEY=your_polygon_api_key_here
WEBSOCKET_HOST=localhost  # Optional, defaults to localhost
WEBSOCKET_PORT=8765       # Optional, defaults to 8765
```

## Firebase Functions Added

### 1. `start_realtime_websocket_service`
- **URL**: `/start_realtime_websocket_service`
- **Method**: POST
- **Purpose**: Start the WebSocket service
- **Response**: Service status and WebSocket URL

### 2. `get_websocket_service_status`
- **URL**: `/get_websocket_service_status`
- **Method**: GET
- **Purpose**: Get current service status
- **Response**: Connection status, client count, subscriptions

### 3. `subscribe_to_realtime_data`
- **URL**: `/subscribe_to_realtime_data`
- **Method**: POST
- **Body**: `{"forex_pair": "EURUSD", "timeframe": "1m", "action": "subscribe"}`
- **Purpose**: Manage Polygon.io subscriptions

## WebSocket Protocol

### Client → Server Messages

#### Subscribe to Forex Pair
```json
{
  "type": "subscribe",
  "forex_pair": "EURUSD",
  "timeframe": "1m"
}
```

#### Unsubscribe from Forex Pair
```json
{
  "type": "unsubscribe",
  "forex_pair": "EURUSD",
  "timeframe": "1m"
}
```

#### Ping
```json
{
  "type": "ping"
}
```

### Server → Client Messages

#### Connection Confirmation
```json
{
  "type": "connection",
  "status": "connected",
  "message": "Connected to Oryn real-time data stream",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Subscription Confirmed
```json
{
  "type": "subscription_confirmed",
  "forex_pair": "EURUSD",
  "timeframe": "1m",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Real-time Candle Data
```json
{
  "type": "candle_update",
  "data": {
    "symbol": "EURUSD",
    "timestamp": 1704067200,
    "time": 1704067200,
    "open": 1.1050,
    "high": 1.1055,
    "low": 1.1048,
    "close": 1.1052,
    "volume": 1000,
    "vwap": 1.1051,
    "transactions": 50,
    "is_complete": true,
    "timeframe": "1m",
    "received_at": "2024-01-01T00:00:00Z"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Pong Response
```json
{
  "type": "pong",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Error Message
```json
{
  "type": "error",
  "message": "Error description",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Testing

### 1. Test WebSocket Services
```bash
cd functions
python test_websocket_service.py
```

### 2. Run Standalone Service
```bash
cd functions
python start_websocket_service.py
```

### 3. Test with WebSocket Client
```bash
# Install wscat if not already installed
npm install -g wscat

# Connect to the service
wscat -c ws://localhost:8765

# Send subscription message
{"type": "subscribe", "forex_pair": "EURUSD", "timeframe": "1m"}
```

## Features Implemented

### PolygonWebSocketClient
- ✅ Automatic connection and reconnection
- ✅ Authentication with Polygon.io
- ✅ Subscription management
- ✅ Message parsing and formatting
- ✅ Error handling and logging
- ✅ Connection status monitoring

### CandleBroadcaster
- ✅ WebSocket server for frontend clients
- ✅ Client connection management
- ✅ Subscription tracking per client
- ✅ Automatic Polygon subscription management
- ✅ Message broadcasting to subscribed clients
- ✅ Client cleanup on disconnect

### Firebase Functions
- ✅ Service lifecycle management
- ✅ Status monitoring
- ✅ Subscription management
- ✅ CORS support for frontend integration

## Known Limitations

1. **Single Instance**: Currently runs as a single instance (not horizontally scalable)
2. **Memory Storage**: Subscriptions stored in memory (lost on restart)
3. **No Persistence**: No persistent storage for connection state
4. **Local Development**: Optimized for local development environment

## Next Steps (Phase 2)

1. **Frontend Integration**: Create React hooks and WebSocket client
2. **Chart Integration**: Integrate with OptimizedTradingChart.js
3. **Real-time Updates**: Implement live candle updating logic
4. **Error Handling**: Add frontend error handling and reconnection
5. **Performance**: Optimize for production deployment

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check POLYGON_API_KEY environment variable
2. **Port Already in Use**: Change WEBSOCKET_PORT or kill existing process
3. **Authentication Failed**: Verify Polygon.io API key is valid and has WebSocket access
4. **No Data Received**: Ensure forex markets are open and pair is valid

### Logs

The service provides detailed logging for debugging:
- Connection status
- Authentication results
- Subscription management
- Message processing
- Error conditions

### Testing Connection

```bash
# Test if service is running
curl -X GET http://localhost:5001/oryntrade/us-central1/get_websocket_service_status

# Start the service
curl -X POST http://localhost:5001/oryntrade/us-central1/start_realtime_websocket_service
```
