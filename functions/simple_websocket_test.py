#!/usr/bin/env python3
"""
Simple WebSocket test using websocket-client (synchronous).
"""

import json
import time
from websocket import create_connection

def test_websocket():
    """Test WebSocket connection."""
    print("🔄 Testing WebSocket connection...")
    
    try:
        # Connect to WebSocket
        ws = create_connection("ws://localhost:8081/ws")
        print("✅ WebSocket connection established")
        
        # Wait for welcome message
        welcome_msg = ws.recv()
        welcome_data = json.loads(welcome_msg)
        print(f"✅ Welcome message: {welcome_data.get('message')}")
        print(f"   Session ID: {welcome_data.get('session_id')}")
        
        # Test subscription
        print("📡 Testing subscription to EURUSD...")
        subscribe_msg = {
            "type": "subscribe",
            "forex_pair": "EURUSD",
            "timeframe": "1m"
        }
        ws.send(json.dumps(subscribe_msg))
        
        # Wait for subscription confirmation
        response = ws.recv()
        response_data = json.loads(response)
        print(f"✅ Subscription response: {response_data}")
        
        # Test ping
        print("🏓 Testing ping...")
        ping_msg = {"type": "ping"}
        ws.send(json.dumps(ping_msg))
        
        # Wait for pong
        pong_response = ws.recv()
        pong_data = json.loads(pong_response)
        print(f"✅ Pong response: {pong_data}")
        
        # Wait a bit for any real-time data
        print("⏳ Waiting for real-time data (10 seconds)...")
        ws.settimeout(10)
        try:
            while True:
                data = ws.recv()
                message = json.loads(data)
                if message.get("type") == "candle_update":
                    print(f"📊 Received candle data: {message['data']['symbol']} - {message['data']['close']}")
                    break
                else:
                    print(f"📨 Received message: {message.get('type')}")
        except Exception as e:
            print(f"⏰ No real-time data received in 10 seconds (this is normal if markets are closed)")
        
        # Close connection
        ws.close()
        print("✅ WebSocket test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False

if __name__ == "__main__":
    test_websocket()
