import subprocess

def deploy_firebase_functions(function_names):
    """
    Deploys a list of Firebase Cloud Functions using gcloud.

    Args:
        function_names: A list of function names to deploy.
    """

    base_command = [
        "gcloud", "functions", "deploy",
        "--gen2",
        "--region=us-central1",
        "--project=oryntrade",
        "--runtime=python312",
        "--build-service-account=projects/oryntrade/serviceAccounts/<EMAIL>",
        "--memory=256Mi",
        "--trigger-http",
        "--source=.",
    ]

    for function_name in function_names:
        deploy_command = base_command + [
            function_name,
            f"--entry-point={function_name}"  # Assuming entry point matches function name
        ]

        try:
            print(f"Deploying function: {function_name}")
            subprocess.run(deploy_command, check=True)
            print(f"Function {function_name} deployed successfully.")
        except subprocess.CalledProcessError as e:
            print(f"Error deploying function {function_name}: {e}")
        except FileNotFoundError:
            print("Error: gcloud command not found. Make sure Google Cloud SDK is installed.")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")

# Example usage:
functions_to_deploy = [
    # "signup",
    # "get_user",
    # "register_api_key",
    # "get_oanda_account",
    # "get_strategies",
    # "save_strategy",
    # "update_strategy",
    "handle_contact_form",
    "handle_email_signup",
    "get_forex_pairs_oanda",
]

deploy_firebase_functions(functions_to_deploy)