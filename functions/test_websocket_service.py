#!/usr/bin/env python3
"""
Test script for the Polygon.io WebSocket service.
This script tests the WebSocket connection and data streaming functionality.
"""

import os
import sys
import time
import json
import asyncio
import threading
from datetime import datetime, timezone

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from polygon_websocket_service import PolygonWebSocketClient
from realtime_candle_broadcaster import CandleBroadcaster

def test_polygon_websocket():
    """Test the Polygon WebSocket client."""
    print("🔄 Testing Polygon WebSocket Client...")
    
    # Check if API key is available
    api_key = os.getenv("POLYGON_API_KEY")
    if not api_key:
        print("❌ POLYGON_API_KEY environment variable not set")
        return False
    
    print(f"✅ API key found: {api_key[:10]}...")
    
    # Create a callback to handle messages
    received_messages = []
    
    def message_callback(candle_data):
        print(f"📊 Received candle data: {json.dumps(candle_data, indent=2)}")
        received_messages.append(candle_data)
    
    # Create and connect the client
    client = PolygonWebSocketClient(api_key, message_callback)
    
    try:
        print("🔌 Connecting to Polygon WebSocket...")
        client.connect()
        
        # Wait for connection
        time.sleep(3)
        
        if not client.is_connected:
            print("❌ Failed to connect to Polygon WebSocket")
            return False
        
        print("✅ Connected to Polygon WebSocket")
        
        # Subscribe to EURUSD
        print("📡 Subscribing to EURUSD...")
        client.subscribe_to_forex_pair("EURUSD", "1m")
        
        # Wait for some data
        print("⏳ Waiting for real-time data (30 seconds)...")
        time.sleep(30)
        
        # Check connection status
        status = client.get_connection_status()
        print(f"📊 Connection status: {json.dumps(status, indent=2)}")
        
        # Disconnect
        print("🔌 Disconnecting...")
        client.disconnect()
        
        print(f"✅ Test completed. Received {len(received_messages)} messages")
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

async def test_candle_broadcaster():
    """Test the candle broadcaster service."""
    print("🔄 Testing Candle Broadcaster...")
    
    try:
        # Create broadcaster
        broadcaster = CandleBroadcaster("localhost", 8766)  # Use different port for testing
        
        print("🚀 Starting broadcaster server...")
        
        # Start server in background
        server_task = asyncio.create_task(broadcaster.start_server())
        
        # Wait a bit for server to start
        await asyncio.sleep(2)
        
        # Check status
        status = broadcaster.get_status()
        print(f"📊 Broadcaster status: {json.dumps(status, indent=2)}")
        
        # Stop server
        await broadcaster.stop_server()
        
        print("✅ Broadcaster test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error during broadcaster test: {e}")
        return False

def test_environment():
    """Test environment setup."""
    print("🔄 Testing Environment Setup...")
    
    # Check required environment variables
    required_vars = ["POLYGON_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value[:10]}..." if len(value) > 10 else f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: Not set")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment setup is correct")
    return True

def main():
    """Run all tests."""
    print("🧪 Starting WebSocket Service Tests")
    print("=" * 50)
    
    # Test environment
    if not test_environment():
        print("❌ Environment test failed")
        return
    
    print("\n" + "=" * 50)
    
    # Test Polygon WebSocket (only if we have API key)
    if os.getenv("POLYGON_API_KEY"):
        if test_polygon_websocket():
            print("✅ Polygon WebSocket test passed")
        else:
            print("❌ Polygon WebSocket test failed")
    else:
        print("⚠️ Skipping Polygon WebSocket test (no API key)")
    
    print("\n" + "=" * 50)
    
    # Test Candle Broadcaster
    try:
        if asyncio.run(test_candle_broadcaster()):
            print("✅ Candle Broadcaster test passed")
        else:
            print("❌ Candle Broadcaster test failed")
    except Exception as e:
        print(f"❌ Candle Broadcaster test failed: {e}")
    
    print("\n" + "=" * 50)
    print("🧪 All tests completed")

if __name__ == "__main__":
    main()
