import numpy as np
from typing import List, Op<PERSON>, Tuple, Union

def calculate_returns(prices: np.ndarray) -> np.ndarray:
    """Calculate percentage returns from price series."""
    return np.diff(prices) / prices[:-1]

def calculate_sharpe_ratio(returns: np.ndarray, 
                         risk_free_rate: float = 0.0,
                         periods_per_year: int = 252) -> float:
    """
    Calculate the Sharpe ratio of a returns series.
    
    Args:
        returns: Array of returns
        risk_free_rate: Annual risk-free rate (default: 0)
        periods_per_year: Number of periods in a year (default: 252 for daily data)
    """
    if len(returns) < 2:
        return 0.0
        
    # Convert annual risk-free rate to per-period rate
    rf_per_period = (1 + risk_free_rate) ** (1/periods_per_year) - 1
    
    excess_returns = returns - rf_per_period
    if np.std(excess_returns) == 0:
        return 0.0
        
    return (np.mean(excess_returns) / np.std(excess_returns) * 
            np.sqrt(periods_per_year))

def calculate_drawdown_series(equity_curve: np.ndarray) -> Tuple[np.ndarray, float, int]:
    """
    Calculate a drawdown series and maximum drawdown from an equity curve.
    
    Returns:
        Tuple containing:
        - Drawdown series
        - Maximum drawdown value
        - Maximum drawdown duration in periods
    """
    rolling_max = np.maximum.accumulate(equity_curve)
    drawdown = equity_curve - rolling_max
    drawdown_pct = drawdown / rolling_max
    
    # Find maximum drawdown and its duration
    max_drawdown = np.min(drawdown_pct)
    
    # Calculate drawdown duration
    is_in_drawdown = drawdown_pct < 0
    if not np.any(is_in_drawdown):
        return drawdown_pct, 0.0, 0
        
    # Find the longest sequence of negative values
    sequences = np.diff(np.where(np.concatenate(([0], is_in_drawdown, [0])))[0])
    max_duration = max(sequences[::2]) if len(sequences) > 0 else 0
    
    return drawdown_pct, abs(max_drawdown), max_duration

def calculate_cagr(equity_curve: np.ndarray, 
                  periods_per_year: int = 252) -> float:
    """
    Calculate Compound Annual Growth Rate.
    
    Args:
        equity_curve: Array of cumulative equity values
        periods_per_year: Number of periods in a year (default: 252 for daily data)
    """
    if len(equity_curve) < 2:
        return 0.0
        
    start_value = equity_curve[0]
    end_value = equity_curve[-1]
    n_periods = len(equity_curve)
    
    if start_value <= 0 or end_value <= 0:
        return 0.0
        
    return ((end_value / start_value) ** 
            (periods_per_year / n_periods) - 1)

def calculate_sortino_ratio(returns: np.ndarray,
                          risk_free_rate: float = 0.0,
                          periods_per_year: int = 252) -> float:
    """
    Calculate the Sortino ratio of a returns series.
    
    Args:
        returns: Array of returns
        risk_free_rate: Annual risk-free rate (default: 0)
        periods_per_year: Number of periods in a year (default: 252 for daily data)
    """
    if len(returns) < 2:
        return 0.0
        
    # Convert annual risk-free rate to per-period rate
    rf_per_period = (1 + risk_free_rate) ** (1/periods_per_year) - 1
    
    excess_returns = returns - rf_per_period
    
    # Calculate downside deviation (denominator)
    negative_returns = returns[returns < 0]
    if len(negative_returns) == 0:
        return np.inf if np.mean(returns) > 0 else 0.0
        
    downside_std = np.sqrt(np.mean(negative_returns**2))
    if downside_std == 0:
        return 0.0
        
    return (np.mean(returns) / downside_std * 
            np.sqrt(periods_per_year))

def calculate_calmar_ratio(returns: np.ndarray,
                         periods_per_year: int = 252) -> float:
    """
    Calculate the Calmar ratio (CAGR / Maximum Drawdown).
    
    Args:
        returns: Array of returns
        periods_per_year: Number of periods in a year (default: 252 for daily data)
    """
    if len(returns) < 2:
        return 0.0
        
    # Calculate equity curve from returns
    equity_curve = np.cumprod(1 + returns)
    
    # Calculate CAGR
    cagr = calculate_cagr(equity_curve, periods_per_year)
    
    # Calculate maximum drawdown
    _, max_drawdown, _ = calculate_drawdown_series(equity_curve)
    
    if max_drawdown == 0:
        return np.inf if cagr > 0 else 0.0
        
    return cagr / max_drawdown 