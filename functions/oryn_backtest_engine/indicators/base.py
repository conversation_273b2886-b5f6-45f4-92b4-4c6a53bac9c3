from abc import ABC, abstractmethod
import numpy as np
from typing import List, Union, Optional

class Indicator(ABC):
    """Base class for all indicators in the OrynBacktestEngine."""
    
    def __init__(self, input_data: np.ndarray):
        """
        Initialize the indicator with input data.
        
        Args:
            input_data (np.ndarray): Input price/volume data to calculate indicator
        """
        self.input_data = input_data
        self.output_data = None
        self._calculate()
    
    @abstractmethod
    def _calculate(self) -> None:
        """Calculate the indicator values. Must be implemented by child classes."""
        pass
    
    def get_value(self, index: int = -1) -> Optional[float]:
        """
        Get indicator value at specific index.
        
        Args:
            index (int): Index to get value from. Default is -1 (latest value)
            
        Returns:
            float: Indicator value at specified index or None if invalid
        """
        if self.output_data is None or index >= len(self.output_data):
            return None
        return float(self.output_data[index])
    
    def get_series(self) -> np.ndarray:
        """Get the full series of indicator values."""
        return self.output_data 