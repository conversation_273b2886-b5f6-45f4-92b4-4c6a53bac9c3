"""Support and Resistance indicators for the Oryn Backtest Engine."""

import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from .base import Indicator

class SupportResistance(Indicator):
    """Support and Resistance levels indicator."""

    def __init__(self, price_data: np.ndarray, left_bars: int = 10, right_bars: int = 10):
        """
        Initialize Support and Resistance indicator.

        Args:
            price_data: OHLC price data as numpy array with shape (n, 4)
                        where columns are [open, high, low, close]
            left_bars: Number of bars to look back for pivot points (default: 10)
            right_bars: Number of bars to look ahead for pivot points (default: 10)
        """
        self.left_bars = int(left_bars)  # Ensure left_bars is an integer
        self.right_bars = int(right_bars)  # Ensure right_bars is an integer
        self.price_data = price_data

        # Store support and resistance levels
        self.support_levels = []
        self.resistance_levels = []

        super().__init__(price_data[:, 3])  # Initialize with close prices

    def _calculate(self) -> None:
        """Calculate Support and Resistance levels."""
        # Extract price data
        high = self.price_data[:, 1]
        low = self.price_data[:, 2]

        # Focus more on recent price action - give higher weight to recent swings
        # We'll use a weighted approach where more recent swings get higher priority
        recentDataThreshold = int(len(high) * 0.7)  # Consider the most recent 70% of data as "recent"

        # Temporary list to store all levels before grouping
        levels = []

        # Find pivot highs (resistance)
        for i in range(self.left_bars, len(high) - self.right_bars):
            # Check if current high is higher than all bars to the left
            is_pivot_high = True
            for j in range(i - self.left_bars, i):
                if high[j] >= high[i]:
                    is_pivot_high = False
                    break

            # If still a potential pivot high, check right side
            if is_pivot_high:
                for j in range(i + 1, i + self.right_bars + 1):
                    if j < len(high) and high[j] >= high[i]:
                        is_pivot_high = False
                        break

            # Calculate a recency factor - more recent swings get higher weight
            recencyFactor = 2 if i >= recentDataThreshold else 1

            # Add to levels if it's a pivot high
            if is_pivot_high:
                levels.append({
                    'price': high[i],
                    'type': 'resistance',
                    'index': i,
                    'strength': recencyFactor,
                    'isRecent': i >= recentDataThreshold
                })

        # Find pivot lows (support)
        for i in range(self.left_bars, len(low) - self.right_bars):
            # Check if current low is lower than all bars to the left
            is_pivot_low = True
            for j in range(i - self.left_bars, i):
                if low[j] <= low[i]:
                    is_pivot_low = False
                    break

            # If still a potential pivot low, check right side
            if is_pivot_low:
                for j in range(i + 1, i + self.right_bars + 1):
                    if j < len(low) and low[j] <= low[i]:
                        is_pivot_low = False
                        break

            # Calculate a recency factor - more recent swings get higher weight
            recencyFactor = 2 if i >= recentDataThreshold else 1

            # Add to levels if it's a pivot low
            if is_pivot_low:
                levels.append({
                    'price': low[i],
                    'type': 'support',
                    'index': i,
                    'strength': recencyFactor,
                    'isRecent': i >= recentDataThreshold
                })

        # Group very similar levels that are extremely close to each other
        # This helps avoid having multiple lines at virtually the same price
        groupedLevels = []
        tolerance = 0.0005  # Very small tolerance (0.05%) to only group extremely similar levels

        # Group similar levels
        for level in levels:
            similarLevelIndex = -1
            for i, existingLevel in enumerate(groupedLevels):
                if (existingLevel['type'] == level['type'] and
                    abs(existingLevel['price'] - level['price']) / level['price'] < tolerance):
                    similarLevelIndex = i
                    break

            if similarLevelIndex >= 0:
                # If we found a very similar level, keep the more recent one
                if level['index'] > groupedLevels[similarLevelIndex]['index']:
                    groupedLevels[similarLevelIndex] = level.copy()
            else:
                # If no similar level found, add this one
                groupedLevels.append(level.copy())

        # Sort levels by recency (most recent first)
        sortedLevels = sorted(groupedLevels, key=lambda x: x['index'], reverse=True)

        # Separate into support and resistance levels
        self.support_levels = [level for level in sortedLevels if level['type'] == 'support']
        self.resistance_levels = [level for level in sortedLevels if level['type'] == 'resistance']

        # Create output data (not really used, but required by base class)
        self.output_data = np.zeros_like(self.input_data)

    def get_nearest_levels(self, index: int, price: float) -> Tuple[Optional[float], Optional[float]]:
        """
        Get nearest support and resistance levels.

        Args:
            index: Current index in the price data
            price: Current price

        Returns:
            tuple: (nearest_support, nearest_resistance)
        """
        # Filter levels that are before the current index
        valid_supports = [level for level in self.support_levels if level['index'] < index]
        valid_resistances = [level for level in self.resistance_levels if level['index'] < index]

        # Find nearest support (below current price)
        nearest_support = None
        nearest_support_diff = float('inf')
        nearest_support_strength = 0

        for level in valid_supports:
            if level['price'] < price:
                diff = price - level['price']
                # Prioritize more recent and stronger levels
                # Adjust the diff by the strength and recency
                adjusted_diff = diff / (level['strength'] * (2 if level.get('isRecent', False) else 1))

                if adjusted_diff < nearest_support_diff:
                    nearest_support_diff = adjusted_diff
                    nearest_support = level['price']
                    nearest_support_strength = level['strength']

        # Find nearest resistance (above current price)
        nearest_resistance = None
        nearest_resistance_diff = float('inf')
        nearest_resistance_strength = 0

        for level in valid_resistances:
            if level['price'] > price:
                diff = level['price'] - price
                # Prioritize more recent and stronger levels
                # Adjust the diff by the strength and recency
                adjusted_diff = diff / (level['strength'] * (2 if level.get('isRecent', False) else 1))

                if adjusted_diff < nearest_resistance_diff:
                    nearest_resistance_diff = adjusted_diff
                    nearest_resistance = level['price']
                    nearest_resistance_strength = level['strength']

        # Log the levels found for debugging
        print(f"Found nearest support at {nearest_support} (diff: {nearest_support_diff}, strength: {nearest_support_strength})")
        print(f"Found nearest resistance at {nearest_resistance} (diff: {nearest_resistance_diff}, strength: {nearest_resistance_strength})")

        return nearest_support, nearest_resistance

    def get_stop_loss(self, index: int, entry_price: float, is_long: bool) -> float:
        """
        Calculate stop loss price based on Support and Resistance levels.

        Args:
            index: Index in the price data
            entry_price: Entry price of the trade
            is_long: True for long trades, False for short trades

        Returns:
            float: Stop loss price
        """
        nearest_support, nearest_resistance = self.get_nearest_levels(index, entry_price)

        # Calculate the default percentage-based stop loss as a fallback
        default_stop_pct = 0.02  # 2% default stop loss
        default_stop_long = entry_price * (1 - default_stop_pct)
        default_stop_short = entry_price * (1 + default_stop_pct)

        if is_long:
            # For long trades, use nearest support as stop loss
            if nearest_support is not None:
                # Calculate the percentage difference between entry and support
                pct_diff = (entry_price - nearest_support) / entry_price

                # If the support level is too far away (more than 5%), use a closer stop loss
                if pct_diff > 0.05:
                    print(f"Support level at {nearest_support} is too far ({pct_diff*100:.2f}%) from entry price {entry_price}. Using default 2% stop loss.")
                    return default_stop_long

                # Always use the support level as stop loss regardless of how close it is
                # as long as it's below the entry price (which is guaranteed by our get_nearest_levels logic)
                print(f"Using support level at {nearest_support} as stop loss for long trade (entry: {entry_price}, distance: {pct_diff*100:.2f}%)")
                return nearest_support
            else:
                # Default to 2% below entry price if no support found
                print(f"No support level found. Using default 2% stop loss at {default_stop_long}")
                return default_stop_long
        else:
            # For short trades, use nearest resistance as stop loss
            if nearest_resistance is not None:
                # Calculate the percentage difference between resistance and entry
                pct_diff = (nearest_resistance - entry_price) / entry_price

                # If the resistance level is too far away (more than 5%), use a closer stop loss
                if pct_diff > 0.05:
                    print(f"Resistance level at {nearest_resistance} is too far ({pct_diff*100:.2f}%) from entry price {entry_price}. Using default 2% stop loss.")
                    return default_stop_short

                # Always use the resistance level as stop loss regardless of how close it is
                # as long as it's above the entry price (which is guaranteed by our get_nearest_levels logic)
                print(f"Using resistance level at {nearest_resistance} as stop loss for short trade (entry: {entry_price}, distance: {pct_diff*100:.2f}%)")
                return nearest_resistance
            else:
                # Default to 2% above entry price if no resistance found
                print(f"No resistance level found. Using default 2% stop loss at {default_stop_short}")
                return default_stop_short

    def get_value(self, index: int) -> Any:
        """
        Get the indicator value at a specific index.

        For Support & Resistance, this returns a dictionary with the nearest support and resistance levels.

        Args:
            index: Index in the price data

        Returns:
            dict: Dictionary with nearest support and resistance levels
        """
        if index < 0 or index >= len(self.input_data):
            return None

        # Get the current price
        current_price = self.input_data[index]

        # Get the nearest support and resistance levels
        nearest_support, nearest_resistance = self.get_nearest_levels(index, current_price)

        return {
            'support': nearest_support,
            'resistance': nearest_resistance,
            'current_price': current_price
        }
