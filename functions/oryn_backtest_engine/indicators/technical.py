import numpy as np
from typing import Optional
from .base import Indicator

class SMA(Indicator):
    """Simple Moving Average indicator."""

    def __init__(self, input_data: np.ndarray, period: int = 14):
        self.period = int(period)  # Ensure period is an integer
        super().__init__(input_data)

    def _calculate(self) -> None:
        """Calculate Simple Moving Average."""
        self.output_data = np.convolve(self.input_data,
                                     np.ones(self.period)/self.period,
                                     mode='valid')
        # Pad the beginning with NaN to maintain array length
        pad_width = len(self.input_data) - len(self.output_data)
        self.output_data = np.pad(self.output_data, (pad_width, 0),
                                 constant_values=np.nan)

class EMA(Indicator):
    """Exponential Moving Average indicator."""

    def __init__(self, input_data: np.ndarray, period: int = 14):
        self.period = int(period)  # Ensure period is an integer
        super().__init__(input_data)

    def _calculate(self) -> None:
        """Calculate Exponential Moving Average."""
        alpha = 2 / (self.period + 1)
        self.output_data = np.zeros_like(self.input_data)
        self.output_data[0] = self.input_data[0]

        for i in range(1, len(self.input_data)):
            self.output_data[i] = (alpha * self.input_data[i] +
                                 (1 - alpha) * self.output_data[i-1])

class RSI(Indicator):
    """Relative Strength Index indicator."""

    def __init__(self, input_data: np.ndarray, period: int = 14):
        self.period = period
        super().__init__(input_data)

    def _calculate(self) -> None:
        """Calculate RSI."""
        # Calculate price changes
        delta = np.diff(self.input_data)
        delta = np.append(delta, 0)  # Add 0 to maintain array length

        # Separate gains and losses
        gains = np.where(delta > 0, delta, 0)
        losses = np.where(delta < 0, -delta, 0)

        # Calculate average gains and losses
        avg_gains = np.zeros_like(self.input_data)
        avg_losses = np.zeros_like(self.input_data)

        # First average
        avg_gains[self.period] = np.mean(gains[:self.period])
        avg_losses[self.period] = np.mean(losses[:self.period])

        # Calculate subsequent values
        for i in range(self.period + 1, len(self.input_data)):
            avg_gains[i] = ((avg_gains[i-1] * (self.period - 1) +
                           gains[i]) / self.period)
            avg_losses[i] = ((avg_losses[i-1] * (self.period - 1) +
                            losses[i]) / self.period)

        # Calculate RS and RSI
        rs = avg_gains / np.where(avg_losses == 0, 1e-10, avg_losses)
        self.output_data = 100 - (100 / (1 + rs))
        self.output_data[:self.period] = np.nan

class MACD(Indicator):
    """Moving Average Convergence Divergence indicator."""

    def __init__(self, input_data: np.ndarray,
                 fast_period: int = 12,
                 slow_period: int = 26,
                 signal_period: int = 9):
        self.fast_period = int(fast_period)  # Ensure fast_period is an integer
        self.slow_period = int(slow_period)  # Ensure slow_period is an integer
        self.signal_period = int(signal_period)  # Ensure signal_period is an integer
        super().__init__(input_data)

    def _calculate(self) -> None:
        """Calculate MACD line and signal line."""
        # Calculate fast and slow EMAs
        fast_ema = EMA(self.input_data, self.fast_period).get_series()
        slow_ema = EMA(self.input_data, self.slow_period).get_series()

        # Calculate MACD line
        macd_line = fast_ema - slow_ema

        # Calculate signal line
        signal_line = EMA(macd_line, self.signal_period).get_series()

        # Store both MACD and signal line
        self.macd_line = macd_line
        self.signal_line = signal_line
        self.output_data = macd_line  # Main output is MACD line

    def get_signal_line(self, index: int = -1) -> Optional[float]:
        """Get signal line value at specific index."""
        if index >= len(self.signal_line):
            return None
        return float(self.signal_line[index])

    def get_histogram(self, index: int = -1) -> Optional[float]:
        """Get MACD histogram value at specific index."""
        if index >= len(self.macd_line):
            return None
        return float(self.macd_line[index] - self.signal_line[index])