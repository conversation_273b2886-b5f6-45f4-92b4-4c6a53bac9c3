"""Volatility indicators for the Oryn Backtest Engine."""

import numpy as np
from typing import Optional, Tuple
from .base import Indicator

class ATR(Indicator):
    """Average True Range indicator."""

    def __init__(self, price_data: np.ndarray, period: int = 14, multiplier: float = 1.0):
        """
        Initialize ATR indicator.

        Args:
            price_data: OHLC price data as numpy array with shape (n, 4)
                        where columns are [open, high, low, close]
            period: ATR period (default: 14)
            multiplier: Multiplier to apply to ATR values (default: 1.0)
        """
        self.period = int(period)  # Ensure period is an integer
        self.multiplier = float(multiplier)  # Ensure multiplier is a float
        self.price_data = price_data
        super().__init__(price_data[:, 3])  # Initialize with close prices

    def _calculate(self) -> None:
        """Calculate Average True Range."""
        # Extract price data
        high = self.price_data[:, 1]
        low = self.price_data[:, 2]
        close = self.price_data[:, 3]

        # Calculate true range
        tr = np.zeros_like(close)

        # First value is high - low
        tr[0] = high[0] - low[0]

        # Calculate subsequent true ranges
        for i in range(1, len(close)):
            # True Range = max(high - low, |high - prev_close|, |low - prev_close|)
            tr[i] = max(
                high[i] - low[i],
                abs(high[i] - close[i-1]),
                abs(low[i] - close[i-1])
            )

        # Calculate ATR using Wilder's smoothing method
        atr = np.zeros_like(close)
        atr[0] = tr[0]

        for i in range(1, len(close)):
            if i < self.period:
                # Simple average for initial values
                atr[i] = np.mean(tr[:i+1])
            else:
                # Wilder's smoothing
                atr[i] = (atr[i-1] * (self.period - 1) + tr[i]) / self.period

        # Apply multiplier
        self.output_data = atr * self.multiplier

    def get_stop_loss(self, index: int, entry_price: float, is_long: bool) -> float:
        """
        Calculate stop loss price based on ATR.

        Args:
            index: Index in the price data
            entry_price: Entry price of the trade
            is_long: True for long trades, False for short trades

        Returns:
            float: Stop loss price
        """
        atr_value = self.get_value(index)
        if atr_value is None:
            # Default to 2% of entry price if ATR is not available
            atr_value = entry_price * 0.02

        if is_long:
            # For long trades, stop loss is below entry price
            return entry_price - atr_value
        else:
            # For short trades, stop loss is above entry price
            return entry_price + atr_value

class BollingerBands(Indicator):
    """Bollinger Bands indicator."""

    def __init__(self, input_data: np.ndarray, period: int = 20, std_dev: float = 2.0):
        """
        Initialize Bollinger Bands indicator.

        Args:
            input_data: Price data as numpy array
            period: Bollinger Bands period (default: 20)
            std_dev: Standard deviation multiplier (default: 2.0)
        """
        self.period = int(period)  # Ensure period is an integer
        self.std_dev = float(std_dev)  # Ensure std_dev is a float
        super().__init__(input_data)

    def _calculate(self) -> None:
        """Calculate Bollinger Bands."""
        # Calculate middle band (SMA)
        middle_band = np.zeros_like(self.input_data)

        for i in range(len(self.input_data)):
            if i < self.period - 1:
                middle_band[i] = np.nan
            else:
                middle_band[i] = np.mean(self.input_data[i-self.period+1:i+1])

        # Calculate standard deviation
        std = np.zeros_like(self.input_data)

        for i in range(len(self.input_data)):
            if i < self.period - 1:
                std[i] = np.nan
            else:
                std[i] = np.std(self.input_data[i-self.period+1:i+1])

        # Calculate upper and lower bands
        upper_band = middle_band + (std * self.std_dev)
        lower_band = middle_band - (std * self.std_dev)

        # Store all bands
        self.middle_band = middle_band
        self.upper_band = upper_band
        self.lower_band = lower_band

        # Main output is middle band
        self.output_data = middle_band

    def get_bands(self, index: int = -1) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """
        Get Bollinger Bands values at specific index.

        Args:
            index: Index to get values from. Default is -1 (latest value)

        Returns:
            tuple: (lower_band, middle_band, upper_band) at specified index
        """
        # Check if index is valid
        if index < 0 or index >= len(self.middle_band):
            print(f"WARNING: Invalid index {index} for Bollinger Bands (length: {len(self.middle_band)})")
            return None, None, None

        # Check if we have enough data (warm-up period)
        if index < self.period - 1:
            print(f"WARNING: Not enough data for Bollinger Bands at index {index} (need at least {self.period} data points)")
            return None, None, None

        # Check for NaN values
        if np.isnan(self.middle_band[index]) or np.isnan(self.upper_band[index]) or np.isnan(self.lower_band[index]):
            print(f"WARNING: NaN values in Bollinger Bands at index {index}")
            return None, None, None

        # Return the bands as floats
        try:
            return (
                float(self.lower_band[index]),
                float(self.middle_band[index]),
                float(self.upper_band[index])
            )
        except Exception as e:
            print(f"ERROR in get_bands: {e}")
            return None, None, None

    def get_stop_loss(self, index: int, entry_price: float, is_long: bool) -> float:
        """
        Calculate stop loss price based on Bollinger Bands.

        Args:
            index: Index in the price data
            entry_price: Entry price of the trade
            is_long: True for long trades, False for short trades

        Returns:
            float: Stop loss price
        """
        lower, middle, upper = self.get_bands(index)

        if lower is None or upper is None:
            # Default to 2% of entry price if bands are not available
            return entry_price * (0.98 if is_long else 1.02)

        if is_long:
            # For long trades, use lower band as stop loss
            return lower
        else:
            # For short trades, use upper band as stop loss
            return upper
