"""
Production state manager for WebSocket service.
Handles persistent state storage in Firestore for production deployment.
"""

import os
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Set
from google.cloud import firestore
from google.api_core import exceptions

logger = logging.getLogger(__name__)

class ProductionStateManager:
    """
    Manages WebSocket service state in Firestore for production deployment.
    
    This handles:
    - User subscription persistence
    - Polygon subscription coordination across instances
    - Instance health tracking
    - Automatic cleanup of stale connections
    """
    
    def __init__(self):
        """Initialize the state manager with Firestore client."""
        try:
            self.db = firestore.Client()
            self.instance_id = self._get_instance_id()
            logger.info(f"ProductionStateManager initialized for instance: {self.instance_id}")
        except Exception as e:
            logger.error(f"Failed to initialize Firestore client: {e}")
            raise
    
    def _get_instance_id(self) -> str:
        """Get unique instance identifier for this Cloud Run instance."""
        # Cloud Run provides K_SERVICE and K_REVISION environment variables
        service = os.environ.get('K_SERVICE', 'unknown-service')
        revision = os.environ.get('K_REVISION', 'unknown-revision')
        return f"{service}-{revision}"
    
    async def add_user_subscription(self, user_id: str, session_id: str, 
                                  forex_pair: str, timeframe: str = "1m") -> bool:
        """
        Add a user subscription and return whether we need to subscribe to Polygon.
        
        Args:
            user_id: Unique user identifier
            session_id: WebSocket session identifier
            forex_pair: Forex pair (e.g., "EURUSD")
            timeframe: Timeframe (e.g., "1m", "5m")
            
        Returns:
            bool: True if this instance should subscribe to Polygon, False if already subscribed
        """
        try:
            subscription_id = f"{user_id}_{session_id}"
            
            # Save user subscription
            user_sub_data = {
                'user_id': user_id,
                'session_id': session_id,
                'forex_pair': forex_pair,
                'timeframe': timeframe,
                'instance_id': self.instance_id,
                'connected_at': datetime.now(timezone.utc),
                'last_seen': datetime.now(timezone.utc)
            }
            
            await self._set_document('websocket_subscriptions', subscription_id, user_sub_data)
            logger.info(f"Added user subscription: {subscription_id} -> {forex_pair}_{timeframe}")
            
            # Check and manage Polygon subscription
            needs_polygon_sub = await self._manage_polygon_subscription(forex_pair, timeframe, 'add')
            
            return needs_polygon_sub
            
        except Exception as e:
            logger.error(f"Error adding user subscription: {e}")
            return False
    
    async def remove_user_subscription(self, user_id: str, session_id: str) -> Optional[Dict[str, str]]:
        """
        Remove a user subscription and return subscription info if we should unsubscribe from Polygon.
        
        Args:
            user_id: Unique user identifier
            session_id: WebSocket session identifier
            
        Returns:
            Dict with forex_pair and timeframe if should unsubscribe from Polygon, None otherwise
        """
        try:
            subscription_id = f"{user_id}_{session_id}"
            
            # Get subscription data before deleting
            doc = await self._get_document('websocket_subscriptions', subscription_id)
            if not doc:
                logger.warning(f"Subscription not found for removal: {subscription_id}")
                return None
            
            forex_pair = doc.get('forex_pair')
            timeframe = doc.get('timeframe')
            
            # Remove user subscription
            await self._delete_document('websocket_subscriptions', subscription_id)
            logger.info(f"Removed user subscription: {subscription_id}")
            
            # Check if we should unsubscribe from Polygon
            should_unsubscribe = await self._manage_polygon_subscription(forex_pair, timeframe, 'remove')
            
            if should_unsubscribe:
                return {'forex_pair': forex_pair, 'timeframe': timeframe}
            
            return None
            
        except Exception as e:
            logger.error(f"Error removing user subscription: {e}")
            return None
    
    async def get_users_subscribed_to(self, forex_pair: str, timeframe: str) -> List[Dict]:
        """
        Get all users subscribed to a specific forex pair and timeframe.
        
        Args:
            forex_pair: Forex pair (e.g., "EURUSD")
            timeframe: Timeframe (e.g., "1m")
            
        Returns:
            List of user subscription data
        """
        try:
            collection_ref = self.db.collection('websocket_subscriptions')
            query = collection_ref.where('forex_pair', '==', forex_pair).where('timeframe', '==', timeframe)
            
            docs = query.stream()
            users = []
            
            for doc in docs:
                data = doc.to_dict()
                # Update last_seen timestamp
                await self._update_document('websocket_subscriptions', doc.id, {
                    'last_seen': datetime.now(timezone.utc)
                })
                users.append(data)
            
            return users
            
        except Exception as e:
            logger.error(f"Error getting subscribed users: {e}")
            return []
    
    async def restore_instance_subscriptions(self) -> Dict[str, List[Dict]]:
        """
        Restore subscriptions for this instance (used on startup).
        
        Returns:
            Dict mapping forex_pair_timeframe to list of user subscriptions
        """
        try:
            collection_ref = self.db.collection('websocket_subscriptions')
            query = collection_ref.where('instance_id', '==', self.instance_id)
            
            docs = query.stream()
            subscriptions = {}
            
            for doc in docs:
                data = doc.to_dict()
                key = f"{data['forex_pair']}_{data['timeframe']}"
                
                if key not in subscriptions:
                    subscriptions[key] = []
                
                subscriptions[key].append(data)
            
            logger.info(f"Restored {len(subscriptions)} subscription groups for instance {self.instance_id}")
            return subscriptions
            
        except Exception as e:
            logger.error(f"Error restoring instance subscriptions: {e}")
            return {}
    
    async def cleanup_stale_connections(self, max_age_minutes: int = 30):
        """
        Clean up stale connections that haven't been seen recently.
        
        Args:
            max_age_minutes: Maximum age in minutes before considering connection stale
        """
        try:
            cutoff_time = datetime.now(timezone.utc).timestamp() - (max_age_minutes * 60)
            
            collection_ref = self.db.collection('websocket_subscriptions')
            docs = collection_ref.stream()
            
            stale_docs = []
            for doc in docs:
                data = doc.to_dict()
                last_seen = data.get('last_seen')
                
                if last_seen and last_seen.timestamp() < cutoff_time:
                    stale_docs.append((doc.id, data))
            
            # Remove stale connections
            for doc_id, data in stale_docs:
                await self._delete_document('websocket_subscriptions', doc_id)
                logger.info(f"Cleaned up stale connection: {doc_id}")
                
                # Check if we should unsubscribe from Polygon
                await self._manage_polygon_subscription(
                    data['forex_pair'], 
                    data['timeframe'], 
                    'remove'
                )
            
            logger.info(f"Cleaned up {len(stale_docs)} stale connections")
            
        except Exception as e:
            logger.error(f"Error cleaning up stale connections: {e}")
    
    async def _manage_polygon_subscription(self, forex_pair: str, timeframe: str, action: str) -> bool:
        """
        Manage Polygon subscription coordination across instances.
        
        Args:
            forex_pair: Forex pair
            timeframe: Timeframe
            action: 'add' or 'remove'
            
        Returns:
            bool: True if this instance should subscribe/unsubscribe to/from Polygon
        """
        try:
            polygon_sub_id = f"{forex_pair}_{timeframe}"
            
            if action == 'add':
                # Check if Polygon subscription exists
                doc = await self._get_document('polygon_subscriptions', polygon_sub_id)
                
                if not doc:
                    # First subscription - create and return True
                    polygon_data = {
                        'forex_pair': forex_pair,
                        'timeframe': timeframe,
                        'subscriber_count': 1,
                        'instances': [self.instance_id],
                        'created_at': datetime.now(timezone.utc),
                        'last_updated': datetime.now(timezone.utc)
                    }
                    await self._set_document('polygon_subscriptions', polygon_sub_id, polygon_data)
                    logger.info(f"Created new Polygon subscription: {polygon_sub_id}")
                    return True
                else:
                    # Subscription exists - increment count
                    current_count = doc.get('subscriber_count', 0)
                    current_instances = doc.get('instances', [])
                    
                    if self.instance_id not in current_instances:
                        current_instances.append(self.instance_id)
                    
                    await self._update_document('polygon_subscriptions', polygon_sub_id, {
                        'subscriber_count': current_count + 1,
                        'instances': current_instances,
                        'last_updated': datetime.now(timezone.utc)
                    })
                    logger.info(f"Incremented Polygon subscription: {polygon_sub_id} (count: {current_count + 1})")
                    return False
            
            elif action == 'remove':
                # Decrement subscription count
                doc = await self._get_document('polygon_subscriptions', polygon_sub_id)
                
                if doc:
                    current_count = doc.get('subscriber_count', 1)
                    current_instances = doc.get('instances', [])
                    
                    if current_count <= 1:
                        # Last subscriber - delete and return True
                        await self._delete_document('polygon_subscriptions', polygon_sub_id)
                        logger.info(f"Deleted Polygon subscription: {polygon_sub_id} (last subscriber)")
                        return True
                    else:
                        # Still have subscribers - decrement count
                        if self.instance_id in current_instances:
                            current_instances.remove(self.instance_id)
                        
                        await self._update_document('polygon_subscriptions', polygon_sub_id, {
                            'subscriber_count': current_count - 1,
                            'instances': current_instances,
                            'last_updated': datetime.now(timezone.utc)
                        })
                        logger.info(f"Decremented Polygon subscription: {polygon_sub_id} (count: {current_count - 1})")
                        return False
            
            return False
            
        except Exception as e:
            logger.error(f"Error managing Polygon subscription: {e}")
            return False
    
    # Helper methods for Firestore operations
    async def _get_document(self, collection: str, document_id: str) -> Optional[Dict]:
        """Get a document from Firestore."""
        try:
            doc_ref = self.db.collection(collection).document(document_id)
            doc = doc_ref.get()
            return doc.to_dict() if doc.exists else None
        except Exception as e:
            logger.error(f"Error getting document {collection}/{document_id}: {e}")
            return None
    
    async def _set_document(self, collection: str, document_id: str, data: Dict):
        """Set a document in Firestore."""
        try:
            doc_ref = self.db.collection(collection).document(document_id)
            doc_ref.set(data)
        except Exception as e:
            logger.error(f"Error setting document {collection}/{document_id}: {e}")
            raise
    
    async def _update_document(self, collection: str, document_id: str, data: Dict):
        """Update a document in Firestore."""
        try:
            doc_ref = self.db.collection(collection).document(document_id)
            doc_ref.update(data)
        except Exception as e:
            logger.error(f"Error updating document {collection}/{document_id}: {e}")
            raise
    
    async def _delete_document(self, collection: str, document_id: str):
        """Delete a document from Firestore."""
        try:
            doc_ref = self.db.collection(collection).document(document_id)
            doc_ref.delete()
        except Exception as e:
            logger.error(f"Error deleting document {collection}/{document_id}: {e}")
            raise
