"""
Real-time candle broadcaster service for streaming live forex data to frontend clients.
"""

import asyncio
import json
import logging
import os
import time
import websockets
from datetime import datetime, timezone
from typing import Dict, List, Set, Optional, Any
import threading
from collections import defaultdict
import traceback

from polygon_websocket_service import get_polygon_websocket_client
from production_state_manager import ProductionStateManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CandleBroadcaster:
    """
    WebSocket server that broadcasts real-time candle data to frontend clients.
    Manages client connections and subscriptions.
    """

    def __init__(self, host: str = "localhost", port: int = 8765, use_production_state: bool = False):
        """
        Initialize the candle broadcaster.

        Args:
            host: Host to bind the WebSocket server
            port: Port to bind the WebSocket server
            use_production_state: Whether to use Firestore for state management
        """
        self.host = host
        self.port = port
        self.use_production_state = use_production_state

        # Client management
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.client_info: Dict[websockets.WebSocketServerProtocol, Dict] = {}

        # Local caches (for performance)
        self.client_subscriptions: Dict[websockets.WebSocketServerProtocol, Set[str]] = defaultdict(set)
        self.subscription_counts: Dict[str, int] = defaultdict(int)

        # Production state manager
        self.state_manager = ProductionStateManager() if use_production_state else None

        # Polygon WebSocket client
        self.polygon_client = None
        self.server = None
        self.is_running = False

        # Client type detection for different data streams
        self.trade_bot_clients = set()  # Track trade-bot clients separately

        # Resource management
        self.max_connections = int(os.getenv("MAX_WEBSOCKET_CONNECTIONS", "1000"))
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = datetime.now(timezone.utc)

        logger.info(f"CandleBroadcaster initialized on {host}:{port} (production_state: {use_production_state})")

    async def start_server(self):
        """Start the WebSocket server."""
        try:
            # Store the main event loop for cross-thread communication
            self._main_loop = asyncio.get_event_loop()

            # Initialize Polygon WebSocket client
            self.polygon_client = get_polygon_websocket_client()
            self.polygon_client.on_message_callback = self._handle_polygon_message

            # Start Polygon WebSocket connection
            self.polygon_client.connect()

            # Start WebSocket server
            self.server = await websockets.serve(
                self._handle_client_connection,
                self.host,
                self.port
            )

            self.is_running = True
            logger.info(f"WebSocket server started on ws://{self.host}:{self.port}")

            # Keep the server running
            await self.server.wait_closed()

        except Exception as e:
            logger.error(f"Error starting server: {e}")
            logger.error(traceback.format_exc())

    async def stop_server(self):
        """Stop the WebSocket server."""
        self.is_running = False

        if self.polygon_client:
            self.polygon_client.disconnect()

        if self.server:
            self.server.close()
            await self.server.wait_closed()

        logger.info("WebSocket server stopped")

    async def _handle_client_connection(self, websocket, path=None):
        """Handle new client WebSocket connections."""
        client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logger.info(f"New client connected: {client_id}")

        # Check connection limits
        if len(self.clients) >= self.max_connections:
            logger.warning(f"Connection limit reached ({self.max_connections}), rejecting client {client_id}")
            await websocket.close(code=1013, reason="Server overloaded")
            return

        # Add client to the set
        self.clients.add(websocket)

        # Periodic cleanup
        await self._periodic_cleanup()

        # Generate session info
        import uuid
        session_id = str(uuid.uuid4())
        user_id = f"user_{client_id.replace(':', '_')}"  # Simple user ID for now

        # Store client info
        self.client_info[websocket] = {
            'user_id': user_id,
            'session_id': session_id,
            'client_id': client_id,
            'connected_at': datetime.now(timezone.utc)
        }

        try:
            # Send welcome message
            welcome_message = {
                "type": "connection",
                "status": "connected",
                "message": "Connected to Oryn real-time data stream",
                "session_id": session_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            await websocket.send(json.dumps(welcome_message))

            # Restore previous subscriptions if using production state
            if self.use_production_state and self.state_manager:
                await self._restore_client_subscriptions(websocket)

            # Listen for client messages
            async for message in websocket:
                await self._handle_client_message(websocket, message)

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client disconnected: {client_id}")
        except Exception as e:
            logger.error(f"Error handling client {client_id}: {e}")
        finally:
            # Clean up client
            await self._cleanup_client(websocket)

    async def _handle_client_message(self, websocket, message: str):
        """Handle messages from clients."""
        try:
            data = json.loads(message)
            message_type = data.get("type")

            if message_type == "subscribe":
                await self._handle_subscription(websocket, data)
            elif message_type == "unsubscribe":
                await self._handle_unsubscription(websocket, data)
            elif message_type == "ping":
                await self._handle_ping(websocket)
            else:
                logger.warning(f"Unknown message type: {message_type}")

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON from client: {e}")
        except Exception as e:
            logger.error(f"Error handling client message: {e}")

    async def _handle_subscription(self, websocket, data: Dict[str, Any]):
        """Handle client subscription requests."""
        try:
            logger.info(f"Handling subscription with data: {data} (type: {type(data)})")

            # Ensure data is a dictionary
            if isinstance(data, str):
                logger.error(f"Data is a string, not a dict: {data}")
                await self._send_error(websocket, "Invalid subscription data format")
                return

            forex_pair = data.get("forex_pair")
            timeframe = data.get("timeframe", "1m")
            client_type = data.get("client_type", "frontend")  # "frontend" or "trade_bot"

            if not forex_pair:
                await self._send_error(websocket, "Missing forex_pair in subscription")
                return

            # Get client info
            client_info = self.client_info.get(websocket)
            if not client_info:
                await self._send_error(websocket, "Client info not found")
                return

            # Track trade-bot clients separately
            if client_type == "trade_bot":
                self.trade_bot_clients.add(websocket)
                logger.info(f"🤖 Trade-bot client detected: {client_info['user_id']}")
                logger.info(f"📊 Total trade-bot clients: {len(self.trade_bot_clients)}")
                logger.info(f"🔍 Trade-bot websocket ID: {id(websocket)}")
            else:
                logger.info(f"🖥️ Frontend client detected: {client_info['user_id']}")
                logger.info(f"📊 Total frontend clients: {len(self.clients) - len(self.trade_bot_clients)}")
                logger.info(f"🔍 Frontend websocket ID: {id(websocket)}")

            # Format subscription key
            subscription_key = f"{forex_pair}_{timeframe}"

            # Add to local client subscriptions
            self.client_subscriptions[websocket].add(subscription_key)

            # Handle production state or local state
            needs_polygon_subscription = False

            if self.use_production_state and self.state_manager:
                # Use production state manager
                logger.info(f"Using production state manager for subscription")
                needs_polygon_subscription = await self.state_manager.add_user_subscription(
                    client_info['user_id'],
                    client_info['session_id'],
                    forex_pair,
                    timeframe
                )
            else:
                # Use local state (development mode)
                logger.info(f"Using local state for subscription")
                self.subscription_counts[subscription_key] += 1
                needs_polygon_subscription = self.subscription_counts[subscription_key] == 1

            # Check Polygon connection status
            polygon_status = self.polygon_client.get_connection_status() if self.polygon_client else None
            logger.info(f"🔍 Polygon connection status: {polygon_status}")

            # Subscribe to Polygon if needed OR if connection is broken
            if needs_polygon_subscription or (polygon_status and not polygon_status.get('is_connected', False)):
                if polygon_status and not polygon_status.get('is_connected', False):
                    logger.warning(f"🔄 Polygon connection broken, forcing re-subscription for {forex_pair} {timeframe}")
                    # Reconnect Polygon client
                    try:
                        self.polygon_client.connect()
                    except Exception as e:
                        logger.error(f"❌ Failed to reconnect Polygon client: {e}")

                self.polygon_client.subscribe_to_forex_pair(forex_pair, timeframe)

                # Also subscribe to some major pairs for testing data flow
                test_pairs = ["GBPUSD", "USDJPY", "USDCHF"]
                for test_pair in test_pairs:
                    if test_pair != forex_pair:
                        logger.info(f"🧪 Also subscribing to {test_pair} for data flow testing")
                        self.polygon_client.subscribe_to_forex_pair(test_pair, timeframe)

                logger.info(f"✅ Subscribed to Polygon for {forex_pair} {timeframe} (+ test pairs)")
            else:
                logger.info(f"⚡ Already subscribed to Polygon for {forex_pair} {timeframe}")

            # Send confirmation to client
            response = {
                "type": "subscription_confirmed",
                "forex_pair": forex_pair,
                "timeframe": timeframe,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            await websocket.send(json.dumps(response))

            logger.info(f"Client {client_info['user_id']} subscribed to {subscription_key}")

        except Exception as e:
            logger.error(f"Error handling subscription: {e}")
            logger.error(f"Exception details: {traceback.format_exc()}")
            await self._send_error(websocket, f"Subscription error: {str(e)}")

    async def _handle_unsubscription(self, websocket, data: Dict[str, Any]):
        """Handle client unsubscription requests."""
        try:
            forex_pair = data.get("forex_pair")
            timeframe = data.get("timeframe", "1m")

            if not forex_pair:
                await self._send_error(websocket, "Missing forex_pair in unsubscription")
                return

            subscription_key = f"{forex_pair}_{timeframe}"

            # Remove from client subscriptions
            if subscription_key in self.client_subscriptions[websocket]:
                self.client_subscriptions[websocket].remove(subscription_key)
                self.subscription_counts[subscription_key] -= 1

                # Only unsubscribe from Polygon if no more clients need this pair AND no trade-bots are connected to ANY timeframe of this forex pair
                if self.subscription_counts[subscription_key] <= 0:
                    # Check if any trade-bot clients are still subscribed to ANY timeframe of this forex pair
                    forex_pair_base = forex_pair  # e.g., "EURUSD"
                    trade_bot_still_subscribed_to_pair = any(
                        any(sub_key.startswith(forex_pair_base + "_") for sub_key in self.client_subscriptions.get(trade_bot_client, set()))
                        for trade_bot_client in self.trade_bot_clients
                    )

                    logger.info(f"🔍 Checking unsubscription for {forex_pair} {timeframe}")
                    logger.info(f"🔍 Trade-bot still subscribed to ANY timeframe of {forex_pair_base}: {trade_bot_still_subscribed_to_pair}")

                    if not trade_bot_still_subscribed_to_pair:
                        self.polygon_client.unsubscribe_from_forex_pair(forex_pair)
                        del self.subscription_counts[subscription_key]
                        logger.info(f"Unsubscribed from Polygon for {forex_pair} {timeframe} (no trade-bots connected to any timeframe)")
                    else:
                        logger.info(f"Keeping Polygon subscription for {forex_pair} {timeframe} (trade-bots still connected to {forex_pair_base})")
                        # Don't delete the subscription count, just set it to 0
                        # This prevents the subscription from being recreated unnecessarily

                # Send confirmation to client
                response = {
                    "type": "unsubscription_confirmed",
                    "forex_pair": forex_pair,
                    "timeframe": timeframe,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                await websocket.send(json.dumps(response))

                logger.info(f"Client unsubscribed from {subscription_key}")

        except Exception as e:
            logger.error(f"Error handling unsubscription: {e}")
            await self._send_error(websocket, f"Unsubscription error: {str(e)}")

    async def _handle_ping(self, websocket):
        """Handle ping messages from clients."""
        try:
            pong_message = {
                "type": "pong",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            await websocket.send(json.dumps(pong_message))
        except Exception as e:
            logger.error(f"Error sending pong: {e}")

    async def _send_error(self, websocket, error_message: str):
        """Send error message to client."""
        try:
            error_response = {
                "type": "error",
                "message": error_message,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            await websocket.send(json.dumps(error_response))
        except Exception as e:
            logger.error(f"Error sending error message: {e}")

    async def _cleanup_client(self, websocket):
        """Clean up client subscriptions when they disconnect."""
        try:
            # Check if this was a trade-bot client
            was_trade_bot = websocket in self.trade_bot_clients

            logger.info(f"🔍 Cleaning up client - websocket ID: {id(websocket)}")
            logger.info(f"🔍 Was trade-bot: {was_trade_bot}")
            logger.info(f"🔍 Before cleanup - Total clients: {len(self.clients)}, Trade-bots: {len(self.trade_bot_clients)}")

            # Remove client from sets
            self.clients.discard(websocket)
            self.trade_bot_clients.discard(websocket)

            logger.info(f"🔍 After cleanup - Total clients: {len(self.clients)}, Trade-bots: {len(self.trade_bot_clients)}")

            if was_trade_bot:
                logger.info(f"🤖 Trade-bot client disconnected. Remaining trade-bots: {len(self.trade_bot_clients)}")
            else:
                logger.info(f"🖥️ Frontend client disconnected. Remaining clients: {len(self.clients)}")

            # Clean up subscriptions
            if websocket in self.client_subscriptions:
                for subscription_key in self.client_subscriptions[websocket]:
                    self.subscription_counts[subscription_key] -= 1

                    # Only unsubscribe from Polygon if no more clients need this pair AND no trade-bots are connected to ANY timeframe of this forex pair
                    if self.subscription_counts[subscription_key] <= 0:
                        # Debug logging
                        logger.info(f"🔍 Checking if should unsubscribe from {subscription_key}")
                        logger.info(f"🔍 Current trade-bot clients: {len(self.trade_bot_clients)}")
                        logger.info(f"🔍 Trade-bot client subscriptions: {[list(self.client_subscriptions.get(tb, set())) for tb in self.trade_bot_clients]}")

                        # Check if any trade-bot clients are still subscribed to ANY timeframe of this forex pair
                        forex_pair = subscription_key.split("_")[0]  # e.g., "EURUSD"
                        trade_bot_still_subscribed_to_pair = any(
                            any(sub_key.startswith(forex_pair + "_") for sub_key in self.client_subscriptions.get(trade_bot_client, set()))
                            for trade_bot_client in self.trade_bot_clients
                        )

                        logger.info(f"🔍 Trade-bot still subscribed to ANY timeframe of {forex_pair}: {trade_bot_still_subscribed_to_pair}")

                        if not trade_bot_still_subscribed_to_pair:
                            self.polygon_client.unsubscribe_from_forex_pair(forex_pair)
                            del self.subscription_counts[subscription_key]
                            logger.info(f"Unsubscribed from Polygon for {forex_pair} during cleanup (no trade-bots connected to any timeframe)")
                        else:
                            logger.info(f"Keeping Polygon subscription for {forex_pair} during cleanup (trade-bots still connected to {forex_pair})")

                del self.client_subscriptions[websocket]

            logger.info("Client cleanup completed")

        except Exception as e:
            logger.error(f"Error during client cleanup: {e}")

    def _handle_polygon_message(self, data: Dict[str, Any]):
        """Handle incoming data from Polygon WebSocket (candles, ticks, quotes)."""
        try:
            # Extract forex pair and timeframe
            symbol = data.get("symbol", "")
            timeframe = data.get("timeframe", "5m")  # Default to 5m to match frontend

            # Normalize symbol format: convert "EUR/USD" to "EURUSD" to match frontend subscriptions
            normalized_symbol = symbol.replace("/", "") if symbol else ""
            subscription_key = f"{normalized_symbol}_{timeframe}"

            logger.info(f"📨 Processing Polygon message: symbol={symbol}, normalized={normalized_symbol}, timeframe={timeframe}, subscription_key={subscription_key}")
            logger.info(f"📊 Available subscriptions: {list(self.subscription_counts.keys())}")

            # Check if any clients are subscribed to this data
            if subscription_key not in self.subscription_counts or self.subscription_counts[subscription_key] <= 0:
                logger.info(f"⚠️ No subscribers for {subscription_key}")
                return

            # Determine message type based on data content
            logger.info(f"📨 Processing Polygon message for {symbol}: type={data.get('type', 'unknown')}, keys={list(data.keys())}")

            # Check if this is candle data (from CA subscription)
            if (data.get("type") == "candle" or
                ("open" in data and "high" in data and "low" in data and "close" in data)):

                # This is a 1m candle from Polygon - send to trade-bots only
                logger.info(f"📈 Received 1m candle: {normalized_symbol} OHLC={data.get('open')}/{data.get('high')}/{data.get('low')}/{data.get('close')}")

                # Create candle message for trade-bots
                client_message = {
                    "type": "candle_update",
                    "data": {
                        "time": data.get("time", data.get("timestamp", 0)),
                        "open": data.get("open", 0),
                        "high": data.get("high", 0),
                        "low": data.get("low", 0),
                        "close": data.get("close", 0),
                        "volume": data.get("volume", 0),
                        "symbol": normalized_symbol,
                        "timeframe": "1m"  # Always 1m from Polygon
                    },
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }

                # Broadcast 1m candles to ALL subscribed clients (trade-bots and frontend)
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        asyncio.create_task(self._broadcast_to_subscribed_clients(subscription_key, client_message))
                    else:
                        asyncio.ensure_future(self._broadcast_to_subscribed_clients(subscription_key, client_message))
                except RuntimeError:
                    try:
                        if hasattr(self, '_main_loop') and self._main_loop:
                            asyncio.run_coroutine_threadsafe(
                                self._broadcast_to_subscribed_clients(subscription_key, client_message),
                                self._main_loop
                            )
                    except Exception as e:
                        logger.error(f"Failed to schedule candle broadcast: {e}")

                logger.info(f"📈 Broadcasting 1m candle to all subscribed clients: {normalized_symbol}")

                return  # Don't process as regular message

            elif (data.get("type") in ["tick", "quote"] or
                  data.get("is_realtime") or
                  "current_price" in data or
                  "mid_price" in data):
                # Handle real-time tick/quote data for price updates
                price = data.get("current_price", data.get("mid_price", data.get("price", 0)))
                client_message = {
                    "type": "tick",
                    "symbol": normalized_symbol,  # Use normalized symbol
                    "price": price,
                    "timestamp": data.get("timestamp", 0),
                    "received_at": data.get("received_at")
                }
                logger.info(f"📊 Broadcasting tick data: {normalized_symbol} @ {price}")
            else:
                logger.info(f"📊 Unknown data format for {symbol}: {data}")
                return

            # Broadcast to subscribed clients
            # Use asyncio.run_coroutine_threadsafe since this might be called from a different thread
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(self._broadcast_to_subscribed_clients(subscription_key, client_message))
                else:
                    # If no loop is running, schedule it to run when the loop starts
                    asyncio.ensure_future(self._broadcast_to_subscribed_clients(subscription_key, client_message))
            except RuntimeError:
                # No event loop in current thread, try to get the main loop
                try:
                    import threading
                    if hasattr(self, '_main_loop') and self._main_loop:
                        asyncio.run_coroutine_threadsafe(
                            self._broadcast_to_subscribed_clients(subscription_key, client_message),
                            self._main_loop
                        )
                    else:
                        logger.error("No event loop available for broadcasting message")
                except Exception as e:
                    logger.error(f"Failed to schedule broadcast: {e}")

        except Exception as e:
            logger.error(f"Error handling Polygon message: {e}")
            logger.error(f"Data: {data}")

    async def _broadcast_to_subscribed_clients(self, subscription_key: str, message: Dict[str, Any]):
        """Broadcast message to clients subscribed to a specific forex pair."""
        if not self.clients:
            return

        message_json = json.dumps(message)
        disconnected_clients = set()

        for client in self.clients:
            if subscription_key in self.client_subscriptions.get(client, set()):
                try:
                    await client.send(message_json)
                except Exception as e:
                    logger.error(f"Error sending message to client: {e}")
                    disconnected_clients.add(client)

        # Clean up disconnected clients
        for client in disconnected_clients:
            await self._cleanup_client(client)

    async def _broadcast_to_subscribed_clients(self, subscription_key: str, message: Dict[str, Any]):
        """Broadcast message to ALL clients (trade-bots and frontend) subscribed to a forex pair."""
        if not self.clients:
            return

        message_json = json.dumps(message)
        disconnected_clients = set()
        trade_bot_count = 0
        frontend_count = 0

        for client in self.clients:
            if subscription_key in self.client_subscriptions.get(client, set()):
                try:
                    await client.send(message_json)

                    # Count client types for logging
                    if client in self.trade_bot_clients:
                        trade_bot_count += 1
                    else:
                        frontend_count += 1

                except Exception as e:
                    logger.error(f"Error sending candle message to client: {e}")
                    disconnected_clients.add(client)

        # Log broadcast summary
        if trade_bot_count > 0 or frontend_count > 0:
            logger.info(f"📡 Sent candle to {trade_bot_count} trade-bot(s) and {frontend_count} frontend client(s)")

        # Clean up disconnected clients
        for client in disconnected_clients:
            await self._cleanup_client(client)

    async def _broadcast_to_trade_bot_clients(self, subscription_key: str, message: Dict[str, Any]):
        """Broadcast message specifically to trade-bot clients subscribed to a forex pair."""
        if not self.trade_bot_clients:
            return

        message_json = json.dumps(message)
        disconnected_clients = set()

        for client in self.trade_bot_clients:
            if subscription_key in self.client_subscriptions.get(client, set()):
                try:
                    await client.send(message_json)
                    logger.info(f"🤖 Sent candle to trade-bot client")
                except Exception as e:
                    logger.error(f"Error sending message to trade-bot client: {e}")
                    disconnected_clients.add(client)

        # Clean up disconnected trade-bot clients
        for client in disconnected_clients:
            self.trade_bot_clients.discard(client)
            await self._cleanup_client(client)

    async def _restore_client_subscriptions(self, websocket):
        """Restore client subscriptions from production state (used on reconnection)."""
        try:
            if not self.state_manager:
                return

            client_info = self.client_info.get(websocket)
            if not client_info:
                return

            # For now, we'll implement a simple restoration
            # In a more advanced version, you could store user preferences
            logger.info(f"Restored subscriptions for client {client_info['user_id']}")

        except Exception as e:
            logger.error(f"Error restoring client subscriptions: {e}")

    async def _periodic_cleanup(self):
        """Perform periodic cleanup of unused resources."""
        now = datetime.now(timezone.utc)
        if (now - self.last_cleanup).total_seconds() < self.cleanup_interval:
            return

        self.last_cleanup = now
        logger.info("🧹 Performing periodic cleanup")

        # Clean up unused candle builders
        active_symbols = set()
        for subscription_key in self.subscription_counts:
            if self.subscription_counts[subscription_key] > 0:
                symbol = subscription_key.split('_')[0]
                active_symbols.add(symbol)

        # Remove builders for inactive symbols
        inactive_symbols = set(self.candle_builders.keys()) - active_symbols
        for symbol in inactive_symbols:
            del self.candle_builders[symbol]
            logger.info(f"🗑️ Cleaned up candle builders for inactive symbol: {symbol}")

        logger.info(f"🧹 Cleanup complete. Active symbols: {len(active_symbols)}, "
                   f"Active connections: {len(self.clients)}")

    def get_status(self) -> Dict[str, Any]:
        """Get current broadcaster status."""
        status = {
            "is_running": self.is_running,
            "connected_clients": len(self.clients),
            "active_subscriptions": dict(self.subscription_counts),
            "polygon_status": self.polygon_client.get_connection_status() if self.polygon_client else None,
            "use_production_state": self.use_production_state
        }

        # Add production state info if available
        if self.use_production_state and self.state_manager:
            status["instance_id"] = self.state_manager.instance_id

        return status


# Global broadcaster instance
_broadcaster = None

def get_candle_broadcaster(use_production_state: bool = None) -> CandleBroadcaster:
    """Get or create the global candle broadcaster instance."""
    global _broadcaster

    if _broadcaster is None:
        host = os.getenv("WEBSOCKET_HOST", "localhost")
        port = int(os.getenv("WEBSOCKET_PORT", "8765"))

        # Auto-detect production mode if not specified
        if use_production_state is None:
            use_production_state = os.getenv("ENVIRONMENT", "development") == "production"

        _broadcaster = CandleBroadcaster(host, port, use_production_state)

    return _broadcaster

async def start_realtime_service():
    """Start the real-time candle broadcasting service."""
    broadcaster = get_candle_broadcaster()
    await broadcaster.start_server()

if __name__ == "__main__":
    # Run the service directly
    asyncio.run(start_realtime_service())
