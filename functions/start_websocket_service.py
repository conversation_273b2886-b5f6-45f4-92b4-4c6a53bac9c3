#!/usr/bin/env python3
"""
Standalone script to start the WebSocket service for development and testing.
"""

import os
import sys
import asyncio
import signal
import logging
from datetime import datetime, timezone

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from realtime_candle_broadcaster import start_realtime_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebSocketServiceRunner:
    """Runner for the WebSocket service with proper shutdown handling."""
    
    def __init__(self):
        self.running = False
        self.server_task = None
    
    async def start(self):
        """Start the WebSocket service."""
        try:
            logger.info("🚀 Starting Oryn Real-time WebSocket Service")
            
            # Check environment
            self._check_environment()
            
            # Set up signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            # Start the service
            self.running = True
            logger.info("📡 WebSocket service is starting...")
            
            # Run the real-time service
            await start_realtime_service()
            
        except KeyboardInterrupt:
            logger.info("🛑 Received keyboard interrupt")
        except Exception as e:
            logger.error(f"❌ Error starting service: {e}")
            raise
        finally:
            await self.stop()
    
    async def stop(self):
        """Stop the WebSocket service."""
        if self.running:
            logger.info("🛑 Stopping WebSocket service...")
            self.running = False
            
            if self.server_task:
                self.server_task.cancel()
                try:
                    await self.server_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("✅ WebSocket service stopped")
    
    def _check_environment(self):
        """Check required environment variables."""
        required_vars = ["POLYGON_API_KEY"]
        missing_vars = []
        
        for var in required_vars:
            value = os.getenv(var)
            if value:
                logger.info(f"✅ {var}: {'*' * 10}...{value[-4:]}")
            else:
                missing_vars.append(var)
        
        if missing_vars:
            error_msg = f"Missing required environment variables: {', '.join(missing_vars)}"
            logger.error(f"❌ {error_msg}")
            raise ValueError(error_msg)
        
        logger.info("✅ Environment check passed")
    
    def _setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"🛑 Received signal {signum}")
            # Create a task to stop the service
            asyncio.create_task(self.stop())
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

def print_startup_info():
    """Print startup information."""
    print("=" * 60)
    print("🚀 Oryn Real-time WebSocket Service")
    print("=" * 60)
    print(f"📅 Started at: {datetime.now(timezone.utc).isoformat()}")
    print(f"🌐 WebSocket URL: ws://localhost:8765")
    print(f"📊 Data Source: Polygon.io")
    print("=" * 60)
    print("📝 Available endpoints:")
    print("   - Connection: ws://localhost:8765")
    print("   - Subscribe: Send JSON with type='subscribe', forex_pair='EURUSD'")
    print("   - Unsubscribe: Send JSON with type='unsubscribe', forex_pair='EURUSD'")
    print("   - Ping: Send JSON with type='ping'")
    print("=" * 60)
    print("🔧 Environment:")
    
    # Show environment info (safely)
    api_key = os.getenv("POLYGON_API_KEY")
    if api_key:
        print(f"   - Polygon API Key: {'*' * 10}...{api_key[-4:]}")
    else:
        print("   - Polygon API Key: ❌ NOT SET")
    
    print("=" * 60)
    print("💡 Press Ctrl+C to stop the service")
    print("=" * 60)

async def main():
    """Main entry point."""
    try:
        # Print startup info
        print_startup_info()
        
        # Create and start the service
        runner = WebSocketServiceRunner()
        await runner.start()
        
    except KeyboardInterrupt:
        logger.info("🛑 Service interrupted by user")
    except Exception as e:
        logger.error(f"❌ Service failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Service stopped by user")
    except Exception as e:
        print(f"\n❌ Service failed: {e}")
        sys.exit(1)
