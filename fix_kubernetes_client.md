## How to Fix the Kubernetes Configuration Error

When running the strategy controller locally with `./run_local.sh`, you're getting an error related to Kubernetes configuration. This happens because the strategy controller tries to initialize a Kubernetes client, but can't find a valid configuration.

Here are two ways to fix this:

### Option 1: Create a minimal kubeconfig file

1. Create a directory for Kubernetes configuration:
```bash
mkdir -p ~/.kube
```

2. Create a minimal kubeconfig file:
```bash
cat > ~/.kube/config << EOF
apiVersion: v1
kind: Config
clusters:
- cluster:
    server: https://localhost:8080
  name: local-cluster
contexts:
- context:
    cluster: local-cluster
    user: local-user
  name: local-context
current-context: local-context
users:
- name: local-user
  user:
    token: dummy-token
EOF
```

This creates a dummy kubeconfig file that will allow the Kubernetes client initialization to succeed, even though it won't actually connect to a real cluster. This is fine for local development when using `USE_LOCAL_TRADE_BOT=true`.

### Option 2: Modify the kubernetes_client.py file

If you don't want to create a kubeconfig file, you can modify the `kubernetes_client.py` file to handle the case when no Kubernetes configuration is available:

1. Open the file:
```bash
nano strategy-controller-service/kubernetes_client.py
```

2. Find the `__init__` method and modify it to handle the case when no Kubernetes configuration is available:

```python
def __init__(self):
    """
    Initialize the Kubernetes client.
    In cluster, this will use the service account.
    For local development, it will use kubectl config.
    For local development without kubectl config, it will use a dummy configuration.
    """
    use_local_trade_bot = os.getenv('USE_LOCAL_TRADE_BOT', 'false').lower() == 'true'
    
    try:
        # Try to load in-cluster config (when running in K8s)
        config.load_incluster_config()
        logging.info("Using in-cluster Kubernetes configuration")
    except config.ConfigException:
        try:
            # Fall back to local kubeconfig
            config.load_kube_config()
            logging.info("Using local Kubernetes configuration")
        except config.ConfigException:
            if use_local_trade_bot:
                # If we're using local trade bot, we don't need a real K8s connection
                logging.warning("No Kubernetes configuration found, but USE_LOCAL_TRADE_BOT=true, continuing with dummy configuration")
                # Set up dummy client configuration
                configuration = client.Configuration()
                configuration.host = "https://localhost:8080"
                configuration.verify_ssl = False
                configuration.api_key = {"authorization": "dummy-token"}
                client.Configuration.set_default(configuration)
            else:
                # If we're not using local trade bot, we need a real K8s connection
                logging.error("No Kubernetes configuration found and USE_LOCAL_TRADE_BOT=false, cannot continue")
                raise

    self.api = client.CoreV1Api()
    self.batch_api = client.BatchV1Api()

    # Get namespace or default to 'default'
    self.namespace = os.getenv("K8S_NAMESPACE", "default")
    logging.info(f"Using Kubernetes namespace: {self.namespace}")
```

This modification will allow the strategy controller to continue running even if no Kubernetes configuration is available, as long as `USE_LOCAL_TRADE_BOT=true`.

### Additional Requirements

Make sure the Firebase emulators are running before starting the strategy controller:

```bash
cd functions && npm run serve
```

This will start the Firebase emulators (Firestore on port 8082, PubSub on port 8085) that the strategy controller needs to function properly.
